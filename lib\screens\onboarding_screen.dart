import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/supabase_auth_service.dart';
import '../theme/app_theme.dart';

/// SCHERMATA DI ONBOARDING OBBLIGATORIA PER NUOVI UTENTI
/// Non ha pulsante indietro - l'utente deve completare il profilo
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  
  bool _isLoading = false;
  String _errorMessage = '';
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadUserData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  Future<void> _loadUserData() async {
    try {
      final authService = SupabaseAuthService();
      final user = authService.currentUser;
      
      if (user != null) {
        // Pre-popola con i dati di Google se disponibili
        final fullName = user.userMetadata?['full_name'] as String?;
        final email = user.email;
        
        if (fullName != null && fullName.isNotEmpty) {
          _nomeController.text = fullName;
        }
        
        if (email != null) {
          // Genera username suggerito dall'email
          final suggestedUsername = email.split('@')[0].toLowerCase();
          _usernameController.text = suggestedUsername;
        }
      }
    } catch (e) {
      print('⚠️ Errore caricamento dati utente: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nomeController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withOpacity(0.1),
              Colors.white,
              AppTheme.secondaryColor.withOpacity(0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Header di benvenuto
                    _buildWelcomeHeader(),
                    
                    const SizedBox(height: 32),
                    
                    // Form di completamento profilo
                    Expanded(
                      child: _buildProfileForm(),
                    ),
                    
                    // Pulsante di completamento
                    _buildCompleteButton(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Column(
      children: [
        // Logo/Icona
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            FontAwesomeIcons.heartPulse,
            color: Colors.white,
            size: 40,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Titolo di benvenuto
        Text(
          'Benvenuto in Dr. Staffilano! 🎉',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 12),
        
        // Sottotitolo
        Text(
          'Completa il tuo profilo per iniziare il tuo viaggio verso il benessere',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProfileForm() {
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Campo Nome
            _buildFormField(
              controller: _nomeController,
              label: 'Nome Visualizzato',
              hint: 'Come vuoi essere chiamato?',
              icon: FontAwesomeIcons.user,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Il nome è obbligatorio';
                }
                if (value.trim().length < 2) {
                  return 'Il nome deve avere almeno 2 caratteri';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 20),
            
            // Campo Username
            _buildFormField(
              controller: _usernameController,
              label: 'Username',
              hint: 'Il tuo identificativo unico',
              icon: FontAwesomeIcons.at,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'L\'username è obbligatorio';
                }
                if (value.trim().length < 3) {
                  return 'L\'username deve avere almeno 3 caratteri';
                }
                if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
                  return 'L\'username può contenere solo lettere, numeri e underscore';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 20),
            
            // Campo Bio (opzionale)
            _buildFormField(
              controller: _bioController,
              label: 'Bio (Opzionale)',
              hint: 'Raccontaci qualcosa di te...',
              icon: FontAwesomeIcons.penToSquare,
              maxLines: 3,
              isRequired: false,
            ),
            
            const SizedBox(height: 24),
            
            // Messaggio di errore
            if (_errorMessage.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red[700], fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(color: Colors.red[600]),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400]),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildCompleteButton() {
    return Column(
      children: [
        // Informazione sui passi successivi
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.primaryColor.withOpacity(0.2)),
          ),
          child: Row(
            children: [
              Icon(
                FontAwesomeIcons.lightbulb,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Dopo aver completato il profilo, potrai accedere a tutte le funzionalità dell\'app!',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Pulsante principale
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _completeProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 4,
              shadowColor: AppTheme.primaryColor.withOpacity(0.3),
            ),
            child: _isLoading
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(FontAwesomeIcons.rocket, size: 20),
                    const SizedBox(width: 12),
                    const Text(
                      'Inizia il Viaggio!',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
          ),
        ),
      ],
    );
  }

  Future<void> _completeProfile() async {
    print('🚀 ONBOARDING: Inizio completamento profilo...');

    // STEP 1: Validazione form
    if (!_formKey.currentState!.validate()) {
      print('❌ ONBOARDING: Validazione form fallita');
      return;
    }
    print('✅ ONBOARDING: Validazione form superata');

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final authService = SupabaseAuthService();

      final nome = _nomeController.text.trim();
      final username = _usernameController.text.trim();
      final bio = _bioController.text.trim().isEmpty ? null : _bioController.text.trim();

      print('📝 ONBOARDING: Dati da salvare - Nome: "$nome", Username: "$username", Bio: "$bio"');

      // STEP 2: Aggiorna il profilo con i dati inseriti
      print('💾 ONBOARDING: Chiamata updateUserProfile...');
      final success = await authService.updateUserProfile(
        nome: nome,
        username: username,
        bio: bio,
      );

      if (!success) {
        throw Exception('Aggiornamento profilo fallito');
      }

      print('✅ ONBOARDING: Profilo aggiornato con successo');

      // STEP 3: Verifica che il profilo sia ora completo
      print('🔍 ONBOARDING: Verifica completezza profilo...');
      final isComplete = await authService.isProfileComplete();
      print('📊 ONBOARDING: Profilo completo: $isComplete');

      // STEP 4: Successo! Naviga alla home con metodo robusto
      if (mounted) {
        print('🎉 ONBOARDING: Mostrando messaggio di successo...');

        // Mostra messaggio di successo
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.celebration, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text('Benvenuto, $nome! 🎉'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );

        // Usa il metodo robusto per la navigazione
        await _navigateAfterProfileUpdate();
      }

    } catch (e) {
      print('❌ ONBOARDING: Errore durante completamento: $e');
      setState(() {
        _errorMessage = 'Errore durante il salvataggio: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// Metodo robusto per la navigazione dopo l'aggiornamento del profilo
  Future<void> _navigateAfterProfileUpdate() async {
    print('🚀 NAVIGATION: Inizio navigazione post-aggiornamento...');

    // Mostra indicatore di caricamento
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final authService = SupabaseAuthService();

      // Delay per permettere la propagazione dei dati nel database
      print('⏳ NAVIGATION: Attesa propagazione dati database...');
      await Future.delayed(const Duration(milliseconds: 800));

      // Verifica che il profilo sia ora completo
      print('🔍 NAVIGATION: Verifica finale completezza profilo...');
      final isComplete = await authService.isProfileComplete();
      print('📊 NAVIGATION: Profilo completo: $isComplete');

      // Nascondi indicatore di caricamento
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (isComplete) {
        print('✅ NAVIGATION: Profilo completo, navigazione alla home...');

        if (mounted) {
          // Naviga alla home (sostituisce completamente lo stack)
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/',
            (route) => false,
          );
          print('🏠 NAVIGATION: Navigazione completata con successo');
        }
      } else {
        print('⚠️ NAVIGATION: Profilo ancora incompleto, rimango nell\'onboarding');

        if (mounted) {
          setState(() {
            _errorMessage = 'Errore durante il salvataggio. Riprova.';
            _isLoading = false;
          });
        }
      }

    } catch (e) {
      print('❌ NAVIGATION: Errore durante navigazione: $e');

      // Nascondi indicatore di caricamento in caso di errore
      if (mounted) {
        Navigator.of(context).pop();

        setState(() {
          _errorMessage = 'Errore di connessione. Riprova.';
          _isLoading = false;
        });
      }
    }
  }
}
