import 'package:uuid/uuid.dart';
import '../models/food.dart';
import 'food_database_service.dart';

/// Servizio per inizializzare il database con alimenti italiani
class ItalianFoodDatabaseInitializer {
  static const Uuid _uuid = Uuid();

  /// Inizializza il database con alimenti italiani
  static Future<void> initializeItalianFoods() async {
    final foodDatabase = await FoodDatabaseService.getInstance();

    // Verifica se il database è già stato inizializzato
    final existingFoods = foodDatabase.getAllFoods();
    if (existingFoods.isNotEmpty) {
      print('Database già inizializzato con ${existingFoods.length} alimenti');
      return;
    }

    print('Inizializzazione database con alimenti italiani...');

    // Lista di alimenti italiani con dati nutrizionali accurati (CREA/USDA)
    final italianFoods = [
      // FRUTTA
      Food(
        id: _uuid.v4(),
        name: '<PERSON><PERSON>',
        description: 'Mela dolce e croccante, varietà Fuji',
        calories: 52,
        proteins: 0.3,
        carbs: 13.8,
        fats: 0.2,
        fiber: 2.4,
        sugar: 10.4,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 6.0,
        potassium: 107.0,
        magnesium: 5.0,
        phosphorus: 11.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_c': 4.6,
          'vitamina_a': 3.0,
          'ferro': 0.12,
        },
        tags: ['frutta', 'dolce', 'croccante', 'italiana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Arancia Tarocco',
        description: 'Arancia rossa siciliana, ricca di vitamina C',
        calories: 47,
        proteins: 0.9,
        carbs: 11.7,
        fats: 0.1,
        fiber: 2.4,
        sugar: 9.4,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 40.0,
        potassium: 181.0,
        magnesium: 10.0,
        phosphorus: 14.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_c': 53.2,
          'vitamina_a': 11.0,
          'ferro': 0.1,
        },
        tags: ['frutta', 'agrumi', 'vitamina_c', 'siciliana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Banana',
        description: 'Banana matura, ricca di potassio',
        calories: 89,
        proteins: 1.1,
        carbs: 22.8,
        fats: 0.3,
        fiber: 2.6,
        sugar: 12.2,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 5.0,
        potassium: 358.0,
        magnesium: 27.0,
        phosphorus: 22.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_c': 8.7,
          'vitamina_b6': 0.4,
          'ferro': 0.26,
        },
        tags: ['frutta', 'tropicale', 'potassio', 'energia'],
        dataSource: DataSource.usda,
        imageUrl: '',
      ),

      // VERDURE
      Food(
        id: _uuid.v4(),
        name: 'Spinaci Freschi',
        description: 'Spinaci freschi italiani, ricchi di ferro',
        calories: 23,
        proteins: 2.9,
        carbs: 3.6,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 99.0,
        potassium: 558.0,
        magnesium: 79.0,
        phosphorus: 49.0,
        sodium: 79.0,
        micronutrients: {
          'ferro': 2.71,
          'vitamina_k': 482.9,
          'vitamina_a': 469.0,
          'acido_folico': 194.0,
        },
        tags: ['verdura', 'foglia_verde', 'ferro', 'italiana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Pomodoro San Marzano',
        description: 'Pomodoro San Marzano DOP, eccellenza campana',
        calories: 18,
        proteins: 0.9,
        carbs: 3.9,
        fats: 0.2,
        fiber: 1.2,
        sugar: 2.6,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 10.0,
        potassium: 237.0,
        magnesium: 11.0,
        phosphorus: 24.0,
        sodium: 5.0,
        micronutrients: {
          'vitamina_c': 13.7,
          'licopene': 2573.0,
          'vitamina_a': 42.0,
        },
        tags: ['verdura', 'pomodoro', 'dop', 'campana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      // CEREALI E PASTA
      Food(
        id: _uuid.v4(),
        name: 'Pasta di Grano Duro',
        description: 'Pasta italiana di grano duro, cruda',
        calories: 371,
        proteins: 13.0,
        carbs: 74.7,
        fats: 1.5,
        fiber: 3.2,
        sugar: 2.7,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        isDairyFree: true,
        calcium: 21.0,
        potassium: 223.0,
        magnesium: 53.0,
        phosphorus: 189.0,
        sodium: 6.0,
        micronutrients: {
          'ferro': 1.28,
          'vitamina_b1': 0.09,
          'niacina': 1.7,
        },
        tags: ['cereali', 'pasta', 'grano_duro', 'italiana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Riso Carnaroli',
        description: 'Riso Carnaroli per risotto, crudo',
        calories: 330,
        proteins: 7.4,
        carbs: 77.3,
        fats: 0.4,
        fiber: 1.4,
        sugar: 0.1,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 3.0,
        potassium: 76.0,
        magnesium: 23.0,
        phosphorus: 94.0,
        sodium: 2.0,
        micronutrients: {
          'ferro': 0.8,
          'vitamina_b1': 0.07,
          'niacina': 1.6,
        },
        tags: ['cereali', 'riso', 'risotto', 'italiana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      // PROTEINE
      Food(
        id: _uuid.v4(),
        name: 'Petto di Pollo',
        description: 'Petto di pollo senza pelle, crudo',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 15.0,
        potassium: 256.0,
        magnesium: 29.0,
        phosphorus: 228.0,
        sodium: 74.0,
        micronutrients: {
          'ferro': 0.7,
          'vitamina_b3': 10.9,
          'vitamina_b6': 0.6,
        },
        tags: ['carne', 'pollo', 'proteine', 'magro'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Salmone Atlantico',
        description: 'Salmone atlantico di allevamento, crudo',
        calories: 208,
        proteins: 25.4,
        carbs: 0.0,
        fats: 11.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 9.0,
        potassium: 384.0,
        magnesium: 30.0,
        phosphorus: 252.0,
        sodium: 59.0,
        micronutrients: {
          'omega_3': 2.3,
          'vitamina_d': 11.0,
          'vitamina_b12': 3.2,
        },
        tags: ['pesce', 'salmone', 'omega_3', 'proteine'],
        dataSource: DataSource.usda,
        imageUrl: '',
      ),

      // LATTICINI
      Food(
        id: _uuid.v4(),
        name: 'Parmigiano Reggiano DOP',
        description: 'Parmigiano Reggiano DOP stagionato 24 mesi',
        calories: 392,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.1,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.dairy],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        calcium: 1159.0,
        potassium: 125.0,
        magnesium: 43.0,
        phosphorus: 694.0,
        sodium: 1529.0,
        micronutrients: {
          'vitamina_a': 270.0,
          'vitamina_b12': 2.7,
          'zinco': 2.75,
        },
        tags: ['formaggio', 'dop', 'stagionato', 'italiana'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      Food(
        id: _uuid.v4(),
        name: 'Mozzarella di Bufala',
        description: 'Mozzarella di bufala campana DOP',
        calories: 288,
        proteins: 16.7,
        carbs: 1.0,
        fats: 24.4,
        fiber: 0.0,
        sugar: 1.0,
        categories: [FoodCategory.dairy],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        calcium: 354.0,
        potassium: 95.0,
        magnesium: 20.0,
        phosphorus: 270.0,
        sodium: 16.0,
        micronutrients: {
          'vitamina_a': 162.0,
          'vitamina_b2': 0.28,
          'zinco': 2.92,
        },
        tags: ['formaggio', 'mozzarella', 'bufala', 'dop'],
        dataSource: DataSource.crea,
        imageUrl: '',
      ),

      // VERDURE E ORTAGGI
      Food(
        id: _uuid.v4(),
        name: 'Pomodori San Marzano',
        description: 'Pomodori San Marzano DOP, freschi',
        calories: 18,
        proteins: 0.9,
        carbs: 3.5,
        fats: 0.2,
        fiber: 1.4,
        sugar: 2.6,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 10.0,
        potassium: 237.0,
        magnesium: 11.0,
        phosphorus: 24.0,
        sodium: 5.0,
        micronutrients: {
          'vitamina_c': 13.7,
          'vitamina_a': 42.0,
          'licopene': 2573.0,
          'folati': 15.0,
        },
        tags: ['pomodori', 'san marzano', 'dop', 'campania'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.campania],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Basilico Genovese',
        description: 'Basilico Genovese DOP, foglie fresche',
        calories: 22,
        proteins: 3.2,
        carbs: 2.6,
        fats: 0.6,
        fiber: 1.6,
        sugar: 0.3,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 177.0,
        potassium: 295.0,
        magnesium: 64.0,
        phosphorus: 56.0,
        sodium: 4.0,
        micronutrients: {
          'vitamina_k': 414.8,
          'vitamina_a': 264.0,
          'ferro': 3.17,
          'manganese': 1.15,
        },
        tags: ['basilico', 'genovese', 'dop', 'liguria', 'erbe'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.liguria],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Melanzane',
        description: 'Melanzane fresche, crude',
        calories: 25,
        proteins: 1.0,
        carbs: 6.0,
        fats: 0.2,
        fiber: 3.0,
        sugar: 3.5,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 9.0,
        potassium: 229.0,
        magnesium: 14.0,
        phosphorus: 24.0,
        sodium: 2.0,
        micronutrients: {
          'vitamina_c': 2.2,
          'folati': 22.0,
          'vitamina_k': 3.5,
          'nasunina': 750.0,
        },
        tags: ['melanzane', 'verdura', 'mediterranea'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Zucchine',
        description: 'Zucchine fresche, crude',
        calories: 17,
        proteins: 1.2,
        carbs: 3.1,
        fats: 0.3,
        fiber: 1.0,
        sugar: 2.5,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 16.0,
        potassium: 261.0,
        magnesium: 18.0,
        phosphorus: 38.0,
        sodium: 8.0,
        micronutrients: {
          'vitamina_c': 17.9,
          'vitamina_a': 10.0,
          'folati': 24.0,
          'vitamina_k': 4.3,
        },
        tags: ['zucchine', 'verdura', 'estiva'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [5, 6, 7, 8, 9],
      ),

      Food(
        id: _uuid.v4(),
        name: 'Peperoni',
        description: 'Peperoni dolci, freschi',
        calories: 31,
        proteins: 1.0,
        carbs: 6.0,
        fats: 0.3,
        fiber: 2.5,
        sugar: 4.2,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 7.0,
        potassium: 211.0,
        magnesium: 12.0,
        phosphorus: 26.0,
        sodium: 4.0,
        micronutrients: {
          'vitamina_c': 127.7,
          'vitamina_a': 157.0,
          'vitamina_b6': 0.29,
          'folati': 46.0,
        },
        tags: ['peperoni', 'verdura', 'dolci'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [6, 7, 8, 9],
      ),

      // CARNI E PESCE
      Food(
        id: _uuid.v4(),
        name: 'Prosciutto di Parma',
        description: 'Prosciutto di Parma DOP, stagionato 18 mesi',
        calories: 268,
        proteins: 25.0,
        carbs: 0.0,
        fats: 18.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 10.0,
        potassium: 320.0,
        magnesium: 22.0,
        phosphorus: 200.0,
        sodium: 2340.0,
        micronutrients: {
          'vitamina_b1': 0.54,
          'vitamina_b12': 0.7,
          'ferro': 1.0,
          'zinco': 2.3,
        },
        tags: ['prosciutto', 'parma', 'dop', 'salumi'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.emiliaRomagna],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Bresaola della Valtellina',
        description: 'Bresaola della Valtellina IGP',
        calories: 151,
        proteins: 32.0,
        carbs: 0.6,
        fats: 2.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 15.0,
        potassium: 505.0,
        magnesium: 35.0,
        phosphorus: 280.0,
        sodium: 1200.0,
        micronutrients: {
          'ferro': 3.9,
          'zinco': 4.6,
          'vitamina_b12': 2.8,
          'niacina': 13.8,
        },
        tags: ['bresaola', 'valtellina', 'igp', 'salumi'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.lombardia],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Tonno Siciliano',
        description: 'Tonno rosso del Mediterraneo, fresco',
        calories: 144,
        proteins: 25.4,
        carbs: 0.0,
        fats: 4.9,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 8.0,
        potassium: 444.0,
        magnesium: 50.0,
        phosphorus: 254.0,
        sodium: 39.0,
        micronutrients: {
          'omega_3': 1298.0,
          'vitamina_b12': 9.43,
          'niacina': 8.65,
          'selenio': 36.5,
        },
        tags: ['tonno', 'pesce', 'mediterraneo', 'sicilia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Branzino',
        description: 'Branzino del Mediterraneo, fresco',
        calories: 82,
        proteins: 16.5,
        carbs: 0.6,
        fats: 1.5,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 12.0,
        potassium: 256.0,
        magnesium: 45.0,
        phosphorus: 203.0,
        sodium: 120.0,
        micronutrients: {
          'omega_3': 595.0,
          'vitamina_b12': 1.2,
          'selenio': 12.6,
          'iodio': 15.0,
        },
        tags: ['branzino', 'pesce', 'mediterraneo'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
      ),

      // CEREALI E PASTA
      Food(
        id: _uuid.v4(),
        name: 'Pasta di Gragnano',
        description: 'Pasta di Gragnano IGP, secca',
        calories: 371,
        proteins: 13.0,
        carbs: 74.7,
        fats: 1.5,
        fiber: 3.2,
        sugar: 2.7,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        isDairyFree: true,
        allergens: ['glutine'],
        calcium: 21.0,
        potassium: 223.0,
        magnesium: 53.0,
        phosphorus: 189.0,
        sodium: 6.0,
        micronutrients: {
          'ferro': 1.8,
          'vitamina_b1': 0.16,
          'niacina': 2.9,
          'folati': 18.0,
        },
        tags: ['pasta', 'gragnano', 'igp', 'campania'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.campania],
        isTraditionalItalian: true,
        rawToCookedFactor: 2.5,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Farro Perlato',
        description: 'Farro perlato della Garfagnana IGP',
        calories: 335,
        proteins: 15.1,
        carbs: 67.1,
        fats: 2.5,
        fiber: 6.8,
        sugar: 2.7,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        isDairyFree: true,
        allergens: ['glutine'],
        calcium: 43.0,
        potassium: 440.0,
        magnesium: 136.0,
        phosphorus: 420.0,
        sodium: 8.0,
        micronutrients: {
          'ferro': 4.2,
          'zinco': 3.7,
          'vitamina_b1': 0.42,
          'niacina': 7.2,
        },
        tags: ['farro', 'garfagnana', 'igp', 'toscana'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.toscana],
        isTraditionalItalian: true,
        rawToCookedFactor: 2.8,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Polenta di Mais',
        description: 'Farina di mais per polenta, cruda',
        calories: 362,
        proteins: 8.7,
        carbs: 79.8,
        fats: 2.7,
        fiber: 2.0,
        sugar: 1.3,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 7.0,
        potassium: 287.0,
        magnesium: 127.0,
        phosphorus: 272.0,
        sodium: 35.0,
        micronutrients: {
          'ferro': 2.4,
          'vitamina_a': 214.0,
          'vitamina_b1': 0.39,
          'niacina': 3.6,
        },
        tags: ['polenta', 'mais', 'nord italia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.lombardia, ItalianRegion.veneto],
        isTraditionalItalian: true,
        rawToCookedFactor: 4.0,
      ),

      // LEGUMI
      Food(
        id: _uuid.v4(),
        name: 'Fagioli Cannellini',
        description: 'Fagioli cannellini secchi, crudi',
        calories: 316,
        proteins: 23.4,
        carbs: 45.5,
        fats: 2.0,
        fiber: 17.6,
        sugar: 2.4,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 142.0,
        potassium: 1795.0,
        magnesium: 190.0,
        phosphorus: 407.0,
        sodium: 16.0,
        micronutrients: {
          'ferro': 10.4,
          'zinco': 3.7,
          'folati': 388.0,
          'vitamina_b1': 0.5,
        },
        tags: ['fagioli', 'cannellini', 'legumi', 'toscana'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.toscana],
        isTraditionalItalian: true,
        rawToCookedFactor: 2.5,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Lenticchie di Castelluccio',
        description: 'Lenticchie di Castelluccio IGP, secche',
        calories: 291,
        proteins: 22.7,
        carbs: 51.1,
        fats: 2.5,
        fiber: 13.8,
        sugar: 2.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 57.0,
        potassium: 980.0,
        magnesium: 122.0,
        phosphorus: 451.0,
        sodium: 6.0,
        micronutrients: {
          'ferro': 8.0,
          'zinco': 3.1,
          'folati': 479.0,
          'vitamina_b1': 0.5,
        },
        tags: ['lenticchie', 'castelluccio', 'igp', 'umbria'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.umbria],
        isTraditionalItalian: true,
        rawToCookedFactor: 2.3,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Ceci',
        description: 'Ceci secchi, crudi',
        calories: 316,
        proteins: 20.9,
        carbs: 46.9,
        fats: 6.0,
        fiber: 13.6,
        sugar: 10.7,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 142.0,
        potassium: 968.0,
        magnesium: 79.0,
        phosphorus: 444.0,
        sodium: 26.0,
        micronutrients: {
          'ferro': 4.3,
          'zinco': 2.8,
          'folati': 557.0,
          'vitamina_b6': 0.5,
        },
        tags: ['ceci', 'legumi', 'mediterranei'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        rawToCookedFactor: 2.4,
      ),

      // FRUTTA ITALIANA
      Food(
        id: _uuid.v4(),
        name: 'Arance di Sicilia',
        description: 'Arance rosse di Sicilia IGP',
        calories: 34,
        proteins: 0.7,
        carbs: 7.8,
        fats: 0.2,
        fiber: 2.0,
        sugar: 6.8,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 49.0,
        potassium: 200.0,
        magnesium: 10.0,
        phosphorus: 22.0,
        sodium: 3.0,
        micronutrients: {
          'vitamina_c': 50.0,
          'vitamina_a': 11.0,
          'folati': 20.0,
          'antocianine': 200.0,
        },
        tags: ['arance', 'sicilia', 'igp', 'agrumi'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [12, 1, 2, 3, 4],
      ),

      Food(
        id: _uuid.v4(),
        name: 'Limoni di Sorrento',
        description: 'Limoni di Sorrento IGP',
        calories: 11,
        proteins: 0.6,
        carbs: 1.6,
        fats: 0.2,
        fiber: 4.7,
        sugar: 1.5,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 6.0,
        potassium: 80.0,
        magnesium: 6.0,
        phosphorus: 8.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_c': 51.0,
          'limonene': 9000.0,
          'citrale': 2000.0,
          'folati': 9.0,
        },
        tags: ['limoni', 'sorrento', 'igp', 'campania'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.campania],
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [10, 11, 12, 1, 2, 3],
      ),

      Food(
        id: _uuid.v4(),
        name: 'Fichi d\'India',
        description: 'Fichi d\'India siciliani, freschi',
        calories: 55,
        proteins: 0.8,
        carbs: 13.0,
        fats: 0.1,
        fiber: 5.0,
        sugar: 9.6,
        categories: [FoodCategory.fruit],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 56.0,
        potassium: 220.0,
        magnesium: 85.0,
        phosphorus: 24.0,
        sodium: 5.0,
        micronutrients: {
          'vitamina_c': 14.0,
          'vitamina_a': 43.0,
          'betalaine': 300.0,
          'taurina': 28.0,
        },
        tags: ['fichi d\'india', 'sicilia', 'cactus'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [8, 9, 10],
      ),

      // FORMAGGI E LATTICINI
      Food(
        id: _uuid.v4(),
        name: 'Parmigiano Reggiano',
        description: 'Parmigiano Reggiano DOP, stagionato 24 mesi',
        calories: 392,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.dairy, FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        allergens: ['latticini'],
        calcium: 1184.0,
        potassium: 102.0,
        magnesium: 43.0,
        phosphorus: 694.0,
        sodium: 1529.0,
        micronutrients: {
          'vitamina_a': 270.0,
          'vitamina_b12': 1.4,
          'zinco': 4.0,
          'selenio': 22.0,
        },
        tags: ['parmigiano', 'reggiano', 'dop', 'emilia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.emiliaRomagna],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Gorgonzola DOP',
        description: 'Gorgonzola DOP dolce',
        calories: 330,
        proteins: 19.4,
        carbs: 0.0,
        fats: 27.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.dairy, FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        allergens: ['latticini'],
        calcium: 612.0,
        potassium: 256.0,
        magnesium: 18.0,
        phosphorus: 347.0,
        sodium: 1395.0,
        micronutrients: {
          'vitamina_a': 294.0,
          'vitamina_b2': 0.38,
          'vitamina_b12': 1.2,
          'zinco': 2.8,
        },
        tags: ['gorgonzola', 'dop', 'lombardia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.lombardia],
        isTraditionalItalian: true,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Ricotta di Pecora',
        description: 'Ricotta di pecora siciliana, fresca',
        calories: 146,
        proteins: 11.4,
        carbs: 3.0,
        fats: 10.0,
        fiber: 0.0,
        sugar: 3.0,
        categories: [FoodCategory.dairy, FoodCategory.protein],
        suitableForMeals: [MealType.breakfast, MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        allergens: ['latticini'],
        calcium: 272.0,
        potassium: 105.0,
        magnesium: 11.0,
        phosphorus: 158.0,
        sodium: 84.0,
        micronutrients: {
          'vitamina_a': 120.0,
          'vitamina_b2': 0.24,
          'vitamina_b12': 0.34,
          'selenio': 14.5,
        },
        tags: ['ricotta', 'pecora', 'sicilia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
      ),

      // OLIO E CONDIMENTI
      Food(
        id: _uuid.v4(),
        name: 'Olio Extravergine Toscano',
        description: 'Olio extravergine di oliva Toscano IGP',
        calories: 884,
        proteins: 0.0,
        carbs: 0.0,
        fats: 100.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.fat],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 1.0,
        potassium: 1.0,
        magnesium: 0.0,
        phosphorus: 0.0,
        sodium: 2.0,
        micronutrients: {
          'vitamina_e': 14.35,
          'vitamina_k': 60.2,
          'polifenoli': 250.0,
          'oleocantale': 85.0,
        },
        tags: ['olio', 'extravergine', 'toscano', 'igp'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.toscana],
        isTraditionalItalian: true,
        servingSize: '1 cucchiaio (10ml)',
        servingSizeGrams: 10,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Aceto Balsamico di Modena',
        description: 'Aceto Balsamico di Modena IGP',
        calories: 88,
        proteins: 0.5,
        carbs: 17.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 14.9,
        categories: [FoodCategory.condiment],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 27.0,
        potassium: 112.0,
        magnesium: 4.0,
        phosphorus: 19.0,
        sodium: 23.0,
        micronutrients: {
          'acido_acetico': 6000.0,
          'polifenoli': 180.0,
          'antocianine': 45.0,
        },
        tags: ['aceto', 'balsamico', 'modena', 'igp'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.emiliaRomagna],
        isTraditionalItalian: true,
        servingSize: '1 cucchiaio (15ml)',
        servingSizeGrams: 15,
      ),

      // FRUTTA SECCA E NOCI
      Food(
        id: _uuid.v4(),
        name: 'Nocciole del Piemonte',
        description: 'Nocciole del Piemonte IGP, sgusciate',
        calories: 628,
        proteins: 14.9,
        carbs: 16.7,
        fats: 60.8,
        fiber: 9.7,
        sugar: 4.3,
        categories: [FoodCategory.protein, FoodCategory.fat],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['frutta a guscio'],
        calcium: 114.0,
        potassium: 680.0,
        magnesium: 163.0,
        phosphorus: 290.0,
        sodium: 0.0,
        micronutrients: {
          'vitamina_e': 15.0,
          'vitamina_b6': 0.56,
          'folati': 113.0,
          'manganese': 4.9,
        },
        tags: ['nocciole', 'piemonte', 'igp', 'frutta secca'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.piemonte],
        isTraditionalItalian: true,
        servingSize: '30g (1 porzione)',
        servingSizeGrams: 30,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Pinoli',
        description: 'Pinoli italiani, sgusciati',
        calories: 673,
        proteins: 13.7,
        carbs: 13.1,
        fats: 68.4,
        fiber: 3.7,
        sugar: 3.6,
        categories: [FoodCategory.protein, FoodCategory.fat],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['frutta a guscio'],
        calcium: 16.0,
        potassium: 597.0,
        magnesium: 251.0,
        phosphorus: 575.0,
        sodium: 2.0,
        micronutrients: {
          'vitamina_e': 9.33,
          'vitamina_k': 53.9,
          'ferro': 5.53,
          'zinco': 6.45,
        },
        tags: ['pinoli', 'frutta secca', 'mediterranei'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        servingSize: '20g (1 porzione)',
        servingSizeGrams: 20,
      ),

      // ERBE AROMATICHE
      Food(
        id: _uuid.v4(),
        name: 'Rosmarino',
        description: 'Rosmarino fresco, foglie',
        calories: 131,
        proteins: 3.3,
        carbs: 20.7,
        fats: 5.9,
        fiber: 14.1,
        sugar: 0.0,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 317.0,
        potassium: 668.0,
        magnesium: 91.0,
        phosphorus: 66.0,
        sodium: 26.0,
        micronutrients: {
          'vitamina_c': 21.8,
          'vitamina_a': 146.0,
          'ferro': 6.65,
          'acido_rosmarinico': 3000.0,
        },
        tags: ['rosmarino', 'erbe', 'aromatiche', 'mediterranee'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        servingSize: '1g (1 rametto)',
        servingSizeGrams: 1,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Origano',
        description: 'Origano siciliano, secco',
        calories: 265,
        proteins: 9.0,
        carbs: 68.9,
        fats: 4.3,
        fiber: 42.5,
        sugar: 4.1,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 1597.0,
        potassium: 1260.0,
        magnesium: 270.0,
        phosphorus: 148.0,
        sodium: 25.0,
        micronutrients: {
          'vitamina_k': 621.7,
          'vitamina_e': 18.26,
          'ferro': 36.8,
          'carvacrolo': 4000.0,
        },
        tags: ['origano', 'sicilia', 'erbe', 'secche'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
        servingSize: '1g (1 pizzico)',
        servingSizeGrams: 1,
      ),

      // PESCE CONSERVATO
      Food(
        id: _uuid.v4(),
        name: 'Acciughe di Cetara',
        description: 'Acciughe di Cetara IGP, sotto sale',
        calories: 210,
        proteins: 25.9,
        carbs: 0.0,
        fats: 11.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['pesce'],
        calcium: 147.0,
        potassium: 383.0,
        magnesium: 41.0,
        phosphorus: 174.0,
        sodium: 3668.0,
        micronutrients: {
          'omega_3': 1478.0,
          'vitamina_b12': 11.0,
          'niacina': 14.2,
          'selenio': 68.1,
        },
        tags: ['acciughe', 'cetara', 'igp', 'campania'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.campania],
        isTraditionalItalian: true,
        servingSize: '10g (2-3 acciughe)',
        servingSizeGrams: 10,
      ),

      // VERDURE TRADIZIONALI
      Food(
        id: _uuid.v4(),
        name: 'Carciofi Romaneschi',
        description: 'Carciofi romaneschi IGP, freschi',
        calories: 47,
        proteins: 3.3,
        carbs: 10.5,
        fats: 0.2,
        fiber: 5.4,
        sugar: 1.0,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 44.0,
        potassium: 370.0,
        magnesium: 60.0,
        phosphorus: 90.0,
        sodium: 94.0,
        micronutrients: {
          'vitamina_c': 11.7,
          'vitamina_k': 14.8,
          'folati': 68.0,
          'cinarina': 15.0,
        },
        tags: ['carciofi', 'romaneschi', 'igp', 'lazio'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.lazio],
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [2, 3, 4, 5],
      ),

      Food(
        id: _uuid.v4(),
        name: 'Radicchio di Treviso',
        description: 'Radicchio di Treviso IGP, tardivo',
        calories: 23,
        proteins: 1.4,
        carbs: 4.5,
        fats: 0.3,
        fiber: 3.0,
        sugar: 0.6,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 36.0,
        potassium: 302.0,
        magnesium: 13.0,
        phosphorus: 40.0,
        sodium: 22.0,
        micronutrients: {
          'vitamina_k': 255.2,
          'vitamina_c': 8.0,
          'antocianine': 180.0,
          'inulina': 680.0,
        },
        tags: ['radicchio', 'treviso', 'igp', 'veneto'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.veneto],
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [11, 12, 1, 2, 3],
      ),

      Food(
        id: _uuid.v4(),
        name: 'Finocchi',
        description: 'Finocchi freschi, crudi',
        calories: 31,
        proteins: 1.2,
        carbs: 7.3,
        fats: 0.2,
        fiber: 3.1,
        sugar: 3.9,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 49.0,
        potassium: 414.0,
        magnesium: 15.0,
        phosphorus: 50.0,
        sodium: 52.0,
        micronutrients: {
          'vitamina_c': 12.0,
          'folati': 27.0,
          'anetolo': 50.0,
          'vitamina_k': 62.8,
        },
        tags: ['finocchi', 'verdura', 'mediterranea'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [10, 11, 12, 1, 2, 3],
      ),

      // PREPARAZIONI TRADIZIONALI
      Food(
        id: _uuid.v4(),
        name: 'Pesto Genovese',
        description: 'Pesto Genovese DOP, tradizionale',
        calories: 418,
        proteins: 5.1,
        carbs: 5.1,
        fats: 42.2,
        fiber: 0.9,
        sugar: 1.6,
        categories: [FoodCategory.condiment, FoodCategory.fat],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: false,
        allergens: ['latticini', 'frutta a guscio'],
        calcium: 184.0,
        potassium: 105.0,
        magnesium: 71.0,
        phosphorus: 153.0,
        sodium: 1040.0,
        micronutrients: {
          'vitamina_e': 5.07,
          'vitamina_k': 257.0,
          'ferro': 1.6,
          'zinco': 1.1,
        },
        tags: ['pesto', 'genovese', 'dop', 'liguria'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.liguria],
        isTraditionalItalian: true,
        servingSize: '20g (1 porzione)',
        servingSizeGrams: 20,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Sugo di Pomodoro',
        description: 'Sugo di pomodoro fatto in casa',
        calories: 29,
        proteins: 1.6,
        carbs: 7.0,
        fats: 0.2,
        fiber: 1.4,
        sugar: 4.2,
        categories: [FoodCategory.vegetable, FoodCategory.condiment],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 18.0,
        potassium: 297.0,
        magnesium: 14.0,
        phosphorus: 30.0,
        sodium: 9.0,
        micronutrients: {
          'vitamina_c': 9.7,
          'vitamina_a': 25.0,
          'licopene': 2900.0,
          'vitamina_k': 4.1,
        },
        tags: ['sugo', 'pomodoro', 'fatto in casa'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        servingSize: '80g (1 porzione)',
        servingSizeGrams: 80,
      ),

      // DOLCI TRADIZIONALI
      Food(
        id: _uuid.v4(),
        name: 'Cannoli Siciliani',
        description: 'Cannoli siciliani con ricotta, tradizionali',
        calories: 379,
        proteins: 9.0,
        carbs: 46.0,
        fats: 18.0,
        fiber: 1.5,
        sugar: 25.0,
        categories: [FoodCategory.sweet],
        suitableForMeals: [MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isDairyFree: false,
        allergens: ['glutine', 'latticini', 'uova'],
        calcium: 147.0,
        potassium: 126.0,
        magnesium: 22.0,
        phosphorus: 133.0,
        sodium: 337.0,
        micronutrients: {
          'vitamina_a': 89.0,
          'vitamina_b2': 0.15,
          'ferro': 1.4,
          'zinco': 0.8,
        },
        tags: ['cannoli', 'sicilia', 'dolci', 'ricotta'],
        dataSource: DataSource.crea,
        imageUrl: '',
        italianRegions: [ItalianRegion.sicilia],
        isTraditionalItalian: true,
        servingSize: '100g (1 cannolo)',
        servingSizeGrams: 100,
      ),

      // BEVANDE TRADIZIONALI
      Food(
        id: _uuid.v4(),
        name: 'Caffè Espresso',
        description: 'Caffè espresso italiano, 1 tazzina',
        calories: 9,
        proteins: 0.1,
        carbs: 1.7,
        fats: 0.2,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.beverage],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 2.0,
        potassium: 49.0,
        magnesium: 3.0,
        phosphorus: 3.0,
        sodium: 14.0,
        micronutrients: {
          'caffeina': 212.0,
          'acido_clorogenico': 70.0,
          'niacina': 0.5,
          'riboflavina': 0.01,
        },
        tags: ['caffè', 'espresso', 'italiano'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        servingSize: '30ml (1 tazzina)',
        servingSizeGrams: 30,
      ),

      // ALTRI CEREALI
      Food(
        id: _uuid.v4(),
        name: 'Orzo Perlato',
        description: 'Orzo perlato, crudo',
        calories: 354,
        proteins: 10.4,
        carbs: 73.5,
        fats: 1.2,
        fiber: 9.2,
        sugar: 0.8,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        isDairyFree: true,
        allergens: ['glutine'],
        calcium: 11.0,
        potassium: 280.0,
        magnesium: 79.0,
        phosphorus: 221.0,
        sodium: 9.0,
        micronutrients: {
          'ferro': 2.5,
          'zinco': 2.1,
          'vitamina_b1': 0.19,
          'niacina': 4.6,
        },
        tags: ['orzo', 'cereali', 'perlato'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        rawToCookedFactor: 3.0,
      ),

      // VERDURE A FOGLIA
      Food(
        id: _uuid.v4(),
        name: 'Rucola',
        description: 'Rucola fresca, foglie',
        calories: 25,
        proteins: 2.6,
        carbs: 3.7,
        fats: 0.7,
        fiber: 1.6,
        sugar: 2.0,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 160.0,
        potassium: 369.0,
        magnesium: 47.0,
        phosphorus: 52.0,
        sodium: 27.0,
        micronutrients: {
          'vitamina_k': 108.6,
          'vitamina_c': 15.0,
          'vitamina_a': 119.0,
          'folati': 97.0,
        },
        tags: ['rucola', 'insalata', 'piccante'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        servingSize: '50g (1 porzione)',
        servingSizeGrams: 50,
      ),

      Food(
        id: _uuid.v4(),
        name: 'Bietole',
        description: 'Bietole da coste, fresche',
        calories: 19,
        proteins: 1.8,
        carbs: 3.7,
        fats: 0.2,
        fiber: 1.6,
        sugar: 1.1,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        calcium: 51.0,
        potassium: 379.0,
        magnesium: 81.0,
        phosphorus: 46.0,
        sodium: 213.0,
        micronutrients: {
          'vitamina_k': 830.0,
          'vitamina_a': 306.0,
          'vitamina_c': 30.0,
          'ferro': 1.8,
        },
        tags: ['bietole', 'coste', 'verdura a foglia'],
        dataSource: DataSource.crea,
        imageUrl: '',
        isTraditionalItalian: true,
        isSeasonal: true,
        seasonalMonths: [9, 10, 11, 12, 1, 2],
      ),
    ];

    // Salva tutti gli alimenti nel database
    for (final food in italianFoods) {
      await foodDatabase.saveFood(food);
    }

    print('Database inizializzato con ${italianFoods.length} alimenti italiani');
  }
}
