import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';
import '../services/translation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Classe che rappresenta i target nutrizionali per un singolo pasto
class MealTarget {
  final int calories;
  final int proteins;
  final int carbs;
  final int fats;

  MealTarget({
    required this.calories,
    required this.proteins,
    required this.carbs,
    required this.fats,
  });

  @override
  String toString() {
    return 'MealTarget(calories: $calories, proteins: $proteins, carbs: $carbs, fats: $fats)';
  }

  /// Crea una copia con valori modificati
  MealTarget copyWith({
    int? calories,
    int? proteins,
    int? carbs,
    int? fats,
  }) {
    return MealTarget(
      calories: calories ?? this.calories,
      proteins: proteins ?? this.proteins,
      carbs: carbs ?? this.carbs,
      fats: fats ?? this.fats,
    );
  }

  /// Calcola la deviazione percentuale rispetto a un altro target
  Map<String, double> calculateDeviationPercent(MealTarget other) {
    return {
      'calories': other.calories > 0 ? (calories - other.calories) / other.calories : 0.0,
      'proteins': other.proteins > 0 ? (proteins - other.proteins) / other.proteins : 0.0,
      'carbs': other.carbs > 0 ? (carbs - other.carbs) / other.carbs : 0.0,
      'fats': other.fats > 0 ? (fats - other.fats) / other.fats : 0.0,
    };
  }

  /// Calcola la deviazione assoluta rispetto a un altro target
  Map<String, int> calculateDeviationAbsolute(MealTarget other) {
    return {
      'calories': calories - other.calories,
      'proteins': proteins - other.proteins,
      'carbs': carbs - other.carbs,
      'fats': fats - other.fats,
    };
  }
}

/// Classe che rappresenta una soluzione candidata per un pasto
class MealSolution {
  final List<Food> foods;
  final List<int> grammages;
  final MealTarget actualNutrition;
  final double errorScore;

  MealSolution({
    required this.foods,
    required this.grammages,
    required this.actualNutrition,
    required this.errorScore,
  });

  /// Calcola l'errore totale ponderato rispetto a un target
  static double calculateErrorScore(MealTarget actual, MealTarget target) {
    // Pesi per dare priorità a diversi nutrienti
    const double calorieWeight = 1.0;
    const double proteinWeight = 2.0;  // Priorità più alta alle proteine
    const double carbWeight = 1.0;
    const double fatWeight = 1.0;

    // Calcola gli errori relativi (percentuali)
    final calorieError = target.calories > 0 ?
        (actual.calories - target.calories).abs() / target.calories : 0.0;
    final proteinError = target.proteins > 0 ?
        (actual.proteins - target.proteins).abs() / target.proteins : 0.0;
    final carbError = target.carbs > 0 ?
        (actual.carbs - target.carbs).abs() / target.carbs : 0.0;
    final fatError = target.fats > 0 ?
        (actual.fats - target.fats).abs() / target.fats : 0.0;

    // Calcola l'errore totale ponderato
    return (calorieWeight * calorieError) +
           (proteinWeight * proteinError) +
           (carbWeight * carbError) +
           (fatWeight * fatError);
  }

  /// Converti in FoodPortion per l'integrazione con il resto dell'app
  List<FoodPortion> toFoodPortions() {
    final portions = <FoodPortion>[];

    for (int i = 0; i < foods.length; i++) {
      portions.add(FoodPortion(
        food: foods[i],
        grams: grammages[i],
      ));
    }

    return portions;
  }
}

/// Servizio ottimizzato per la generazione di piani dietetici con grammature precise
class OptimizedDietGeneratorService {
  final FoodDatabase _foodDatabase;
  final Uuid _uuid = Uuid();
  late TranslationService _translationService;
  bool _translateFoodNames = true;

  // Tolleranze per la precisione
  static const double _calorieTolerancePercent = 0.01; // 1% di tolleranza per le calorie
  static const int _calorieToleranceAbsolute = 10; // ±10 kcal di tolleranza assoluta
  static const int _macroToleranceGrams = 1; // ±1g di tolleranza per i macronutrienti

  // Limiti per l'ottimizzazione
  static const int _maxOptimizationIterations = 100; // Numero massimo di iterazioni
  static const int _maxGlobalCorrectionCycles = 5; // Numero massimo di cicli di correzione globale

  // Singleton
  static OptimizedDietGeneratorService? _instance;

  static Future<OptimizedDietGeneratorService> getInstance() async {
    if (_instance == null) {
      final foodDatabase = FoodDatabase();
      final translationService = await TranslationService.getInstance();
      _instance = OptimizedDietGeneratorService._(foodDatabase, translationService);
      await _instance!._loadSettings();
    }
    return _instance!;
  }

  OptimizedDietGeneratorService._(this._foodDatabase, this._translationService);

  /// Carica le impostazioni di traduzione
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _translateFoodNames = prefs.getBool('translate_food_names') ?? true;
    } catch (e) {
      print('Errore nel caricamento delle impostazioni di traduzione: $e');
      _translateFoodNames = true;
    }
  }

  /// Imposta se tradurre i nomi degli alimenti
  Future<void> setTranslateFoodNames(bool translate) async {
    _translateFoodNames = translate;
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('translate_food_names', translate);
    } catch (e) {
      print('Errore nel salvataggio delle impostazioni di traduzione: $e');
    }
  }

  // Tiene traccia degli alimenti utilizzati per evitare ripetizioni
  final Map<String, int> _usedFoodsCount = {};

  /// Genera un piano dietetico settimanale completo
  Future<WeeklyDietPlan> generateWeeklyDietPlan(UserProfile userProfile, {int weeks = 1}) async {
    print('Iniziando la generazione del piano dietetico ottimizzato per $weeks settimane...');

    // Resetta il contatore degli alimenti utilizzati
    _usedFoodsCount.clear();

    // Assicurati che il database degli alimenti sia inizializzato
    await _foodDatabase.resetDatabase();

    // Verifica che il database contenga alimenti
    final allFoods = await _foodDatabase.getAllFoods();
    print('Numero di alimenti nel database: ${allFoods.length}');

    if (allFoods.isEmpty) {
      print('ERRORE: Il database degli alimenti è vuoto!');
      throw Exception('Il database degli alimenti è vuoto. Impossibile generare un piano dietetico.');
    }

    // Calcola l'obiettivo calorico e la distribuzione dei macronutrienti
    final calorieTarget = userProfile.calculateCalorieTarget();
    final macroGrams = userProfile.calculateMacroGrams();

    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivo macronutrienti: $macroGrams');

    // Crea un piano settimanale vuoto
    final startDate = _getStartOfWeek(DateTime.now());
    final weeklyPlan = WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano Dietetico Ottimizzato ${startDate.day}/${startDate.month}/${startDate.year} ($weeks settimane)',
      startDate: _formatDate(startDate),
      dailyPlans: [],
      userProfile: userProfile,
    );

    // Genera un piano per ogni giorno del periodo richiesto
    WeeklyDietPlan updatedWeeklyPlan = weeklyPlan;

    for (int i = 0; i < 7 * weeks; i++) {
      final date = startDate.add(Duration(days: i));
      final dailyPlan = await generateDailyDietPlan(
        userProfile,
        _formatDate(date),
        calorieTarget,
        macroGrams,
      );

      updatedWeeklyPlan = updatedWeeklyPlan.updateDailyPlan(dailyPlan);
    }

    return updatedWeeklyPlan;
  }

  /// Genera un piano dietetico giornaliero ottimizzato
  Future<DailyDietPlan> generateDailyDietPlan(
    UserProfile userProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
  ) async {
    print('Generazione piano giornaliero ottimizzato per la data: $date');
    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivi macronutrienti: $macroTargets');

    // Numero di pasti al giorno
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Definisci la struttura dei pasti (distribuzione percentuale)
    final mealStructure = _defineMealStructure(mealsPerDay);

    // Calcola i target nutrizionali per ogni pasto
    final mealTargets = _calculateMealTargets(
      calorieTarget,
      macroTargets,
      mealStructure
    );

    // Filtra gli alimenti disponibili per l'utente
    final availableFoods = await _filterAvailableFoods(userProfile);

    // Genera ogni pasto
    final meals = <PlannedMeal>[];
    final mealTimes = _getMealTimes(mealsPerDay);

    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final target = mealTargets[i];

      print('Generazione pasto $i: tipo=$mealType, target=$target');

      final meal = await _generateOptimizedMeal(
        userProfile,
        mealType.toString().split('.').last,
        target,
        mealTimes[i],
        availableFoods,
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // Verifica e correggi il piano giornaliero per garantire la precisione
    return _validateAndCorrectDailyPlan(dailyPlan, userProfile, availableFoods);
  }

  /// Definisci la struttura dei pasti (distribuzione percentuale)
  Map<int, double> _defineMealStructure(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return {
          0: 0.25, // Colazione: 25%
          1: 0.40, // Pranzo: 40%
          2: 0.35, // Cena: 35%
        };
      case 4:
        return {
          0: 0.25, // Colazione: 25%
          1: 0.10, // Spuntino: 10%
          2: 0.35, // Pranzo: 35%
          3: 0.30, // Cena: 30%
        };
      case 5:
        return {
          0: 0.20, // Colazione: 20%
          1: 0.10, // Spuntino mattutino: 10%
          2: 0.30, // Pranzo: 30%
          3: 0.15, // Spuntino pomeridiano: 15%
          4: 0.25, // Cena: 25%
        };
      case 6:
        return {
          0: 0.15, // Colazione: 15%
          1: 0.10, // Spuntino mattutino: 10%
          2: 0.30, // Pranzo: 30%
          3: 0.10, // Spuntino pomeridiano: 10%
          4: 0.25, // Cena: 25%
          5: 0.10, // Spuntino serale: 10%
        };
      default:
        // Distribuzione uniforme per altri casi
        final percentage = 1.0 / mealsPerDay;
        final structure = <int, double>{};
        for (int i = 0; i < mealsPerDay; i++) {
          structure[i] = percentage;
        }
        return structure;
    }
  }

  /// Calcola i target nutrizionali per ogni pasto
  List<MealTarget> _calculateMealTargets(
    int calorieTarget,
    Map<String, int> macroTargets,
    Map<int, double> mealStructure,
  ) {
    final mealTargets = <MealTarget>[];

    print('Distribuzione di $calorieTarget kcal in ${mealStructure.length} pasti');

    // Calcola i target per ogni pasto
    int totalCalories = 0;
    for (final entry in mealStructure.entries) {
      final mealIndex = entry.key;
      final percentage = entry.value;
      final mealType = _getMealType(mealIndex, mealStructure.length);

      // Calcola le calorie per questo pasto
      final mealCalories = (calorieTarget * percentage).round();
      totalCalories += mealCalories;

      print('Pasto ${mealIndex + 1} (${mealType}): $mealCalories kcal (${(percentage * 100).round()}%)');

      mealTargets.add(MealTarget(
        calories: mealCalories,
        proteins: 0, // Temporaneo, verrà calcolato dopo
        carbs: 0,    // Temporaneo, verrà calcolato dopo
        fats: 0,     // Temporaneo, verrà calcolato dopo
      ));
    }

    // Verifica che la somma delle calorie sia uguale al target
    final calorieAdjustment = calorieTarget - totalCalories;
    if (calorieAdjustment != 0) {
      // Aggiungi/sottrai la differenza all'ultimo pasto
      final lastIndex = mealTargets.length - 1;
      mealTargets[lastIndex] = mealTargets[lastIndex].copyWith(
        calories: mealTargets[lastIndex].calories + calorieAdjustment,
      );
    }

    print('Distribuzione calorie per pasto: ${mealTargets.map((t) => t.calories).toList()}');

    // Distribuzione macronutrienti
    print('Distribuzione macronutrienti - totalMacros: $macroTargets');

    // Calcola la distribuzione dei macronutrienti
    final totalProteins = macroTargets['proteins'] ?? macroTargets['proteine'] ?? 0;
    final totalCarbs = macroTargets['carbs'] ?? macroTargets['carboidrati'] ?? 0;
    final totalFats = macroTargets['fats'] ?? macroTargets['grassi'] ?? 0;

    print('Macronutrienti totali: $totalProteins g proteine, $totalCarbs g carboidrati, $totalFats g grassi');

    // Distribuisci i macronutrienti in base alle percentuali di calorie
    int distributedProteins = 0;
    int distributedCarbs = 0;
    int distributedFats = 0;

    for (int i = 0; i < mealTargets.length; i++) {
      final percentage = mealStructure[i]!;
      final mealType = _getMealType(i, mealStructure.length);

      // Calcola i macronutrienti per questo pasto
      int mealProteins = (totalProteins * percentage).round();
      int mealCarbs = (totalCarbs * percentage).round();
      int mealFats = (totalFats * percentage).round();

      distributedProteins += mealProteins;
      distributedCarbs += mealCarbs;
      distributedFats += mealFats;

      print('Pasto ${i + 1} (${mealType}): $mealProteins g proteine, $mealCarbs g carboidrati, $mealFats g grassi');

      mealTargets[i] = mealTargets[i].copyWith(
        proteins: mealProteins,
        carbs: mealCarbs,
        fats: mealFats,
      );
    }

    // Verifica che la somma dei macronutrienti sia uguale al target
    final proteinAdjustment = totalProteins - distributedProteins;
    final carbAdjustment = totalCarbs - distributedCarbs;
    final fatAdjustment = totalFats - distributedFats;

    if (proteinAdjustment != 0 || carbAdjustment != 0 || fatAdjustment != 0) {
      print('Macronutrienti rimanenti: $proteinAdjustment g proteine, $carbAdjustment g carboidrati, $fatAdjustment g grassi');

      // Aggiungi/sottrai la differenza all'ultimo pasto
      final lastIndex = mealTargets.length - 1;
      mealTargets[lastIndex] = mealTargets[lastIndex].copyWith(
        proteins: mealTargets[lastIndex].proteins + proteinAdjustment,
        carbs: mealTargets[lastIndex].carbs + carbAdjustment,
        fats: mealTargets[lastIndex].fats + fatAdjustment,
      );

      print('Aggiornato ultimo pasto: ${mealTargets[lastIndex].proteins} g proteine, ${mealTargets[lastIndex].carbs} g carboidrati, ${mealTargets[lastIndex].fats} g grassi');
    }

    print('Macronutrienti distribuiti: $totalProteins g proteine, $totalCarbs g carboidrati, $totalFats g grassi');
    print('Distribuzione macronutrienti per pasto: ${mealTargets.map((t) => '{proteins: ${t.proteins}, carbs: ${t.carbs}, fats: ${t.fats}}').toList()}');

    return mealTargets;
  }

  /// Filtra gli alimenti disponibili per l'utente
  Future<List<Food>> _filterAvailableFoods(UserProfile userProfile) async {
    // Ottieni tutti gli alimenti validati dal database
    final allFoods = await _foodDatabase.getAllFoods();

    // Traduci i nomi degli alimenti se necessario
    if (_translateFoodNames) {
      for (var food in allFoods) {
        try {
          final translatedName = _translationService.translateFoodName(food.name);
          food.setTranslatedName(translatedName);
          print('Tradotto: ${food.name} -> ${food.getDisplayName()}');
        } catch (e) {
          print('Errore nella traduzione di ${food.name}: $e');
          // In caso di errore, mantieni il nome originale
          food.setTranslatedName(null);
        }
      }
    } else {
      // Se la traduzione è disattivata, assicurati che tutti gli alimenti usino il nome originale
      for (var food in allFoods) {
        food.setTranslatedName(null);
      }
    }

    // Filtra per tipo di dieta
    final dietTypeFoods = allFoods.where((food) {
      switch (userProfile.dietType) {
        case DietType.vegetarian:
          return food.isVegetarian;
        case DietType.vegan:
          return food.isVegan;
        case DietType.pescatarian:
          return food.isVegetarian || food.categories.contains(FoodCategory.protein);
        case DietType.keto:
          return food.carbs <= 5; // Esempio semplificato per dieta keto
        case DietType.paleo:
          return !food.categories.contains(FoodCategory.grain) &&
                 !food.categories.contains(FoodCategory.dairy);
        case DietType.omnivore:
        default:
          return true;
      }
    }).toList();

    // Filtra per allergie
    final nonAllergenicFoods = dietTypeFoods.where((food) {
      for (var allergen in userProfile.allergies) {
        if (food.allergens.contains(allergen.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Filtra per cibi non graditi
    final preferredFoods = nonAllergenicFoods.where((food) {
      for (var dislikedFood in userProfile.dislikedFoods) {
        // Controlla sia il nome originale che quello tradotto
        if (food.name.toLowerCase().contains(dislikedFood.toLowerCase()) ||
            (food.getDisplayName() != food.name &&
             food.getDisplayName().toLowerCase().contains(dislikedFood.toLowerCase()))) {
          return false;
        }
      }
      return true;
    }).toList();

    // FILTRO DI SICUREZZA ALIMENTARE - CRITICO!
    // Rimuovi tutti gli alimenti non sicuri (es. pollo crudo, spinaci crudi, ecc.)
    final safeFoods = FoodSafetyService.filterSafeFoods(
      preferredFoods.isEmpty && userProfile.dislikedFoods.isNotEmpty
        ? (nonAllergenicFoods.isEmpty ? dietTypeFoods : nonAllergenicFoods)
        : preferredFoods
    );

    print('Alimenti sicuri dopo filtro di sicurezza: ${safeFoods.length}');

    // Se non ci sono alimenti sicuri, prova a convertire quelli non sicuri in versioni cotte
    if (safeFoods.isEmpty) {
      print('ATTENZIONE: Nessun alimento sicuro trovato, tentativo di conversione in versioni cotte...');
      final originalFoods = preferredFoods.isEmpty && userProfile.dislikedFoods.isNotEmpty
        ? (nonAllergenicFoods.isEmpty ? dietTypeFoods : nonAllergenicFoods)
        : preferredFoods;

      final convertedFoods = <Food>[];
      for (var food in originalFoods) {
        if (FoodSafetyService.requiresCooking(food)) {
          final cookedVersion = FoodSafetyService.getCookedVersion(food);
          if (cookedVersion != null) {
            convertedFoods.add(cookedVersion);
          }
        } else {
          convertedFoods.add(food);
        }
      }

      print('Alimenti convertiti in versioni sicure: ${convertedFoods.length}');
      return convertedFoods;
    }

    print('Alimenti disponibili dopo il filtraggio: ${safeFoods.length}');
    return safeFoods;
  }

  /// Genera un pasto ottimizzato
  Future<PlannedMeal> _generateOptimizedMeal(
    UserProfile userProfile,
    String mealType,
    MealTarget target,
    String time,
    List<Food> availableFoods,
  ) async {
    print('Generazione pasto ottimizzato di tipo $mealType');
    print('Target: ${target.calories} kcal, ${target.proteins}g proteine, ${target.carbs}g carboidrati, ${target.fats}g grassi');

    // Filtra gli alimenti adatti per questo tipo di pasto
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );
    final mealFoods = availableFoods.where((food) =>
      food.suitableForMeals.contains(mealTypeEnum)).toList();

    if (mealFoods.isEmpty) {
      print('Nessun alimento adatto trovato per il pasto di tipo $mealType');
      // Usa tutti gli alimenti disponibili come fallback
      mealFoods.addAll(availableFoods);
    }

    print('Alimenti adatti per il pasto di tipo $mealType: ${mealFoods.length}');

    // Organizza gli alimenti per categoria
    final Map<FoodCategory, List<Food>> foodsByCategory = {};
    for (var food in mealFoods) {
      for (var category in food.categories) {
        if (!foodsByCategory.containsKey(category)) {
          foodsByCategory[category] = [];
        }
        foodsByCategory[category]!.add(food);
      }
    }

    // Definisci le categorie di alimenti da includere in base al tipo di pasto
    final categoriesToInclude = _getCategoriesForMealType(mealType);
    print('Categorie da includere: $categoriesToInclude');

    // Seleziona un set preliminare di alimenti
    final selectedFoods = _selectPreliminaryFoods(foodsByCategory, categoriesToInclude, mealType);

    if (selectedFoods.isEmpty) {
      print('Impossibile selezionare alimenti per il pasto di tipo $mealType');
      return PlannedMeal(
        id: _uuid.v4(),
        name: _getMealNameFromString(mealType.toString().split('.').last),
        type: mealType,
        foods: [],
        time: time,
      );
    }

    print('Alimenti selezionati preliminarmente: ${selectedFoods.map((f) => f.name).toList()}');

    // Ottimizza le grammature
    final optimizedSolution = _optimizeGrammages(selectedFoods, target);

    // Converti la soluzione in FoodPortion
    final foodPortions = optimizedSolution.toFoodPortions();

    // Stampa il risultato
    print('Soluzione ottimizzata:');
    print('- Errore: ${optimizedSolution.errorScore}');
    print('- Calorie: ${optimizedSolution.actualNutrition.calories} / ${target.calories} kcal');
    print('- Proteine: ${optimizedSolution.actualNutrition.proteins} / ${target.proteins} g');
    print('- Carboidrati: ${optimizedSolution.actualNutrition.carbs} / ${target.carbs} g');
    print('- Grassi: ${optimizedSolution.actualNutrition.fats} / ${target.fats} g');

    // Crea il pasto
    return PlannedMeal(
      id: _uuid.v4(),
      name: _getMealNameFromString(mealType),
      type: mealType,
      foods: foodPortions,
      time: time,
    );
  }

  /// Ottieni le categorie di alimenti da includere in base al tipo di pasto
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    if (mealType == 'breakfast') {
      // Colazione: cereali/carboidrati + proteine + frutta
      return [
        FoodCategory.grain,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fruit,
      ];
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      // Pranzo/Cena: proteine + carboidrati + verdure + grassi
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
        FoodCategory.fat,
      ];
    } else if (mealType == 'snack') {
      // Spuntino: frutta + proteine o frutta secca
      return [
        FoodCategory.fruit,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fat,
      ];
    } else {
      // Default
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
      ];
    }
  }

  /// Seleziona un set preliminare di alimenti per il pasto
  List<Food> _selectPreliminaryFoods(
    Map<FoodCategory, List<Food>> foodsByCategory,
    List<FoodCategory> categoriesToInclude,
    String mealType,
  ) {
    final selectedFoods = <Food>[];
    final random = Random();

    // Seleziona un alimento da ciascuna categoria necessaria
    for (var category in categoriesToInclude) {
      if (foodsByCategory.containsKey(category) && foodsByCategory[category]!.isNotEmpty) {
        // Ordina gli alimenti in base alla categoria
        List<Food> categoryFoods = foodsByCategory[category]!;

        // Ordina gli alimenti per frequenza di utilizzo (meno utilizzati prima)
        categoryFoods.sort((a, b) {
          final countA = _usedFoodsCount[a.id] ?? 0;
          final countB = _usedFoodsCount[b.id] ?? 0;
          if (countA != countB) {
            return countA - countB; // Meno utilizzati prima
          } else {
            // Se hanno la stessa frequenza, ordina in base alla categoria
            return 0;
          }
        });

        // Applica l'ordinamento specifico per categoria solo ai primi 10 alimenti meno utilizzati
        final lessUsedFoods = categoryFoods.length > 10 ?
            categoryFoods.sublist(0, 10) : categoryFoods;
        _sortFoodsByCategory(lessUsedFoods, category);

        // Seleziona un alimento casuale tra i primi 3 (se disponibili)
        final selectionPool = lessUsedFoods.length > 3 ?
            lessUsedFoods.sublist(0, 3) : lessUsedFoods;

        final selectedFood = selectionPool[random.nextInt(selectionPool.length)];
        selectedFoods.add(selectedFood);

        // Incrementa il contatore di utilizzo per questo alimento
        _usedFoodsCount[selectedFood.id] = (_usedFoodsCount[selectedFood.id] ?? 0) + 1;

        // Rimuovi l'alimento selezionato da tutte le categorie per evitare duplicati
        for (var cat in foodsByCategory.keys) {
          foodsByCategory[cat]!.removeWhere((f) => f.id == selectedFood.id);
        }

        print('Selezionato ${selectedFood.name} (categoria: $category, utilizzi: ${_usedFoodsCount[selectedFood.id]})');
      }
    }

    return selectedFoods;
  }

  /// Ordina gli alimenti in base alla categoria
  void _sortFoodsByCategory(List<Food> foods, FoodCategory category) {
    switch (category) {
      case FoodCategory.protein:
        // Ordina per contenuto proteico decrescente
        foods.sort((a, b) => b.proteins.compareTo(a.proteins));
        break;
      case FoodCategory.grain:
        // Ordina per contenuto di carboidrati decrescente
        foods.sort((a, b) => b.carbs.compareTo(a.carbs));
        break;
      case FoodCategory.vegetable:
        // Ordina per contenuto di fibre decrescente
        foods.sort((a, b) => b.fiber.compareTo(a.fiber));
        break;
      case FoodCategory.fruit:
        // Ordina per contenuto di zuccheri crescente (preferisci frutta meno zuccherina)
        foods.sort((a, b) => a.sugar.compareTo(b.sugar));
        break;
      case FoodCategory.dairy:
        // Ordina per contenuto proteico decrescente
        foods.sort((a, b) => b.proteins.compareTo(a.proteins));
        break;
      case FoodCategory.fat:
        // Ordina per contenuto di grassi decrescente
        foods.sort((a, b) => b.fats.compareTo(a.fats));
        break;
      default:
        // Nessun ordinamento specifico
        break;
    }
  }

  /// Ottimizza le grammature degli alimenti per raggiungere i target nutrizionali
  MealSolution _optimizeGrammages(List<Food> foods, MealTarget target) {
    print('Ottimizzazione grammature per ${foods.length} alimenti');
    print('Target: ${target.calories} kcal, ${target.proteins}g proteine, ${target.carbs}g carboidrati, ${target.fats}g grassi');

    // Inizializza le grammature con valori standard
    List<int> grammages = _initializeGrammages(foods);

    // Calcola i valori nutrizionali iniziali
    MealTarget currentNutrition = _calculateNutrition(foods, grammages);
    double currentError = MealSolution.calculateErrorScore(currentNutrition, target);

    print('Valori iniziali:');
    print('- Grammature: $grammages');
    print('- Calorie: ${currentNutrition.calories} kcal');
    print('- Proteine: ${currentNutrition.proteins}g');
    print('- Carboidrati: ${currentNutrition.carbs}g');
    print('- Grassi: ${currentNutrition.fats}g');
    print('- Errore: $currentError');

    // Ottimizzazione iterativa
    for (int iteration = 0; iteration < _maxOptimizationIterations; iteration++) {
      bool improved = false;

      // Per ogni alimento, prova ad aumentare o diminuire la grammatura
      for (int i = 0; i < foods.length; i++) {
        // Prova ad aumentare la grammatura
        if (_tryIncreaseGrammage(foods, grammages, i, target, currentNutrition, currentError)) {
          // Ricalcola i valori nutrizionali e l'errore
          currentNutrition = _calculateNutrition(foods, grammages);
          currentError = MealSolution.calculateErrorScore(currentNutrition, target);
          improved = true;
        }

        // Prova a diminuire la grammatura
        else if (_tryDecreaseGrammage(foods, grammages, i, target, currentNutrition, currentError)) {
          // Ricalcola i valori nutrizionali e l'errore
          currentNutrition = _calculateNutrition(foods, grammages);
          currentError = MealSolution.calculateErrorScore(currentNutrition, target);
          improved = true;
        }
      }

      // Se non ci sono stati miglioramenti in questa iterazione, termina
      if (!improved) {
        print('Nessun miglioramento all\'iterazione $iteration, terminazione anticipata');
        break;
      }

      // Stampa lo stato ogni 10 iterazioni
      if (iteration % 10 == 0) {
        print('Iterazione $iteration:');
        print('- Grammature: $grammages');
        print('- Calorie: ${currentNutrition.calories} kcal');
        print('- Proteine: ${currentNutrition.proteins}g');
        print('- Carboidrati: ${currentNutrition.carbs}g');
        print('- Grassi: ${currentNutrition.fats}g');
        print('- Errore: $currentError');
      }

      // Se l'errore è sufficientemente basso, termina
      if (currentError < 0.05) {
        print('Errore sufficientemente basso ($currentError) all\'iterazione $iteration, terminazione anticipata');
        break;
      }
    }

    // Stampa il risultato finale
    print('Risultato finale:');
    print('- Grammature: $grammages');
    print('- Calorie: ${currentNutrition.calories} kcal');
    print('- Proteine: ${currentNutrition.proteins}g');
    print('- Carboidrati: ${currentNutrition.carbs}g');
    print('- Grassi: ${currentNutrition.fats}g');
    print('- Errore: $currentError');

    // Crea e restituisci la soluzione
    return MealSolution(
      foods: foods,
      grammages: grammages,
      actualNutrition: currentNutrition,
      errorScore: currentError,
    );
  }

  /// Inizializza le grammature con valori standard
  List<int> _initializeGrammages(List<Food> foods) {
    final grammages = <int>[];

    for (var food in foods) {
      // Usa la porzione standard dell'alimento o un valore predefinito
      int initialGrammage = food.servingSizeGrams;

      // Assicurati che la grammatura sia ragionevole
      if (initialGrammage < 10) {
        initialGrammage = 10;
      } else if (initialGrammage > 300) {
        initialGrammage = 300;
      }

      grammages.add(initialGrammage);
    }

    return grammages;
  }

  /// Calcola i valori nutrizionali per un set di alimenti con grammature specifiche
  MealTarget _calculateNutrition(List<Food> foods, List<int> grammages) {
    int totalCalories = 0;
    int totalProteins = 0;
    int totalCarbs = 0;
    int totalFats = 0;

    for (int i = 0; i < foods.length; i++) {
      final food = foods[i];
      final grams = grammages[i];

      // Calcola i valori nutrizionali per questa porzione
      totalCalories += (food.calories * grams / 100).round();
      totalProteins += (food.proteins * grams / 100).round();
      totalCarbs += (food.carbs * grams / 100).round();
      totalFats += (food.fats * grams / 100).round();
    }

    return MealTarget(
      calories: totalCalories,
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
    );
  }

  /// Prova ad aumentare la grammatura di un alimento
  bool _tryIncreaseGrammage(
    List<Food> foods,
    List<int> grammages,
    int foodIndex,
    MealTarget target,
    MealTarget currentNutrition,
    double currentError,
  ) {
    // Incremento di grammatura
    const int increment = 5;

    // Limite massimo di grammatura
    const int maxGrammage = 300;

    // Se la grammatura è già al massimo, non fare nulla
    if (grammages[foodIndex] >= maxGrammage) {
      return false;
    }

    // Aumenta temporaneamente la grammatura
    final originalGrammage = grammages[foodIndex];
    grammages[foodIndex] += increment;

    // Calcola i nuovi valori nutrizionali
    final newNutrition = _calculateNutrition(foods, grammages);
    final newError = MealSolution.calculateErrorScore(newNutrition, target);

    // Se l'errore è diminuito, mantieni la modifica
    if (newError < currentError) {
      return true;
    }

    // Altrimenti, ripristina la grammatura originale
    grammages[foodIndex] = originalGrammage;
    return false;
  }

  /// Prova a diminuire la grammatura di un alimento
  bool _tryDecreaseGrammage(
    List<Food> foods,
    List<int> grammages,
    int foodIndex,
    MealTarget target,
    MealTarget currentNutrition,
    double currentError,
  ) {
    // Decremento di grammatura
    const int decrement = 5;

    // Limite minimo di grammatura
    const int minGrammage = 10;

    // Se la grammatura è già al minimo, non fare nulla
    if (grammages[foodIndex] <= minGrammage) {
      return false;
    }

    // Diminuisci temporaneamente la grammatura
    final originalGrammage = grammages[foodIndex];
    grammages[foodIndex] -= decrement;

    // Calcola i nuovi valori nutrizionali
    final newNutrition = _calculateNutrition(foods, grammages);
    final newError = MealSolution.calculateErrorScore(newNutrition, target);

    // Se l'errore è diminuito, mantieni la modifica
    if (newError < currentError) {
      return true;
    }

    // Altrimenti, ripristina la grammatura originale
    grammages[foodIndex] = originalGrammage;
    return false;
  }

  /// Verifica e correggi il piano dietetico giornaliero
  Future<DailyDietPlan> _validateAndCorrectDailyPlan(
    DailyDietPlan dailyPlan,
    UserProfile userProfile,
    List<Food> availableFoods,
  ) async {
    print('Validazione e correzione del piano dietetico giornaliero');

    // Calcola le calorie e i macronutrienti totali del piano
    int totalCalories = 0;
    int totalProteins = 0;
    int totalCarbs = 0;
    int totalFats = 0;

    for (var meal in dailyPlan.meals) {
      totalCalories += meal.totalCalories;
      totalProteins += meal.totalMacros['proteins']!.round();
      totalCarbs += meal.totalMacros['carbs']!.round();
      totalFats += meal.totalMacros['fats']!.round();
    }

    // Calcola le deviazioni dai target
    int calorieDeviation = totalCalories - dailyPlan.calorieTarget;
    int proteinDeviation = totalProteins - dailyPlan.macroTargets['proteins']!;
    int carbDeviation = totalCarbs - dailyPlan.macroTargets['carbs']!;
    int fatDeviation = totalFats - dailyPlan.macroTargets['fats']!;

    // Calcola le deviazioni percentuali
    final calorieDeviationPercent = dailyPlan.calorieTarget > 0 ?
        (calorieDeviation / dailyPlan.calorieTarget * 100).round() : 0;
    final proteinDeviationPercent = dailyPlan.macroTargets['proteins']! > 0 ?
        (proteinDeviation / dailyPlan.macroTargets['proteins']! * 100).round() : 0;
    final carbDeviationPercent = dailyPlan.macroTargets['carbs']! > 0 ?
        (carbDeviation / dailyPlan.macroTargets['carbs']! * 100).round() : 0;
    final fatDeviationPercent = dailyPlan.macroTargets['fats']! > 0 ?
        (fatDeviation / dailyPlan.macroTargets['fats']! * 100).round() : 0;

    print('Deviazioni dai target:');
    print('- Calorie: $calorieDeviation kcal ($calorieDeviationPercent%)');
    print('- Proteine: $proteinDeviation g ($proteinDeviationPercent%)');
    print('- Carboidrati: $carbDeviation g ($carbDeviationPercent%)');
    print('- Grassi: $fatDeviation g ($fatDeviationPercent%)');

    // Verifica se le deviazioni sono accettabili
    final calorieDeviationPercentAbs = calorieDeviationPercent.abs();
    final proteinDeviationPercentAbs = proteinDeviationPercent.abs();
    final carbDeviationPercentAbs = carbDeviationPercent.abs();
    final fatDeviationPercentAbs = fatDeviationPercent.abs();

    // Tolleranze accettabili
    const calorieTolerancePercent = 1; // 1% di tolleranza per le calorie
    const macroTolerancePercent = 2; // 2% di tolleranza per i macronutrienti

    // Se le deviazioni sono accettabili, restituisci il piano originale
    if (calorieDeviationPercentAbs <= calorieTolerancePercent &&
        proteinDeviationPercentAbs <= macroTolerancePercent &&
        carbDeviationPercentAbs <= macroTolerancePercent &&
        fatDeviationPercentAbs <= macroTolerancePercent) {
      print('Piano dietetico giornaliero valido, deviazioni entro le tolleranze accettabili');
      return dailyPlan;
    }

    // Altrimenti, correggi il piano
    print('Correzione del piano dietetico giornaliero necessaria');

    // Ciclo di correzione globale
    for (int cycle = 0; cycle < _maxGlobalCorrectionCycles; cycle++) {
      print('Ciclo di correzione globale $cycle');

      // Identifica il pasto più problematico
      int mostProblematicMealIndex = _findMostProblematicMeal(
        dailyPlan.meals,
        calorieDeviation,
        proteinDeviation,
        carbDeviation,
        fatDeviation,
      );

      final problematicMeal = dailyPlan.meals[mostProblematicMealIndex];
      print('Pasto più problematico: ${problematicMeal.name} (${problematicMeal.type})');

      // Calcola i target corretti per questo pasto
      final originalTarget = MealTarget(
        calories: problematicMeal.totalCalories,
        proteins: problematicMeal.totalMacros['proteins']!.round(),
        carbs: problematicMeal.totalMacros['carbs']!.round(),
        fats: problematicMeal.totalMacros['fats']!.round(),
      );

      // Calcola i target corretti sottraendo una parte della deviazione
      final correctionFactor = 0.5; // Correggi il 50% della deviazione in questo ciclo
      final correctedTarget = MealTarget(
        calories: originalTarget.calories - (calorieDeviation * correctionFactor).round(),
        proteins: originalTarget.proteins - (proteinDeviation * correctionFactor).round(),
        carbs: originalTarget.carbs - (carbDeviation * correctionFactor).round(),
        fats: originalTarget.fats - (fatDeviation * correctionFactor).round(),
      );

      print('Target originale: $originalTarget');
      print('Target corretto: $correctedTarget');

      // Estrai gli alimenti dal pasto problematico
      final mealFoods = problematicMeal.foods.map((portion) => portion.food).toList();

      // Ottimizza le grammature con i nuovi target
      final optimizedSolution = _optimizeGrammages(mealFoods, correctedTarget);

      // Crea un nuovo pasto con le grammature ottimizzate
      final correctedMeal = PlannedMeal(
        id: problematicMeal.id,
        name: problematicMeal.name,
        type: problematicMeal.type,
        foods: optimizedSolution.toFoodPortions(),
        time: problematicMeal.time,
      );

      // Sostituisci il pasto nel piano
      final correctedMeals = List<PlannedMeal>.from(dailyPlan.meals);
      correctedMeals[mostProblematicMealIndex] = correctedMeal;

      // Crea il piano corretto
      final correctedPlan = DailyDietPlan(
        date: dailyPlan.date,
        meals: correctedMeals,
        calorieTarget: dailyPlan.calorieTarget,
        macroTargets: dailyPlan.macroTargets,
      );

      // Ricalcola le deviazioni
      int correctedTotalCalories = 0;
      int correctedTotalProteins = 0;
      int correctedTotalCarbs = 0;
      int correctedTotalFats = 0;

      for (var meal in correctedPlan.meals) {
        correctedTotalCalories += meal.totalCalories;
        correctedTotalProteins += meal.totalMacros['proteins']!.round();
        correctedTotalCarbs += meal.totalMacros['carbs']!.round();
        correctedTotalFats += meal.totalMacros['fats']!.round();
      }

      final correctedCalorieDeviation = correctedTotalCalories - dailyPlan.calorieTarget;
      final correctedProteinDeviation = correctedTotalProteins - dailyPlan.macroTargets['proteins']!;
      final correctedCarbDeviation = correctedTotalCarbs - dailyPlan.macroTargets['carbs']!;
      final correctedFatDeviation = correctedTotalFats - dailyPlan.macroTargets['fats']!;

      // Calcola le deviazioni percentuali corrette
      final correctedCalorieDeviationPercent = dailyPlan.calorieTarget > 0 ?
          (correctedCalorieDeviation / dailyPlan.calorieTarget * 100).round() : 0;
      final correctedProteinDeviationPercent = dailyPlan.macroTargets['proteins']! > 0 ?
          (correctedProteinDeviation / dailyPlan.macroTargets['proteins']! * 100).round() : 0;
      final correctedCarbDeviationPercent = dailyPlan.macroTargets['carbs']! > 0 ?
          (correctedCarbDeviation / dailyPlan.macroTargets['carbs']! * 100).round() : 0;
      final correctedFatDeviationPercent = dailyPlan.macroTargets['fats']! > 0 ?
          (correctedFatDeviation / dailyPlan.macroTargets['fats']! * 100).round() : 0;

      print('Piano corretto:');
      print('- Calorie: $correctedTotalCalories / ${dailyPlan.calorieTarget} kcal ($correctedCalorieDeviationPercent%)');
      print('- Proteine: $correctedTotalProteins / ${dailyPlan.macroTargets['proteins']} g ($correctedProteinDeviationPercent%)');
      print('- Carboidrati: $correctedTotalCarbs / ${dailyPlan.macroTargets['carbs']} g ($correctedCarbDeviationPercent%)');
      print('- Grassi: $correctedTotalFats / ${dailyPlan.macroTargets['fats']} g ($correctedFatDeviationPercent%)');

      // Verifica se le deviazioni corrette sono accettabili
      final correctedCalorieDeviationPercentAbs = correctedCalorieDeviationPercent.abs();
      final correctedProteinDeviationPercentAbs = correctedProteinDeviationPercent.abs();
      final correctedCarbDeviationPercentAbs = correctedCarbDeviationPercent.abs();
      final correctedFatDeviationPercentAbs = correctedFatDeviationPercent.abs();

      if (correctedCalorieDeviationPercentAbs <= calorieTolerancePercent &&
          correctedProteinDeviationPercentAbs <= macroTolerancePercent &&
          correctedCarbDeviationPercentAbs <= macroTolerancePercent &&
          correctedFatDeviationPercentAbs <= macroTolerancePercent) {
        print('Piano dietetico giornaliero corretto, deviazioni entro le tolleranze accettabili');
        return correctedPlan;
      }

      // Aggiorna il piano e le deviazioni per il prossimo ciclo
      dailyPlan = correctedPlan;
      calorieDeviation = correctedCalorieDeviation;
      proteinDeviation = correctedProteinDeviation;
      carbDeviation = correctedCarbDeviation;
      fatDeviation = correctedFatDeviation;
    }

    print('Raggiunto il numero massimo di cicli di correzione, restituisco il piano migliore possibile');
    return dailyPlan;
  }

  /// Trova il pasto più problematico in termini di deviazioni dai target
  int _findMostProblematicMeal(
    List<PlannedMeal> meals,
    int calorieDeviation,
    int proteinDeviation,
    int carbDeviation,
    int fatDeviation,
  ) {
    int mostProblematicMealIndex = 0;
    double highestContribution = 0;

    for (int i = 0; i < meals.length; i++) {
      final meal = meals[i];

      // Calcola il contributo di questo pasto alle deviazioni
      final calorieContribution = meal.totalCalories / (calorieDeviation.abs() + 1);
      final proteinContribution = meal.totalMacros['proteins']! / (proteinDeviation.abs() + 1);
      final carbContribution = meal.totalMacros['carbs']! / (carbDeviation.abs() + 1);
      final fatContribution = meal.totalMacros['fats']! / (fatDeviation.abs() + 1);

      // Calcola il contributo totale ponderato
      final totalContribution =
          calorieContribution +
          (proteinContribution * 2) + // Priorità più alta alle proteine
          carbContribution +
          fatContribution;

      if (totalContribution > highestContribution) {
        highestContribution = totalContribution;
        mostProblematicMealIndex = i;
      }
    }

    return mostProblematicMealIndex;
  }

  /// Ottieni il tipo di pasto in base all'indice
  MealType _getMealType(int index, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        // 3 pasti: colazione, pranzo, cena
        switch (index) {
          case 0: return MealType.breakfast;
          case 1: return MealType.lunch;
          case 2: return MealType.dinner;
          default: return MealType.snack;
        }
      case 4:
        // 4 pasti: colazione, spuntino, pranzo, cena
        switch (index) {
          case 0: return MealType.breakfast;
          case 1: return MealType.snack;
          case 2: return MealType.lunch;
          case 3: return MealType.dinner;
          default: return MealType.snack;
        }
      case 5:
        // 5 pasti: colazione, spuntino, pranzo, spuntino, cena
        switch (index) {
          case 0: return MealType.breakfast;
          case 1: return MealType.snack;
          case 2: return MealType.lunch;
          case 3: return MealType.snack;
          case 4: return MealType.dinner;
          default: return MealType.snack;
        }
      case 6:
        // 6 pasti: colazione, spuntino, pranzo, spuntino, cena, spuntino
        switch (index) {
          case 0: return MealType.breakfast;
          case 1: return MealType.snack;
          case 2: return MealType.lunch;
          case 3: return MealType.snack;
          case 4: return MealType.dinner;
          case 5: return MealType.snack;
          default: return MealType.snack;
        }
      default:
        // Per altri numeri di pasti, alterna tra i tipi principali
        if (index == 0) return MealType.breakfast;
        if (index == mealsPerDay - 1) return MealType.dinner;
        if (index == mealsPerDay ~/ 2) return MealType.lunch;
        return MealType.snack;
    }
  }

  /// Ottieni gli orari dei pasti in base al numero di pasti
  List<String> _getMealTimes(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return ['08:00', '13:00', '20:00'];
      case 4:
        return ['08:00', '11:00', '14:00', '20:00'];
      case 5:
        return ['08:00', '10:30', '13:00', '16:30', '20:00'];
      case 6:
        return ['08:00', '10:00', '13:00', '16:00', '19:00', '22:00'];
      default:
        // Per altri numeri di pasti, distribuisci uniformemente tra le 8:00 e le 22:00
        final mealTimes = <String>[];
        final totalMinutes = 14 * 60; // 14 ore (dalle 8:00 alle 22:00) in minuti
        final intervalMinutes = totalMinutes ~/ (mealsPerDay - 1);

        for (int i = 0; i < mealsPerDay; i++) {
          final minutesFromStart = i * intervalMinutes;
          final hour = 8 + (minutesFromStart ~/ 60);
          final minute = minutesFromStart % 60;

          final hourStr = hour.toString().padLeft(2, '0');
          final minuteStr = minute.toString().padLeft(2, '0');

          mealTimes.add('$hourStr:$minuteStr');
        }

        return mealTimes;
    }
  }

  /// Ottieni il nome del pasto in base al tipo
  String _getMealName(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast: return 'Colazione';
      case MealType.lunch: return 'Pranzo';
      case MealType.dinner: return 'Cena';
      case MealType.snack: return 'Spuntino';
    }
  }

  /// Ottieni il nome del pasto in base al tipo (stringa)
  String _getMealNameFromString(String mealType) {
    if (mealType == 'breakfast') {
      return 'Colazione';
    } else if (mealType == 'lunch') {
      return 'Pranzo';
    } else if (mealType == 'dinner') {
      return 'Cena';
    } else if (mealType == 'snack') {
      return 'Spuntino';
    } else {
      return 'Pasto';
    }
  }

  /// Ottieni l'inizio della settimana (lunedì) per una data
  DateTime _getStartOfWeek(DateTime date) {
    final dayOfWeek = date.weekday;
    return date.subtract(Duration(days: dayOfWeek - 1));
  }

  /// Formatta una data in formato YYYY-MM-DD
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
