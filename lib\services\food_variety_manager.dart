import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/food.dart';

/// Gestisce la varietà degli alimenti nei piani dietetici per evitare ripetizioni
/// e massimizzare l'utilizzo del database completo di alimenti italiani
class FoodVarietyManager {
  static const String _usageHistoryKey = 'food_usage_history';
  static const int _maxHistoryDays = 14; // Mantieni storia per 2 settimane
  static const int _cooldownDays = 3; // Giorni di "riposo" per alimenti utilizzati di frequente

  // Singleton pattern
  static FoodVarietyManager? _instance;
  static Future<FoodVarietyManager> getInstance() async {
    _instance ??= FoodVarietyManager._();
    await _instance!._initialize();
    return _instance!;
  }

  FoodVarietyManager._();

  Map<String, List<DateTime>> _foodUsageHistory = {};
  bool _isInitialized = false;

  /// Inizializza il manager caricando la storia degli utilizzi
  Future<void> _initialize() async {
    if (_isInitialized) return;

    await _loadUsageHistory();
    await _cleanOldHistory();
    _isInitialized = true;
  }

  /// Carica la storia degli utilizzi dal storage locale
  Future<void> _loadUsageHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_usageHistoryKey);

      if (historyJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(historyJson);
        _foodUsageHistory = decoded.map((key, value) {
          final List<DateTime> dates = (value as List)
              .map((dateStr) => DateTime.parse(dateStr))
              .toList();
          return MapEntry(key, dates);
        });
      }
    } catch (e) {
      print('Errore nel caricamento della storia varietà alimenti: $e');
      _foodUsageHistory = {};
    }
  }

  /// Salva la storia degli utilizzi nel storage locale
  Future<void> _saveUsageHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> toSave = _foodUsageHistory.map((key, value) {
        return MapEntry(key, value.map((date) => date.toIso8601String()).toList());
      });

      await prefs.setString(_usageHistoryKey, jsonEncode(toSave));
    } catch (e) {
      print('Errore nel salvataggio della storia varietà alimenti: $e');
    }
  }

  /// Rimuove le voci di utilizzo più vecchie di _maxHistoryDays
  Future<void> _cleanOldHistory() async {
    final cutoffDate = DateTime.now().subtract(Duration(days: _maxHistoryDays));

    for (final foodId in _foodUsageHistory.keys.toList()) {
      _foodUsageHistory[foodId]!.removeWhere((date) => date.isBefore(cutoffDate));

      // Rimuovi completamente gli alimenti senza utilizzi recenti
      if (_foodUsageHistory[foodId]!.isEmpty) {
        _foodUsageHistory.remove(foodId);
      }
    }

    await _saveUsageHistory();
  }

  /// Registra l'utilizzo di un alimento
  Future<void> recordFoodUsage(String foodId, {DateTime? date}) async {
    await _initialize();

    final usageDate = date ?? DateTime.now();

    if (!_foodUsageHistory.containsKey(foodId)) {
      _foodUsageHistory[foodId] = [];
    }

    _foodUsageHistory[foodId]!.add(usageDate);
    await _saveUsageHistory();
  }

  /// Registra l'utilizzo di più alimenti (per un pasto completo)
  Future<void> recordMealUsage(List<String> foodIds, {DateTime? date}) async {
    for (final foodId in foodIds) {
      await recordFoodUsage(foodId, date: date);
    }
  }

  /// Calcola il punteggio di varietà per un alimento (più alto = più varietà)
  int calculateVarietyScore(Food food, {DateTime? forDate}) {
    final checkDate = forDate ?? DateTime.now();
    final foodId = food.id;

    int score = 100; // Punteggio base

    // Penalizza alimenti utilizzati di recente
    if (_foodUsageHistory.containsKey(foodId)) {
      final recentUsages = _foodUsageHistory[foodId]!
          .where((date) => checkDate.difference(date).inDays <= _cooldownDays)
          .length;

      score -= recentUsages * 25; // -25 punti per ogni utilizzo recente
    }

    // Bonus per alimenti stagionali nel periodo giusto
    if (food.isSeasonal && food.isCurrentlySeasonal()) {
      score += 20;
    }

    // Bonus per alimenti tradizionali italiani
    if (food.isTraditionalItalian) {
      score += 15;
    }

    // Bonus per alimenti con dati nutrizionali verificati
    if (food.validationStatus == ValidationStatus.validated) {
      score += 10;
    }

    // Bonus per alimenti con micronutrienti completi
    if (food.micronutrients.isNotEmpty) {
      score += 10;
    }

    // Penalizza alimenti processati
    if (food.foodState == FoodState.processed) {
      score -= 15;
    }

    return math.max(score, 0); // Non permettere punteggi negativi
  }

  /// Seleziona alimenti da una lista con priorità alla varietà
  List<Food> selectVariedFoods(
    List<Food> availableFoods, {
    int maxSelections = 5,
    DateTime? forDate,
    List<FoodCategory>? preferredCategories,
  }) {
    if (availableFoods.isEmpty) return [];

    // Calcola punteggi di varietà per tutti gli alimenti
    final scoredFoods = availableFoods.map((food) {
      int score = calculateVarietyScore(food, forDate: forDate);

      // Bonus per categorie preferite
      if (preferredCategories != null) {
        for (final category in food.categories) {
          if (preferredCategories.contains(category)) {
            score += 30;
          }
        }
      }

      return _ScoredFood(food, score);
    }).toList();

    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    // Seleziona i migliori alimenti con un elemento di casualità
    final selectedFoods = <Food>[];
    final random = math.Random();

    for (int i = 0; i < math.min(maxSelections, scoredFoods.length); i++) {
      // Seleziona dai primi 3 alimenti con maggior punteggio per aggiungere varietà
      final topCandidates = scoredFoods.take(math.min(3, scoredFoods.length)).toList();
      final selectedIndex = random.nextInt(topCandidates.length);
      final selectedFood = topCandidates[selectedIndex];

      selectedFoods.add(selectedFood.food);
      scoredFoods.remove(selectedFood);

      if (scoredFoods.isEmpty) break;
    }

    return selectedFoods;
  }

  /// Ottiene statistiche sull'utilizzo degli alimenti
  Map<String, dynamic> getUsageStatistics() {
    final totalFoods = _foodUsageHistory.length;
    final totalUsages = _foodUsageHistory.values
        .map((dates) => dates.length)
        .fold(0, (sum, count) => sum + count);

    final recentUsages = _foodUsageHistory.values
        .map((dates) => dates.where((date) =>
            DateTime.now().difference(date).inDays <= 7).length)
        .fold(0, (sum, count) => sum + count);

    return {
      'totalTrackedFoods': totalFoods,
      'totalUsages': totalUsages,
      'recentUsages': recentUsages,
      'averageUsagePerFood': totalFoods > 0 ? totalUsages / totalFoods : 0,
    };
  }

  /// Resetta la storia degli utilizzi (per test o reset completo)
  Future<void> resetUsageHistory() async {
    _foodUsageHistory.clear();
    await _saveUsageHistory();
  }

  /// Ottiene gli alimenti utilizzati di recente per evitarli
  List<String> getRecentlyUsedFoods({int days = 3}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    final recentFoods = <String>[];

    for (final entry in _foodUsageHistory.entries) {
      final hasRecentUsage = entry.value.any((date) => date.isAfter(cutoffDate));
      if (hasRecentUsage) {
        recentFoods.add(entry.key);
      }
    }

    return recentFoods;
  }
}

/// Classe helper per associare un alimento al suo punteggio di varietà
class _ScoredFood {
  final Food food;
  final int score;

  _ScoredFood(this.food, this.score);
}
