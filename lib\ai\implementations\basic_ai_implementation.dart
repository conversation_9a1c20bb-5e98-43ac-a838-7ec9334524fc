import 'dart:math';
import 'package:uuid/uuid.dart';
import '../interfaces/ai_interface.dart';
import '../models/ai_models.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../models/diet_plan.dart';
import '../../services/food_database_service.dart';
import '../../services/optimized_diet_generator_service.dart';
import '../../services/storage_service.dart';

/// Implementazione base del sistema di intelligenza artificiale
class BasicAIImplementation implements AIInterface {
  final FoodDatabaseService _foodDatabaseService;
  final StorageService _storageService;
  final Uuid _uuid = Uuid();
  
  // Cache delle preferenze apprese
  final Map<String, List<LearnedPreference>> _userPreferencesCache = {};
  
  // Stato di inizializzazione
  bool _isInitialized = false;

  BasicAIImplementation({
    required FoodDatabaseService foodDatabaseService,
    required StorageService storageService,
  }) : 
    _foodDatabaseService = foodDatabaseService,
    _storageService = storageService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    print('Inizializzazione del sistema AI...');
    
    // Carica le preferenze degli utenti
    await _loadUserPreferences();
    
    _isInitialized = true;
    print('Sistema AI inizializzato con successo');
  }

  Future<void> _loadUserPreferences() async {
    try {
      // In una implementazione reale, qui caricheremmo le preferenze dal database
      // Per ora, inizializziamo con un insieme vuoto
      _userPreferencesCache.clear();
      print('Preferenze utenti caricate');
    } catch (e) {
      print('Errore nel caricamento delle preferenze: $e');
    }
  }

  @override
  Future<void> train(AILearningContext context) async {
    print('Addestramento del modello AI...');
    
    // Aggiorna le preferenze apprese in base ai feedback
    for (var feedback in context.feedbacks) {
      if (feedback.foodId != null) {
        await _updateFoodPreference(
          feedback.userId, 
          feedback.foodId!, 
          _convertRatingToPreferenceScore(feedback.rating)
        );
      }
    }
    
    print('Addestramento completato');
  }

  double _convertRatingToPreferenceScore(int rating) {
    // Converte un rating 1-5 in un punteggio di preferenza -1.0 a 1.0
    return (rating - 3) / 2;
  }

  Future<void> _updateFoodPreference(String userId, String foodId, double score) async {
    // Ottieni le preferenze esistenti dell'utente
    if (!_userPreferencesCache.containsKey(userId)) {
      _userPreferencesCache[userId] = [];
    }
    
    // Cerca una preferenza esistente
    int existingIndex = _userPreferencesCache[userId]!.indexWhere(
      (pref) => pref.foodId == foodId
    );
    
    if (existingIndex >= 0) {
      // Aggiorna la preferenza esistente
      var existing = _userPreferencesCache[userId]![existingIndex];
      var updated = LearnedPreference(
        id: existing.id,
        userId: userId,
        foodId: foodId,
        // Media ponderata con più peso al nuovo valore
        preferenceScore: (existing.preferenceScore * 0.7) + (score * 0.3),
        updatedAt: DateTime.now(),
      );
      
      _userPreferencesCache[userId]![existingIndex] = updated;
    } else {
      // Crea una nuova preferenza
      var newPreference = LearnedPreference(
        id: _uuid.v4(),
        userId: userId,
        foodId: foodId,
        preferenceScore: score,
        updatedAt: DateTime.now(),
      );
      
      _userPreferencesCache[userId]!.add(newPreference);
    }
    
    // In una implementazione reale, qui salveremmo le preferenze nel database
    print('Preferenza aggiornata per l\'utente $userId, alimento $foodId: $score');
  }

  @override
  Future<AIRecommendationResponse> getRecommendations(AIRecommendationRequest request) async {
    print('Generazione raccomandazioni per l\'utente ${request.userProfile.id}');
    
    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabaseService.getAllFoods();
    
    // Filtra gli alimenti in base alle preferenze dell'utente
    final filteredFoods = _filterFoodsByUserPreferences(
      allFoods,
      request.userProfile.id,
      request.excludeFoodIds ?? [],
      request.preferredCategories ?? [],
    );
    
    // Calcola i punteggi di raccomandazione
    final scoredFoods = _scoreFoodsForRecommendation(
      filteredFoods,
      request.userProfile,
      request.mealType,
    );
    
    // Crea le raccomandazioni
    final recommendations = scoredFoods.map((scoredFood) {
      return FoodRecommendation(
        food: scoredFood.food,
        score: scoredFood.score,
        reason: _generateRecommendationReason(scoredFood.food, request.userProfile),
      );
    }).toList();
    
    return AIRecommendationResponse(
      recommendedFoods: recommendations,
      message: 'Raccomandazioni generate in base alle tue preferenze',
    );
  }

  List<Food> _filterFoodsByUserPreferences(
    List<Food> foods,
    String userId,
    List<String> excludeFoodIds,
    List<FoodCategory> preferredCategories,
  ) {
    // Filtra gli alimenti esclusi
    var filtered = foods.where((food) => !excludeFoodIds.contains(food.id)).toList();
    
    // Se ci sono categorie preferite, filtra per quelle
    if (preferredCategories.isNotEmpty) {
      filtered = filtered.where((food) {
        return food.categories.any((cat) => preferredCategories.contains(cat));
      }).toList();
    }
    
    return filtered;
  }

  List<_ScoredFood> _scoreFoodsForRecommendation(
    List<Food> foods,
    UserProfile userProfile,
    String? mealType,
  ) {
    final scoredFoods = <_ScoredFood>[];
    
    for (var food in foods) {
      double score = 0.5; // Punteggio base
      
      // Considera il tipo di pasto
      if (mealType != null) {
        final mealTypeEnum = MealType.values.firstWhere(
          (e) => e.toString().split('.').last == mealType,
          orElse: () => MealType.values.first,
        );
        
        if (food.suitableForMeals.contains(mealTypeEnum)) {
          score += 0.2;
        } else {
          score -= 0.2;
        }
      }
      
      // Considera le preferenze dietetiche
      if (userProfile.dietType.toString().contains('vegetarian') && !food.isVegetarian) {
        score = 0; // Escludi completamente
      } else if (userProfile.dietType.toString().contains('vegan') && !food.isVegan) {
        score = 0; // Escludi completamente
      }
      
      // Considera le allergie
      for (var allergen in userProfile.allergies) {
        if (food.allergens.contains(allergen.toLowerCase())) {
          score = 0; // Escludi completamente
          break;
        }
      }
      
      // Considera le preferenze apprese
      if (_userPreferencesCache.containsKey(userProfile.id)) {
        final userPrefs = _userPreferencesCache[userProfile.id]!;
        final foodPref = userPrefs.firstWhere(
          (pref) => pref.foodId == food.id,
          orElse: () => LearnedPreference(
            id: '',
            userId: userProfile.id,
            foodId: food.id,
            preferenceScore: 0,
            updatedAt: DateTime.now(),
          ),
        );
        
        // Aggiusta il punteggio in base alla preferenza appresa
        score += foodPref.preferenceScore * 0.3;
      }
      
      // Limita il punteggio tra 0 e 1
      score = score.clamp(0.0, 1.0);
      
      scoredFoods.add(_ScoredFood(food: food, score: score));
    }
    
    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));
    
    return scoredFoods;
  }

  String _generateRecommendationReason(Food food, UserProfile userProfile) {
    final reasons = <String>[];
    
    // Aggiungi motivi basati sulle caratteristiche dell'alimento
    if (food.isVegetarian && userProfile.dietType.toString().contains('vegetarian')) {
      reasons.add('Adatto alla tua dieta vegetariana');
    }
    
    if (food.isVegan && userProfile.dietType.toString().contains('vegan')) {
      reasons.add('Adatto alla tua dieta vegana');
    }
    
    if (food.isGlutenFree && userProfile.allergies.contains('glutine')) {
      reasons.add('Senza glutine');
    }
    
    if (food.isDairyFree && userProfile.allergies.contains('lattosio')) {
      reasons.add('Senza lattosio');
    }
    
    // Aggiungi motivi basati sui nutrienti
    if (food.proteins > 15) {
      reasons.add('Ricco di proteine');
    }
    
    if (food.fiber > 5) {
      reasons.add('Ricco di fibre');
    }
    
    if (reasons.isEmpty) {
      return 'Alimento consigliato in base al tuo profilo';
    }
    
    return reasons.join(', ');
  }

  @override
  Future<void> recordFeedback(UserFeedback feedback) async {
    print('Registrazione feedback: ${feedback.toMap()}');
    
    // Aggiorna le preferenze se il feedback è su un alimento
    if (feedback.foodId != null) {
      await _updateFoodPreference(
        feedback.userId,
        feedback.foodId!,
        _convertRatingToPreferenceScore(feedback.rating),
      );
    }
    
    // In una implementazione reale, qui salveremmo il feedback nel database
  }

  @override
  Future<List<String>> analyzeDietPlan(WeeklyDietPlan dietPlan, UserProfile userProfile) async {
    final suggestions = <String>[];
    
    // Analisi semplice del piano dietetico
    int totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    
    for (var dailyPlan in dietPlan.dailyPlans) {
      totalCalories += dailyPlan.totalCaloriesPlanned;
      totalProteins += dailyPlan.getMacroValuePlanned('proteins');
      totalCarbs += dailyPlan.getMacroValuePlanned('carbs');
      totalFats += dailyPlan.getMacroValuePlanned('fats');
    }
    
    // Calcola le medie giornaliere
    final days = dietPlan.dailyPlans.length;
    final avgCalories = totalCalories / days;
    final avgProteins = totalProteins / days;
    final avgCarbs = totalCarbs / days;
    final avgFats = totalFats / days;
    
    // Verifica se le calorie sono appropriate
    final targetCalories = userProfile.calculateCalorieTarget();
    if (avgCalories < targetCalories * 0.9) {
      suggestions.add('Il piano fornisce meno calorie del necessario. Considera di aumentare le porzioni.');
    } else if (avgCalories > targetCalories * 1.1) {
      suggestions.add('Il piano fornisce più calorie del necessario. Considera di ridurre le porzioni.');
    }
    
    // Verifica la distribuzione dei macronutrienti
    final targetMacros = userProfile.calculateMacroGrams();
    final targetProteins = targetMacros['proteins'] ?? 0;
    final targetCarbs = targetMacros['carbs'] ?? 0;
    final targetFats = targetMacros['fats'] ?? 0;
    
    if (avgProteins < targetProteins * 0.8) {
      suggestions.add('Il piano è carente di proteine. Aggiungi più alimenti proteici come carne, pesce, legumi o tofu.');
    }
    
    if (avgCarbs < targetCarbs * 0.8) {
      suggestions.add('Il piano è carente di carboidrati. Aggiungi più cereali, pasta, riso o patate.');
    }
    
    if (avgFats < targetFats * 0.8) {
      suggestions.add('Il piano è carente di grassi sani. Aggiungi più olio d\'oliva, avocado o frutta secca.');
    }
    
    // Verifica la varietà degli alimenti
    final allFoods = <String>{};
    for (var dailyPlan in dietPlan.dailyPlans) {
      for (var meal in dailyPlan.meals) {
        for (var food in meal.foods) {
          allFoods.add(food.food.id);
        }
      }
    }
    
    if (allFoods.length < 15) {
      suggestions.add('Il piano ha poca varietà di alimenti. Prova ad includere più alimenti diversi per ottenere un migliore apporto di nutrienti.');
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('Il piano dietetico è ben bilanciato e adatto alle tue esigenze.');
    }
    
    return suggestions;
  }

  @override
  Future<WeeklyDietPlan> generatePersonalizedDietPlan(UserProfile userProfile, {int weeks = 1}) async {
    print('Generazione piano dietetico personalizzato per ${userProfile.id}');
    
    // Utilizziamo il generatore di diete ottimizzato esistente
    final dietGenerator = await OptimizedDietGeneratorService.getInstance();
    
    // Genera il piano dietetico
    final dietPlan = await dietGenerator.generateWeeklyDietPlan(userProfile, weeks: weeks);
    
    // In una implementazione più avanzata, qui potremmo personalizzare ulteriormente
    // il piano in base alle preferenze apprese
    
    return dietPlan;
  }

  @override
  Future<List<FoodRecommendation>> getSimilarFoods(Food food, int limit) async {
    final allFoods = await _foodDatabaseService.getAllFoods();
    
    // Filtra per categoria
    final similarFoods = allFoods.where((f) {
      if (f.id == food.id) return false; // Escludi l'alimento stesso
      
      // Controlla se condividono almeno una categoria
      return f.categories.any((cat) => food.categories.contains(cat));
    }).toList();
    
    // Calcola la similarità
    final scoredFoods = similarFoods.map((f) {
      double score = 0.0;
      
      // Similarità per categoria
      final sharedCategories = f.categories.where((cat) => food.categories.contains(cat)).length;
      score += sharedCategories / max(f.categories.length, food.categories.length) * 0.4;
      
      // Similarità per macronutrienti
      final proteinDiff = (f.proteins - food.proteins).abs() / max(f.proteins, food.proteins);
      final carbsDiff = (f.carbs - food.carbs).abs() / max(f.carbs, food.carbs);
      final fatsDiff = (f.fats - food.fats).abs() / max(f.fats, food.fats);
      
      score += (1 - (proteinDiff + carbsDiff + fatsDiff) / 3) * 0.6;
      
      return _ScoredFood(food: f, score: score);
    }).toList();
    
    // Ordina per punteggio e limita
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));
    final limitedFoods = scoredFoods.take(limit).toList();
    
    // Converti in raccomandazioni
    return limitedFoods.map((sf) {
      return FoodRecommendation(
        food: sf.food,
        score: sf.score,
        reason: 'Simile a ${food.name} per composizione nutrizionale',
      );
    }).toList();
  }

  @override
  Future<List<FoodRecommendation>> getComplementaryFoods(Food food, int limit) async {
    final allFoods = await _foodDatabaseService.getAllFoods();
    
    // Filtra per categoria complementare
    final complementaryCategories = _getComplementaryCategories(food.categories);
    
    final complementaryFoods = allFoods.where((f) {
      if (f.id == food.id) return false; // Escludi l'alimento stesso
      
      // Controlla se l'alimento ha categorie complementari
      return f.categories.any((cat) => complementaryCategories.contains(cat));
    }).toList();
    
    // Calcola il punteggio di complementarietà
    final scoredFoods = complementaryFoods.map((f) {
      double score = 0.5; // Punteggio base
      
      // Bonus per categorie complementari
      final complementaryCount = f.categories.where((cat) => complementaryCategories.contains(cat)).length;
      score += complementaryCount * 0.1;
      
      // Bonus per tipi di pasto compatibili
      final sharedMealTypes = f.suitableForMeals.where((type) => food.suitableForMeals.contains(type)).length;
      score += sharedMealTypes * 0.05;
      
      return _ScoredFood(food: f, score: score.clamp(0.0, 1.0));
    }).toList();
    
    // Ordina per punteggio e limita
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));
    final limitedFoods = scoredFoods.take(limit).toList();
    
    // Converti in raccomandazioni
    return limitedFoods.map((sf) {
      return FoodRecommendation(
        food: sf.food,
        score: sf.score,
        reason: 'Si abbina bene con ${food.name}',
      );
    }).toList();
  }

  List<FoodCategory> _getComplementaryCategories(List<FoodCategory> categories) {
    final complementary = <FoodCategory>[];
    
    for (var category in categories) {
      switch (category) {
        case FoodCategory.protein:
          complementary.addAll([FoodCategory.vegetable, FoodCategory.grain]);
          break;
        case FoodCategory.grain:
          complementary.addAll([FoodCategory.protein, FoodCategory.vegetable]);
          break;
        case FoodCategory.vegetable:
          complementary.addAll([FoodCategory.protein, FoodCategory.grain]);
          break;
        case FoodCategory.fruit:
          complementary.addAll([FoodCategory.dairy]);
          break;
        case FoodCategory.dairy:
          complementary.addAll([FoodCategory.fruit, FoodCategory.grain]);
          break;
        case FoodCategory.fat:
          complementary.addAll([FoodCategory.vegetable]);
          break;
        default:
          break;
      }
    }
    
    return complementary.toSet().toList();
  }

  @override
  Future<List<FoodRecommendation>> getAlternativeFoods(Food food, int limit) async {
    final allFoods = await _foodDatabaseService.getAllFoods();
    
    // Filtra per categoria simile
    final alternativeFoods = allFoods.where((f) {
      if (f.id == food.id) return false; // Escludi l'alimento stesso
      
      // Controlla se condividono almeno una categoria
      return f.categories.any((cat) => food.categories.contains(cat));
    }).toList();
    
    // Calcola il punteggio di alternativa
    final scoredFoods = alternativeFoods.map((f) {
      double score = 0.5; // Punteggio base
      
      // Similarità per categoria
      final sharedCategories = f.categories.where((cat) => food.categories.contains(cat)).length;
      score += sharedCategories / max(f.categories.length, food.categories.length) * 0.3;
      
      // Similarità per calorie
      final calorieDiff = (f.calories - food.calories).abs() / max(f.calories, food.calories);
      score += (1 - calorieDiff) * 0.2;
      
      // Similarità per macronutrienti principali
      final proteinDiff = (f.proteins - food.proteins).abs() / max(f.proteins, food.proteins);
      final carbsDiff = (f.carbs - food.carbs).abs() / max(f.carbs, food.carbs);
      final fatsDiff = (f.fats - food.fats).abs() / max(f.fats, food.fats);
      
      score += (1 - (proteinDiff + carbsDiff + fatsDiff) / 3) * 0.5;
      
      return _ScoredFood(food: f, score: score.clamp(0.0, 1.0));
    }).toList();
    
    // Ordina per punteggio e limita
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));
    final limitedFoods = scoredFoods.take(limit).toList();
    
    // Converti in raccomandazioni
    return limitedFoods.map((sf) {
      return FoodRecommendation(
        food: sf.food,
        score: sf.score,
        reason: 'Alternativa a ${food.name} con profilo nutrizionale simile',
      );
    }).toList();
  }

  @override
  Future<List<LearnedPreference>> getLearnedPreferences(String userId) async {
    if (!_userPreferencesCache.containsKey(userId)) {
      return [];
    }
    
    return _userPreferencesCache[userId]!;
  }

  @override
  Future<Map<String, dynamic>> getAIStats() async {
    int totalPreferences = 0;
    int totalUsers = _userPreferencesCache.length;
    
    for (var userPrefs in _userPreferencesCache.values) {
      totalPreferences += userPrefs.length;
    }
    
    return {
      'totalUsers': totalUsers,
      'totalPreferences': totalPreferences,
      'averagePreferencesPerUser': totalUsers > 0 ? totalPreferences / totalUsers : 0,
      'isInitialized': _isInitialized,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
}

/// Classe di supporto per gli alimenti con punteggio
class _ScoredFood {
  final Food food;
  final double score;
  
  _ScoredFood({
    required this.food,
    required this.score,
  });
}
