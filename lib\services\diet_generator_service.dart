import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../data/food_database.dart';
import 'precision_food_selector.dart';
import 'food_safety_service.dart';
import 'athletic_food_selector_service.dart';
import 'meal_appropriateness_validator.dart';
import '../data/athletic_italian_proteins.dart';

class DietGeneratorService {
  final FoodDatabase _foodDatabase;
  final Uuid _uuid = const Uuid();

  DietGeneratorService(this._foodDatabase);

  // Singleton pattern
  static DietGeneratorService? _instance;

  static Future<DietGeneratorService> getInstance() async {
    if (_instance == null) {
      final foodDatabase = FoodDatabase();
      await foodDatabase.resetDatabase();
      _instance = DietGeneratorService(foodDatabase);
    }
    return _instance!;
  }

  // Genera un piano dietetico settimanale completo
  Future<WeeklyDietPlan> generateWeeklyDietPlan(UserProfile userProfile) async {
    print('Iniziando la generazione del piano dietetico...');

    // Assicurati che il database degli alimenti sia inizializzato
    await _foodDatabase.resetDatabase();

    // Verifica che il database contenga alimenti
    final allFoods = await _foodDatabase.getAllFoods();
    print('Numero di alimenti nel database: ${allFoods.length}');

    if (allFoods.isEmpty) {
      print('ERRORE: Il database degli alimenti è vuoto!');
      throw Exception('Il database degli alimenti è vuoto. Impossibile generare un piano dietetico.');
    }

    // Calcola l'obiettivo calorico e la distribuzione dei macronutrienti
    final calorieTarget = userProfile.calculateCalorieTarget();
    final macroGrams = userProfile.calculateMacroGrams();

    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivo macronutrienti: $macroGrams');

    // Crea un piano settimanale vuoto
    final startDate = _getStartOfWeek(DateTime.now());
    final weeklyPlan = WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano Settimanale ${startDate.day}/${startDate.month}/${startDate.year}',
      startDate: _formatDate(startDate),
      dailyPlans: [],
      userProfile: userProfile,
    );

    // Genera un piano per ogni giorno della settimana
    WeeklyDietPlan updatedWeeklyPlan = weeklyPlan;

    for (int i = 0; i < 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dailyPlan = await generateDailyDietPlan(
        userProfile,
        _formatDate(date),
        calorieTarget,
        macroGrams,
      );

      updatedWeeklyPlan = updatedWeeklyPlan.updateDailyPlan(dailyPlan);
    }

    return updatedWeeklyPlan;
  }

  // Genera un piano dietetico giornaliero
  Future<DailyDietPlan> generateDailyDietPlan(
    UserProfile userProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
  ) async {
    print('Generazione piano giornaliero per la data: $date');
    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivi macronutrienti: $macroTargets');

    // Verifica che la mappa macroTargets contenga tutte le chiavi necessarie
    if (macroTargets.isEmpty ||
        !macroTargets.containsKey('proteins') ||
        !macroTargets.containsKey('carbs') ||
        !macroTargets.containsKey('fats')) {
      print('ERRORE: La mappa macroTargets non contiene tutte le chiavi necessarie: $macroTargets');

      // Usa valori predefiniti
      macroTargets = {
        'proteins': 50, // Valore predefinito
        'carbs': 100,   // Valore predefinito
        'fats': 30,     // Valore predefinito
      };

      print('Usando valori predefiniti per macroTargets: $macroTargets');
    }

    // Determina il numero di pasti in base alle preferenze dell'utente
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Distribuisci le calorie tra i pasti
    final mealCalorieDistribution = _distributeMealCalories(calorieTarget, mealsPerDay);
    print('Distribuzione calorie per pasto: $mealCalorieDistribution');

    // Distribuisci i macronutrienti tra i pasti
    final mealMacroDistribution = _distributeMealMacros(macroTargets, mealsPerDay);
    print('Distribuzione macronutrienti per pasto: $mealMacroDistribution');

    // Genera i pasti
    final meals = <PlannedMeal>[];

    // Orari predefiniti per i pasti
    final mealTimes = _getMealTimes(mealsPerDay);

    // Genera ogni pasto
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final mealCalories = mealCalorieDistribution[i];
      final mealMacros = mealMacroDistribution[i];

      print('Generazione pasto $i: tipo=$mealType, calorie=$mealCalories, macros=$mealMacros');

      final meal = await _generateMeal(
        userProfile,
        mealType,
        mealCalories,
        mealMacros,
        mealTimes[i],
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // Verifica e correggi il piano giornaliero per garantire la precisione
    return _validateAndCorrectDailyPlan(dailyPlan, userProfile);
  }

  // Genera un singolo pasto
  Future<PlannedMeal> _generateMeal(
    UserProfile userProfile,
    String mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    String time,
  ) async {
    print('Generazione pasto di tipo $mealType con obiettivo $targetCalories kcal e macros $targetMacros');

    // Verifica che la mappa targetMacros contenga tutte le chiavi necessarie
    if (targetMacros.isEmpty ||
        !targetMacros.containsKey('proteins') ||
        !targetMacros.containsKey('carbs') ||
        !targetMacros.containsKey('fats')) {
      print('ERRORE: La mappa targetMacros non contiene tutte le chiavi necessarie: $targetMacros');

      // Usa valori predefiniti
      targetMacros = {
        'proteins': (targetCalories * 0.3 / 4).round(), // 30% proteine
        'carbs': (targetCalories * 0.4 / 4).round(),    // 40% carboidrati
        'fats': (targetCalories * 0.3 / 9).round(),     // 30% grassi
      };

      print('Usando valori predefiniti per targetMacros: $targetMacros');
    }

    // Utilizza il selettore di precisione per selezionare gli alimenti
    final foodSelector = await PrecisionFoodSelector.getInstance();
    final selectedFoods = await foodSelector.selectFoodsForMeal(
      userProfile: userProfile,
      mealType: mealType,
      targetCalories: targetCalories,
      targetMacros: targetMacros,
      forDate: DateTime.tryParse(userProfile.id) ?? DateTime.now(), // Usa una data per il tracking della varietà
    );

    print('Selezionati ${selectedFoods.length} alimenti per il pasto di tipo $mealType');

    // Crea il pasto
    return PlannedMeal(
      id: _uuid.v4(),
      name: _getMealName(mealType),
      type: mealType,
      foods: selectedFoods,
      time: time,
    );
  }

  // Filtra gli alimenti adatti in base alle preferenze dell'utente e al tipo di pasto
  Future<List<Food>> _filterSuitableFoods(UserProfile userProfile, String mealType) async {
    print('Filtrando alimenti per tipo di pasto: $mealType');

    // Ottieni tutti gli alimenti dal database
    final allFoods = await _foodDatabase.getAllFoods();

    // Aggiungi alimenti proteici italiani per atleti se necessario
    final enhancedFoods = _enhanceFoodsForAthletes(allFoods, userProfile);
    print('Numero totale di alimenti (inclusi quelli atletici): ${enhancedFoods.length}');

    // Se non ci sono alimenti nel database, restituisci una lista vuota
    if (enhancedFoods.isEmpty) {
      print('ERRORE: Database alimenti vuoto');
      // Prova a reinizializzare il database
      await _foodDatabase.resetDatabase();

      // Verifica nuovamente
      final newFoods = await _foodDatabase.getAllFoods();
      final newEnhancedFoods = _enhanceFoodsForAthletes(newFoods, userProfile);
      print('Dopo reinizializzazione, numero di alimenti: ${newEnhancedFoods.length}');

      if (newEnhancedFoods.isEmpty) {
        print('ERRORE CRITICO: Impossibile inizializzare il database degli alimenti');
        throw Exception('Impossibile inizializzare il database degli alimenti');
      }

      return newEnhancedFoods.where((food) => food.suitableForMeals.contains(MealType.values.firstWhere(
        (e) => e.toString().split('.').last == mealType,
        orElse: () => MealType.values.first,
      ))).toList();
    }

    // Filtra gli alimenti in base al tipo di pasto e applica la selezione atletica
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );

    // STEP 1: Filtra per tipo di pasto (basic filtering)
    final basicMealTypeFoods = enhancedFoods.where((food) => food.suitableForMeals.contains(mealTypeEnum)).toList();

    // STEP 2: APPLICA VALIDAZIONE APPROPRIATEZZA CULTURALE (CRITICO!)
    final appropriateFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      basicMealTypeFoods,
      mealTypeEnum,
    );

    print('Alimenti base per $mealType: ${basicMealTypeFoods.length}');
    print('Alimenti appropriati dopo validazione culturale: ${appropriateFoods.length}');

    // STEP 3: Applica selezione atletica sui cibi appropriati
    final mealTypeFoods = AthleticFoodSelectorService.selectHighProteinItalianFoods(
      appropriateFoods,
      userProfile,
      mealTypeEnum,
    );

    print('Alimenti finali per il tipo di pasto $mealType (con selezione atletica): ${mealTypeFoods.length}');

    // Filtra gli alimenti in base al tipo di dieta
    final dietTypeFoods = mealTypeFoods.where((food) {
      switch (userProfile.dietType) {
        case DietType.vegetarian:
          return food.isVegetarian;
        case DietType.vegan:
          return food.isVegan;
        case DietType.pescatarian:
          return food.isVegetarian || food.categories.contains(FoodCategory.protein);
        case DietType.keto:
          return food.carbs <= 5; // Esempio semplificato per dieta keto
        case DietType.paleo:
          return !food.categories.contains(FoodCategory.grain) &&
                 !food.categories.contains(FoodCategory.dairy);
        case DietType.omnivore:
        default:
          return true;
      }
    }).toList();

    // Se non ci sono alimenti adatti per questo tipo di dieta, usa tutti gli alimenti del tipo di pasto
    if (dietTypeFoods.isEmpty) {
      print('Attenzione: Nessun alimento trovato per il tipo di dieta ${userProfile.dietType}. Uso tutti gli alimenti per il tipo di pasto $mealType');
      return mealTypeFoods;
    }

    // Filtra gli alimenti in base alle allergie
    final nonAllergenicFoods = dietTypeFoods.where((food) {
      for (var allergen in userProfile.allergies) {
        if (food.allergens.contains(allergen.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Se non ci sono alimenti senza allergeni, usa tutti gli alimenti del tipo di dieta
    if (nonAllergenicFoods.isEmpty && userProfile.allergies.isNotEmpty) {
      print('Attenzione: Nessun alimento trovato senza gli allergeni specificati. Ignoro le allergie.');
      return dietTypeFoods;
    }

    // Filtra gli alimenti in base ai cibi non graditi
    final preferredFoods = nonAllergenicFoods.where((food) {
      for (var dislikedFood in userProfile.dislikedFoods) {
        if (food.name.toLowerCase().contains(dislikedFood.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // FILTRO DI SICUREZZA ALIMENTARE - CRITICO!
    // Rimuovi tutti gli alimenti non sicuri (es. pollo crudo, carne cruda, ecc.)
    final safeFoods = FoodSafetyService.filterSafeFoods(
      preferredFoods.isEmpty && userProfile.dislikedFoods.isNotEmpty
        ? (nonAllergenicFoods.isEmpty ? dietTypeFoods : nonAllergenicFoods)
        : preferredFoods
    );

    print('Alimenti sicuri dopo filtro di sicurezza: ${safeFoods.length}');

    // Se non ci sono alimenti sicuri, prova a convertire quelli non sicuri in versioni cotte
    if (safeFoods.isEmpty) {
      print('ATTENZIONE: Nessun alimento sicuro trovato, tentativo di conversione in versioni cotte...');
      final originalFoods = preferredFoods.isEmpty && userProfile.dislikedFoods.isNotEmpty
        ? (nonAllergenicFoods.isEmpty ? dietTypeFoods : nonAllergenicFoods)
        : preferredFoods;

      final convertedFoods = <Food>[];
      for (var food in originalFoods) {
        if (FoodSafetyService.requiresCooking(food)) {
          final cookedVersion = FoodSafetyService.getCookedVersion(food);
          if (cookedVersion != null) {
            convertedFoods.add(cookedVersion);
          }
        } else {
          convertedFoods.add(food);
        }
      }

      print('Alimenti convertiti in versioni sicure: ${convertedFoods.length}');
      return convertedFoods;
    }

    return safeFoods;
  }

  // Seleziona gli alimenti per un pasto in modo scientifico e bilanciato
  List<FoodPortion> _selectFoodsForMeal(
    List<Food> suitableFoods,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
  ) {
    print('Selezione alimenti per pasto di tipo $mealType');
    print('Obiettivo calorico: $targetCalories kcal');
    print('Obiettivi macronutrienti: $targetMacros');

    // Se non ci sono alimenti adatti, restituisci una lista vuota
    if (suitableFoods.isEmpty) {
      print('Attenzione: Nessun alimento adatto trovato per il pasto di tipo $mealType');
      return [];
    }

    // Crea una copia della lista per non modificare l'originale
    final availableFoods = List<Food>.from(suitableFoods);
    final selectedFoods = <FoodPortion>[];

    // Calorie e macronutrienti correnti
    int currentCalories = 0;
    int currentProteins = 0;
    int currentCarbs = 0;
    int currentFats = 0;

    // Target macronutrienti
    final targetProtein = targetMacros['proteins'] ?? 0;
    final targetCarbs = targetMacros['carbs'] ?? 0;
    final targetFats = targetMacros['fats'] ?? 0;

    // Organizza gli alimenti per categoria
    final Map<FoodCategory, List<Food>> foodsByCategory = {};
    for (var food in availableFoods) {
      for (var category in food.categories) {
        if (!foodsByCategory.containsKey(category)) {
          foodsByCategory[category] = [];
        }
        foodsByCategory[category]!.add(food);
      }
    }

    // Definisci le categorie di alimenti da includere in base al tipo di pasto
    List<FoodCategory> categoriesToInclude = [];

    if (mealType == 'breakfast') {
      // Colazione: cereali/carboidrati + proteine + frutta
      categoriesToInclude = [
        FoodCategory.grain,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fruit,
      ];
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      // Pranzo/Cena: proteine + carboidrati + verdure
      categoriesToInclude = [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
        FoodCategory.fat,
      ];
    } else if (mealType == 'snack') {
      // Spuntino: frutta + proteine o frutta secca
      categoriesToInclude = [
        FoodCategory.fruit,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fat,
      ];
    }

    print('Categorie da includere: $categoriesToInclude');

    // Seleziona alimenti da ciascuna categoria necessaria
    for (var category in categoriesToInclude) {
      if (foodsByCategory.containsKey(category) && foodsByCategory[category]!.isNotEmpty) {
        // Ordina gli alimenti per contenuto di macronutrienti in base alla categoria
        List<Food> categoryFoods = foodsByCategory[category]!;

        // Ordina gli alimenti in base alla categoria
        switch (category) {
          case FoodCategory.protein:
            // Per le proteine, ordina per contenuto proteico decrescente
            categoryFoods.sort((a, b) => b.proteins.compareTo(a.proteins));
            break;
          case FoodCategory.grain:
            // Per i carboidrati, ordina per contenuto di carboidrati decrescente
            categoryFoods.sort((a, b) => b.carbs.compareTo(a.carbs));
            break;
          case FoodCategory.fat:
            // Per i grassi, ordina per contenuto di grassi decrescente
            categoryFoods.sort((a, b) => b.fats.compareTo(a.fats));
            break;
          default:
            // Per le altre categorie, ordina per calorie crescenti
            categoryFoods.sort((a, b) => a.calories.compareTo(b.calories));
        }

        // Seleziona il miglior alimento dalla categoria
        if (categoryFoods.isNotEmpty) {
          final food = categoryFoods.first;

          // Calcola la porzione ottimale in base al tipo di alimento e al tipo di pasto
          int portion = _calculateOptimalPortion(
            food,
            category,
            mealType,
            targetCalories - currentCalories,
            {
              'proteins': targetProtein - currentProteins,
              'carbs': targetCarbs - currentCarbs,
              'fats': targetFats - currentFats,
            }
          );

          if (portion >= 20) { // Porzione minima significativa
            final foodPortion = FoodPortion(food: food, grams: portion);
            selectedFoods.add(foodPortion);

            // Aggiorna le calorie e i macronutrienti correnti
            currentCalories += foodPortion.calories;
            currentProteins += foodPortion.proteins.round();
            currentCarbs += foodPortion.carbs.round();
            currentFats += foodPortion.fats.round();

            print('Aggiunto ${food.name} ($portion g): ${foodPortion.calories} kcal, ${foodPortion.proteins.round()} g proteine, ${foodPortion.carbs.round()} g carb, ${foodPortion.fats.round()} g grassi');

            // Rimuovi l'alimento selezionato da tutte le categorie
            for (var cat in foodsByCategory.keys) {
              foodsByCategory[cat]!.removeWhere((f) => f.id == food.id);
            }
          }
        }
      }
    }

    // Se non abbiamo raggiunto almeno il 70% dell'obiettivo calorico, aggiungi altri alimenti
    if (currentCalories < targetCalories * 0.7 && availableFoods.isNotEmpty) {
      print('Obiettivo calorico non raggiunto, aggiunta di alimenti supplementari');

      // Ordina gli alimenti rimanenti per calorie per porzione
      availableFoods.sort((a, b) =>
        (a.calories / a.servingSizeGrams).compareTo(b.calories / b.servingSizeGrams));

      // Aggiungi alimenti fino a raggiungere l'obiettivo calorico
      for (var food in availableFoods) {
        // Verifica se l'alimento è già stato selezionato
        if (selectedFoods.any((fp) => fp.food.id == food.id)) {
          continue;
        }

        // Calcola la porzione in base alle calorie rimanenti
        final remainingCalories = targetCalories - currentCalories;
        final caloriesPerGram = food.calories / 100;
        int portion = min((remainingCalories / caloriesPerGram).round(), food.servingSizeGrams);

        // Limita la porzione a un valore ragionevole
        portion = min(portion, 200);

        if (portion >= 20) { // Porzione minima significativa
          final foodPortion = FoodPortion(food: food, grams: portion);
          selectedFoods.add(foodPortion);

          // Aggiorna le calorie e i macronutrienti correnti
          currentCalories += foodPortion.calories;
          currentProteins += foodPortion.proteins.round();
          currentCarbs += foodPortion.carbs.round();
          currentFats += foodPortion.fats.round();

          print('Aggiunto supplementare ${food.name} ($portion g): ${foodPortion.calories} kcal');

          // Se abbiamo raggiunto l'obiettivo calorico, interrompi il ciclo
          if (currentCalories >= targetCalories * 0.8) {
            break;
          }
        }
      }
    }

    // Stampa il riepilogo del pasto
    print('Riepilogo pasto:');
    print('- Calorie: $currentCalories / $targetCalories kcal (${(currentCalories / targetCalories * 100).round()}%)');
    print('- Proteine: $currentProteins / $targetProtein g (${(currentProteins / targetProtein * 100).round()}%)');
    print('- Carboidrati: $currentCarbs / $targetCarbs g (${(currentCarbs / targetCarbs * 100).round()}%)');
    print('- Grassi: $currentFats / $targetFats g (${(currentFats / targetFats * 100).round()}%)');

    return selectedFoods;
  }

  // Calcola la porzione ottimale in base al tipo di alimento e al tipo di pasto
  int _calculateOptimalPortion(
    Food food,
    FoodCategory category,
    String mealType,
    int remainingCalories,
    Map<String, int> remainingMacros
  ) {
    // Porzioni standard per categoria di alimento
    final Map<FoodCategory, int> standardPortions = {
      FoodCategory.protein: 100, // 100g di proteine (carne, pesce, tofu, ecc.)
      FoodCategory.grain: 80,    // 80g di carboidrati (pasta, riso, ecc.)
      FoodCategory.vegetable: 150, // 150g di verdure
      FoodCategory.fruit: 150,   // 150g di frutta
      FoodCategory.dairy: 150,   // 150g/ml di latticini
      FoodCategory.fat: 15,      // 15g di grassi (olio, burro, ecc.)
      FoodCategory.sweet: 50,    // 50g di dolci
      FoodCategory.beverage: 250, // 250ml di bevande
      FoodCategory.mixed: 200,   // 200g di piatti misti
    };

    // Usa la porzione standard se disponibile, altrimenti usa la porzione dell'alimento
    int basePortion = standardPortions[category] ?? food.servingSizeGrams;

    // Adatta la porzione in base al tipo di pasto
    if (mealType == 'breakfast') {
      // Colazione: porzioni standard
    } else if (mealType == 'lunch') {
      // Pranzo: porzioni leggermente più grandi
      basePortion = (basePortion * 1.2).round();
    } else if (mealType == 'dinner') {
      // Cena: porzioni standard
    } else if (mealType == 'snack') {
      // Spuntino: porzioni più piccole
      basePortion = (basePortion * 0.7).round();
    }

    // Calcola le calorie per la porzione base
    final caloriesForBasePortion = (food.calories / 100 * basePortion).round();

    // Se le calorie della porzione base superano le calorie rimanenti, riduci la porzione
    if (caloriesForBasePortion > remainingCalories && remainingCalories > 0) {
      basePortion = (basePortion * remainingCalories / caloriesForBasePortion).round();
    }

    // Limita la porzione a un valore ragionevole
    basePortion = min(basePortion, 300); // Massimo 300g
    basePortion = max(basePortion, 20);  // Minimo 20g

    return basePortion;
  }

  // Distribuisci le calorie tra i pasti in modo scientificamente accurato
  List<int> _distributeMealCalories(int totalCalories, int mealsPerDay) {
    final distribution = <int>[];

    print('Distribuzione di $totalCalories kcal in $mealsPerDay pasti');

    // Distribuzione scientifica delle calorie in base al numero di pasti
    // Basata su linee guida nutrizionali e studi sul timing dei pasti
    switch (mealsPerDay) {
      case 3: // 3 pasti principali
        // Distribuzione standard: colazione 20-25%, pranzo 35-40%, cena 35-40%
        distribution.add((totalCalories * 0.25).round()); // Colazione (25%)
        distribution.add((totalCalories * 0.40).round()); // Pranzo (40%)
        distribution.add((totalCalories * 0.35).round()); // Cena (35%)
        break;

      case 4: // 3 pasti principali + 1 spuntino
        // Aggiunta di uno spuntino a metà mattina
        distribution.add((totalCalories * 0.25).round()); // Colazione (25%)
        distribution.add((totalCalories * 0.10).round()); // Spuntino mattina (10%)
        distribution.add((totalCalories * 0.35).round()); // Pranzo (35%)
        distribution.add((totalCalories * 0.30).round()); // Cena (30%)
        break;

      case 5: // 3 pasti principali + 2 spuntini
        // Aggiunta di spuntini a metà mattina e metà pomeriggio
        distribution.add((totalCalories * 0.20).round()); // Colazione (20%)
        distribution.add((totalCalories * 0.10).round()); // Spuntino mattina (10%)
        distribution.add((totalCalories * 0.30).round()); // Pranzo (30%)
        distribution.add((totalCalories * 0.15).round()); // Spuntino pomeriggio (15%)
        distribution.add((totalCalories * 0.25).round()); // Cena (25%)
        break;

      case 6: // 3 pasti principali + 3 spuntini
        // Aggiunta di spuntini a metà mattina, metà pomeriggio e sera
        distribution.add((totalCalories * 0.20).round()); // Colazione (20%)
        distribution.add((totalCalories * 0.10).round()); // Spuntino mattina (10%)
        distribution.add((totalCalories * 0.25).round()); // Pranzo (25%)
        distribution.add((totalCalories * 0.15).round()); // Spuntino pomeriggio (15%)
        distribution.add((totalCalories * 0.20).round()); // Cena (20%)
        distribution.add((totalCalories * 0.10).round()); // Spuntino sera (10%)
        break;

      default: // Distribuzione personalizzata per altri numeri di pasti
        if (mealsPerDay > 6) {
          // Per più di 6 pasti, distribuzione tipo "frequent feeding"
          // Pasti principali leggermente più grandi, spuntini più piccoli
          final double mainMealPercentage = 0.20; // 20% per pasto principale
          final double snackPercentage = 0.10; // 10% per spuntino

          // Identifica quali sono i pasti principali (colazione, pranzo, cena)
          final mainMealIndices = [0]; // Colazione è sempre il primo pasto

          // Pranzo è a circa 1/3 della giornata
          final lunchIndex = (mealsPerDay / 3).round();
          mainMealIndices.add(lunchIndex);

          // Cena è a circa 2/3 della giornata
          final dinnerIndex = (mealsPerDay * 2 / 3).round();
          mainMealIndices.add(dinnerIndex);

          // Distribuisci le calorie
          double totalPercentage = 0.0;
          for (int i = 0; i < mealsPerDay; i++) {
            if (mainMealIndices.contains(i)) {
              distribution.add((totalCalories * mainMealPercentage).round());
              totalPercentage += mainMealPercentage;
            } else {
              distribution.add((totalCalories * snackPercentage).round());
              totalPercentage += snackPercentage;
            }
          }

          // Aggiusta l'ultimo pasto per assicurarsi che la somma sia esattamente totalCalories
          if (totalPercentage != 1.0 && distribution.isNotEmpty) {
            final adjustment = (totalCalories - distribution.fold(0, (sum, cal) => sum + cal)).round();
            distribution[distribution.length - 1] += adjustment;
          }
        } else {
          // Per meno di 3 pasti, distribuzione uniforme
          final caloriesPerMeal = (totalCalories / mealsPerDay).round();
          for (int i = 0; i < mealsPerDay; i++) {
            distribution.add(caloriesPerMeal);
          }
        }
    }

    // Verifica che la somma delle calorie distribuite sia uguale al totale
    final totalDistributed = distribution.fold(0, (sum, cal) => sum + cal);
    if (totalDistributed != totalCalories) {
      final adjustment = (totalCalories - totalDistributed).round();
      // Aggiungi o sottrai la differenza dall'ultimo pasto
      distribution[distribution.length - 1] += adjustment;
    }

    // Stampa la distribuzione
    for (int i = 0; i < distribution.length; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      print('Pasto ${i+1} ($mealType): ${distribution[i]} kcal (${(distribution[i] / totalCalories * 100).round()}%)');
    }

    return distribution;
  }

  // Distribuisci i macronutrienti tra i pasti in modo scientificamente accurato
  List<Map<String, int>> _distributeMealMacros(Map<String, int> totalMacros, int mealsPerDay) {
    final distribution = <Map<String, int>>[];

    print('Distribuzione macronutrienti - totalMacros: $totalMacros');

    // Verifica che la mappa contenga tutte le chiavi necessarie
    if (totalMacros.isEmpty ||
        !totalMacros.containsKey('proteins') ||
        !totalMacros.containsKey('carbs') ||
        !totalMacros.containsKey('fats')) {
      print('ERRORE: La mappa totalMacros non contiene tutte le chiavi necessarie: $totalMacros');

      // Usa valori predefiniti basati su una dieta standard
      final proteins = 75; // Valore predefinito (circa 1.2g/kg per 62.5kg)
      final carbs = 250;   // Valore predefinito (circa 4g/kg per 62.5kg)
      final fats = 55;     // Valore predefinito (circa 0.8g/kg per 62.5kg)

      // Distribuisci uniformemente
      for (int i = 0; i < mealsPerDay; i++) {
        distribution.add({
          'proteins': (proteins / mealsPerDay).round(),
          'carbs': (carbs / mealsPerDay).round(),
          'fats': (fats / mealsPerDay).round(),
        });
      }

      return distribution;
    }

    // Ottieni le percentuali di distribuzione delle calorie
    final calorieDistribution = _distributeMealCalories(100, mealsPerDay);

    // Estrai i valori totali dei macronutrienti
    final totalProteins = totalMacros['proteins'] ?? 0;
    final totalCarbs = totalMacros['carbs'] ?? 0;
    final totalFats = totalMacros['fats'] ?? 0;

    print('Macronutrienti totali: $totalProteins g proteine, $totalCarbs g carboidrati, $totalFats g grassi');

    // Distribuisci i macronutrienti in base al tipo di pasto e alle percentuali di calorie
    int remainingProteins = totalProteins;
    int remainingCarbs = totalCarbs;
    int remainingFats = totalFats;

    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final caloriePercentage = calorieDistribution[i] / 100;

      // Distribuisci i macronutrienti in base al tipo di pasto
      int mealProteins = 0;
      int mealCarbs = 0;
      int mealFats = 0;

      if (mealType == 'breakfast') {
        // Colazione: più carboidrati, meno grassi
        mealProteins = (totalProteins * caloriePercentage * 0.9).round();  // 90% della quota proporzionale
        mealCarbs = (totalCarbs * caloriePercentage * 1.2).round();        // 120% della quota proporzionale
        mealFats = (totalFats * caloriePercentage * 0.8).round();          // 80% della quota proporzionale
      } else if (mealType == 'lunch') {
        // Pranzo: bilanciato, leggermente più carboidrati
        mealProteins = (totalProteins * caloriePercentage * 1.0).round();  // 100% della quota proporzionale
        mealCarbs = (totalCarbs * caloriePercentage * 1.1).round();        // 110% della quota proporzionale
        mealFats = (totalFats * caloriePercentage * 0.9).round();          // 90% della quota proporzionale
      } else if (mealType == 'dinner') {
        // Cena: più proteine, meno carboidrati
        mealProteins = (totalProteins * caloriePercentage * 1.2).round();  // 120% della quota proporzionale
        mealCarbs = (totalCarbs * caloriePercentage * 0.8).round();        // 80% della quota proporzionale
        mealFats = (totalFats * caloriePercentage * 1.0).round();          // 100% della quota proporzionale
      } else if (mealType == 'snack') {
        // Spuntino: bilanciato, leggermente più carboidrati per energia
        mealProteins = (totalProteins * caloriePercentage * 0.8).round();  // 80% della quota proporzionale
        mealCarbs = (totalCarbs * caloriePercentage * 1.2).round();        // 120% della quota proporzionale
        mealFats = (totalFats * caloriePercentage * 1.0).round();          // 100% della quota proporzionale
      }

      // Assicurati di non superare i valori rimanenti
      mealProteins = min(mealProteins, remainingProteins);
      mealCarbs = min(mealCarbs, remainingCarbs);
      mealFats = min(mealFats, remainingFats);

      // Aggiorna i valori rimanenti
      remainingProteins -= mealProteins;
      remainingCarbs -= mealCarbs;
      remainingFats -= mealFats;

      // Aggiungi i macronutrienti al pasto
      distribution.add({
        'proteins': mealProteins,
        'carbs': mealCarbs,
        'fats': mealFats,
      });

      print('Pasto ${i+1} ($mealType): $mealProteins g proteine, $mealCarbs g carboidrati, $mealFats g grassi');
    }

    // Se ci sono macronutrienti rimanenti, distribuiscili nell'ultimo pasto
    if (remainingProteins > 0 || remainingCarbs > 0 || remainingFats > 0) {
      print('Macronutrienti rimanenti: $remainingProteins g proteine, $remainingCarbs g carboidrati, $remainingFats g grassi');

      final lastIndex = distribution.length - 1;
      if (lastIndex >= 0) {
        final lastMeal = distribution[lastIndex];
        distribution[lastIndex] = {
          'proteins': lastMeal['proteins']! + remainingProteins,
          'carbs': lastMeal['carbs']! + remainingCarbs,
          'fats': lastMeal['fats']! + remainingFats,
        };

        print('Aggiornato ultimo pasto: ${distribution[lastIndex]['proteins']} g proteine, ${distribution[lastIndex]['carbs']} g carboidrati, ${distribution[lastIndex]['fats']} g grassi');
      }
    }

    // Verifica che la somma dei macronutrienti distribuiti sia uguale al totale
    final distributedProteins = distribution.fold(0, (sum, meal) => sum + meal['proteins']!);
    final distributedCarbs = distribution.fold(0, (sum, meal) => sum + meal['carbs']!);
    final distributedFats = distribution.fold(0, (sum, meal) => sum + meal['fats']!);

    print('Macronutrienti distribuiti: $distributedProteins g proteine, $distributedCarbs g carboidrati, $distributedFats g grassi');

    return distribution;
  }

  // Ottieni il tipo di pasto in base all'indice
  String _getMealType(int index, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3: // 3 pasti principali
        switch (index) {
          case 0: return 'breakfast';
          case 1: return 'lunch';
          case 2: return 'dinner';
          default: return 'snack';
        }
      case 4: // 3 pasti principali + 1 spuntino
        switch (index) {
          case 0: return 'breakfast';
          case 1: return 'snack';
          case 2: return 'lunch';
          case 3: return 'dinner';
          default: return 'snack';
        }
      case 5: // 3 pasti principali + 2 spuntini
        switch (index) {
          case 0: return 'breakfast';
          case 1: return 'snack';
          case 2: return 'lunch';
          case 3: return 'snack';
          case 4: return 'dinner';
          default: return 'snack';
        }
      case 6: // 3 pasti principali + 3 spuntini
        switch (index) {
          case 0: return 'breakfast';
          case 1: return 'snack';
          case 2: return 'lunch';
          case 3: return 'snack';
          case 4: return 'dinner';
          case 5: return 'snack';
          default: return 'snack';
        }
      default:
        return index == 0 ? 'breakfast' :
               index == mealsPerDay - 1 ? 'dinner' :
               index == mealsPerDay ~/ 2 ? 'lunch' :
               'snack';
    }
  }

  // Ottieni gli orari predefiniti per i pasti
  List<String> _getMealTimes(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3: // 3 pasti principali
        return ['08:00', '13:00', '20:00'];
      case 4: // 3 pasti principali + 1 spuntino
        return ['08:00', '10:30', '13:00', '20:00'];
      case 5: // 3 pasti principali + 2 spuntini
        return ['08:00', '10:30', '13:00', '16:30', '20:00'];
      case 6: // 3 pasti principali + 3 spuntini
        return ['08:00', '10:30', '13:00', '16:30', '20:00', '22:00'];
      default:
        final times = <String>[];
        final startHour = 8;
        final hoursPerMeal = 14 / (mealsPerDay - 1);

        for (int i = 0; i < mealsPerDay; i++) {
          final hour = (startHour + i * hoursPerMeal).floor();
          final minute = ((startHour + i * hoursPerMeal) % 1 * 60).round();
          times.add('${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}');
        }

        return times;
    }
  }

  // Ottieni il nome del pasto in base al tipo
  String _getMealName(String mealType) {
    if (mealType == 'breakfast') {
      return 'Colazione';
    } else if (mealType == 'lunch') {
      return 'Pranzo';
    } else if (mealType == 'dinner') {
      return 'Cena';
    } else if (mealType == 'snack') {
      return 'Spuntino';
    } else {
      return 'Pasto';
    }
  }

  // Ottieni l'inizio della settimana (lunedì)
  DateTime _getStartOfWeek(DateTime date) {
    final day = date.weekday;
    return date.subtract(Duration(days: day - 1));
  }

  // Formatta una data in formato YYYY-MM-DD
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Verifica e correggi il piano dietetico giornaliero
  Future<DailyDietPlan> _validateAndCorrectDailyPlan(DailyDietPlan dailyPlan, UserProfile userProfile) async {
    print('Validazione e correzione del piano dietetico giornaliero');

    // Calcola le calorie e i macronutrienti totali del piano
    int totalCalories = 0;
    int totalProteins = 0;
    int totalCarbs = 0;
    int totalFats = 0;

    for (var meal in dailyPlan.meals) {
      totalCalories += meal.totalCalories;
      totalProteins += meal.totalMacros['proteins']!.round();
      totalCarbs += meal.totalMacros['carbs']!.round();
      totalFats += meal.totalMacros['fats']!.round();
    }

    // Calcola le deviazioni dai target
    final calorieDeviation = totalCalories - dailyPlan.calorieTarget;
    final proteinDeviation = totalProteins - dailyPlan.macroTargets['proteins']!;
    final carbDeviation = totalCarbs - dailyPlan.macroTargets['carbs']!;
    final fatDeviation = totalFats - dailyPlan.macroTargets['fats']!;

    print('Deviazioni dai target:');
    final caloriePercent = dailyPlan.calorieTarget > 0 ?
        (calorieDeviation / dailyPlan.calorieTarget * 100).round() : 0;
    final proteinPercent = dailyPlan.macroTargets['proteins']! > 0 ?
        (proteinDeviation / dailyPlan.macroTargets['proteins']! * 100).round() : 0;
    final carbPercent = dailyPlan.macroTargets['carbs']! > 0 ?
        (carbDeviation / dailyPlan.macroTargets['carbs']! * 100).round() : 0;
    final fatPercent = dailyPlan.macroTargets['fats']! > 0 ?
        (fatDeviation / dailyPlan.macroTargets['fats']! * 100).round() : 0;

    print('- Calorie: $calorieDeviation kcal ($caloriePercent%)');
    print('- Proteine: $proteinDeviation g ($proteinPercent%)');
    print('- Carboidrati: $carbDeviation g ($carbPercent%)');
    print('- Grassi: $fatDeviation g ($fatPercent%)');

    // Verifica se le deviazioni sono accettabili
    final calorieDeviationPercent = dailyPlan.calorieTarget > 0 ?
        (calorieDeviation / dailyPlan.calorieTarget).abs() : 0.0;
    final proteinDeviationPercent = dailyPlan.macroTargets['proteins']! > 0 ?
        (proteinDeviation / dailyPlan.macroTargets['proteins']!).abs() : 0.0;
    final carbDeviationPercent = dailyPlan.macroTargets['carbs']! > 0 ?
        (carbDeviation / dailyPlan.macroTargets['carbs']!).abs() : 0.0;
    final fatDeviationPercent = dailyPlan.macroTargets['fats']! > 0 ?
        (fatDeviation / dailyPlan.macroTargets['fats']!).abs() : 0.0;

    // Tolleranze accettabili
    const calorieTolerancePercent = 0.05; // 5% di tolleranza per le calorie
    const macroTolerancePercent = 0.10; // 10% di tolleranza per i macronutrienti

    // Se le deviazioni sono accettabili, restituisci il piano originale
    if (calorieDeviationPercent <= calorieTolerancePercent &&
        proteinDeviationPercent <= macroTolerancePercent &&
        carbDeviationPercent <= macroTolerancePercent &&
        fatDeviationPercent <= macroTolerancePercent) {
      print('Piano dietetico giornaliero valido, deviazioni entro le tolleranze accettabili');
      return dailyPlan;
    }

    // Altrimenti, correggi il piano
    print('Correzione del piano dietetico giornaliero necessaria');

    // Utilizza il selettore di precisione per aggiungere alimenti correttivi
    final foodSelector = await PrecisionFoodSelector.getInstance();

    // Crea una copia delle liste di pasti per non modificare l'originale
    final correctedMeals = <PlannedMeal>[];

    // Correggi ogni pasto
    for (var meal in dailyPlan.meals) {
      // Calcola le correzioni necessarie per questo pasto
      final mealCaloriePercent = totalCalories > 0 ? meal.totalCalories / totalCalories : 0.0;

      // Calcola le correzioni proporzionali per questo pasto
      final mealCalorieCorrection = (calorieDeviation * mealCaloriePercent * -1).round();
      final mealProteinCorrection = (proteinDeviation * mealCaloriePercent * -1).round();
      final mealCarbCorrection = (carbDeviation * mealCaloriePercent * -1).round();
      final mealFatCorrection = (fatDeviation * mealCaloriePercent * -1).round();

      // Se le correzioni sono trascurabili, mantieni il pasto originale
      if (mealCalorieCorrection.abs() < 50 &&
          mealProteinCorrection.abs() < 5 &&
          mealCarbCorrection.abs() < 5 &&
          mealFatCorrection.abs() < 3) {
        correctedMeals.add(meal);
        continue;
      }

      // Altrimenti, aggiungi alimenti correttivi
      final correctionTargets = {
        'proteins': mealProteinCorrection,
        'carbs': mealCarbCorrection,
        'fats': mealFatCorrection,
      };

      // Seleziona alimenti correttivi
      final correctionFoods = await foodSelector.selectFoodsForMeal(
        userProfile: userProfile,
        mealType: meal.type,
        targetCalories: mealCalorieCorrection,
        targetMacros: correctionTargets,
      );

      // Crea un nuovo pasto con gli alimenti originali più quelli correttivi
      final correctedFoods = [...meal.foods, ...correctionFoods];

      // Crea il pasto corretto
      final correctedMeal = PlannedMeal(
        id: meal.id,
        name: meal.name,
        type: meal.type,
        foods: correctedFoods,
        time: meal.time,
      );

      correctedMeals.add(correctedMeal);
    }

    // Crea il piano corretto
    final correctedPlan = DailyDietPlan(
      date: dailyPlan.date,
      meals: correctedMeals,
      calorieTarget: dailyPlan.calorieTarget,
      macroTargets: dailyPlan.macroTargets,
    );

    // Verifica il piano corretto
    int correctedTotalCalories = 0;
    int correctedTotalProteins = 0;
    int correctedTotalCarbs = 0;
    int correctedTotalFats = 0;

    for (var meal in correctedPlan.meals) {
      correctedTotalCalories += meal.totalCalories;
      correctedTotalProteins += meal.totalMacros['proteins']!.round();
      correctedTotalCarbs += meal.totalMacros['carbs']!.round();
      correctedTotalFats += meal.totalMacros['fats']!.round();
    }

    print('Piano corretto:');
    final correctedCaloriePercent = dailyPlan.calorieTarget > 0 ?
        (correctedTotalCalories / dailyPlan.calorieTarget * 100).round() : 0;
    final correctedProteinPercent = dailyPlan.macroTargets['proteins']! > 0 ?
        (correctedTotalProteins / dailyPlan.macroTargets['proteins']! * 100).round() : 0;
    final correctedCarbPercent = dailyPlan.macroTargets['carbs']! > 0 ?
        (correctedTotalCarbs / dailyPlan.macroTargets['carbs']! * 100).round() : 0;
    final correctedFatPercent = dailyPlan.macroTargets['fats']! > 0 ?
        (correctedTotalFats / dailyPlan.macroTargets['fats']! * 100).round() : 0;

    print('- Calorie: $correctedTotalCalories / ${dailyPlan.calorieTarget} kcal ($correctedCaloriePercent%)');
    print('- Proteine: $correctedTotalProteins / ${dailyPlan.macroTargets['proteins']} g ($correctedProteinPercent%)');
    print('- Carboidrati: $correctedTotalCarbs / ${dailyPlan.macroTargets['carbs']} g ($correctedCarbPercent%)');
    print('- Grassi: $correctedTotalFats / ${dailyPlan.macroTargets['fats']} g ($correctedFatPercent%)');

    return correctedPlan;
  }

  /// Potenzia il database degli alimenti con proteine italiane specifiche per atleti
  List<Food> _enhanceFoodsForAthletes(List<Food> baseFoods, UserProfile userProfile) {
    // Se l'utente non è un atleta, restituisci il database base
    if (!_isAthleteProfile(userProfile)) {
      return baseFoods;
    }

    print('Utente atleta rilevato, aggiunta di alimenti proteici italiani specializzati');

    // Crea una copia della lista base
    final enhancedFoods = List<Food>.from(baseFoods);

    // Aggiungi gli alimenti proteici italiani per atleti
    final athleticProteins = AthleticItalianProteins.getAthleticProteins();

    // Evita duplicati controllando gli ID
    final existingIds = enhancedFoods.map((food) => food.id).toSet();

    for (final protein in athleticProteins) {
      if (!existingIds.contains(protein.id)) {
        enhancedFoods.add(protein);
        existingIds.add(protein.id);
      }
    }

    print('Aggiunti ${athleticProteins.length} alimenti proteici italiani per atleti');
    return enhancedFoods;
  }

  /// Determina se il profilo utente è quello di un atleta
  bool _isAthleteProfile(UserProfile userProfile) {
    return userProfile.activityLevel == ActivityLevel.veryActive ||
           userProfile.activityLevel == ActivityLevel.extremelyActive;
  }
}
