import 'package:flutter/material.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../services/ultra_advanced_diet_generator.dart';
import '../services/ultra_profile_service.dart';
import '../widgets/detailed_food_table.dart';
import '../widgets/daily_nutrition_summary.dart';
import '../theme/app_theme.dart';

/// SCHERMATA IBRIDA: BACKEND ULTRA-AVANZATO + FRONTEND CLASSICO
/// Combina la potenza del nuovo sistema di profilazione con l'interfaccia dettagliata
class HybridDietScreen extends StatefulWidget {
  const HybridDietScreen({Key? key}) : super(key: key);

  @override
  State<HybridDietScreen> createState() => _HybridDietScreenState();
}

class _HybridDietScreenState extends State<HybridDietScreen> with TickerProviderStateMixin {
  UltraDetailedProfile? _profile;
  DailyDietPlan? _dailyPlan;
  bool _isLoading = true;
  String _errorMessage = '';
  bool _enableRefeedDay = false;
  
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });
    _initializeSystem();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeSystem() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      print('🚀 Inizializzazione sistema ibrido...');

      final profileService = UltraProfileService();
      
      // Carica o crea profilo ultra-dettagliato
      UltraDetailedProfile profile;
      final existing = await profileService.loadUltraProfile();
      
      if (existing != null) {
        profile = existing;
        print('📱 Profilo ultra-dettagliato esistente caricato');
      } else {
        // Crea profilo demo avanzato
        profile = await _createAdvancedDemoProfile(profileService);
        print('🆕 Profilo demo ultra-avanzato creato');
      }

      setState(() {
        _profile = profile;
      });

      // Genera piano con sistema ultra-avanzato
      await _generateHybridPlan();

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore inizializzazione sistema ibrido: $e';
        _isLoading = false;
      });
      print('❌ Errore: $e');
    }
  }

  Future<UltraDetailedProfile> _createAdvancedDemoProfile(UltraProfileService service) async {
    return await service.createUltraProfile(
      name: 'Dr. Staffilano Pro',
      age: 40,
      gender: Gender.male,
      weight: 78.0,
      height: 178.0,
      goal: Goal.maintenance,
      
      // Composizione corporea avanzata
      bodyFatPercentage: 12.0,
      bodyCompositionMethod: BodyCompositionMethod.dexa,
      circumferences: {
        'vita': 82.0,
        'fianchi': 95.0,
        'collo': 38.0,
      },
      
      // Valori ematochimici demo
      bloodValues: BloodValues(
        fastingGlucose: 85.0,
        totalCholesterol: 180.0,
        ldlCholesterol: 100.0,
        hdlCholesterol: 60.0,
        triglycerides: 80.0,
        tsh: 2.1,
        testDate: DateTime.now().subtract(const Duration(days: 30)),
      ),
      
      // Attività fisica dettagliata
      workActivity: WorkActivity.moderate,
      plannedExercises: [
        PlannedExercise(
          name: 'Corsa mattutina',
          type: ExerciseType.cardio,
          durationMinutes: 40,
          frequencyPerWeek: 4,
          intensity: ExerciseIntensity.moderate,
          metValue: 8.5,
        ),
        PlannedExercise(
          name: 'Allenamento pesi',
          type: ExerciseType.strength,
          durationMinutes: 75,
          frequencyPerWeek: 3,
          intensity: ExerciseIntensity.vigorous,
          metValue: 6.5,
        ),
        PlannedExercise(
          name: 'Yoga',
          type: ExerciseType.flexibility,
          durationMinutes: 60,
          frequencyPerWeek: 2,
          intensity: ExerciseIntensity.light,
          metValue: 3.0,
        ),
      ],
      neatLevel: NEATLevel.high,
      
      // Obiettivi gerarchizzati
      primaryGoal: PrimaryGoal.bodyRecomposition,
      secondaryGoals: [
        SecondaryGoal.improveHealth,
        SecondaryGoal.increaseEnergy,
        SecondaryGoal.betterSleep,
      ],
      weightLossDetails: WeightLossDetails(
        targetWeightLossPerWeek: 0.3,
        deficitPercentage: 0.15,
        proteinMultiplier: 2.2,
        preserveLeanMass: true,
      ),
      
      // Preferenze alimentari avanzate
      dietaryRegimen: DietaryRegimen.mediterranean,
      preferredFoods: [
        'Salmone selvaggio',
        'Avocado biologico',
        'Quinoa',
        'Spinaci freschi',
        'Olio extravergine di oliva',
        'Mandorle',
        'Mirtilli',
      ],
      dislikedFoods: [
        'Fegato',
        'Cavolo di Bruxelles',
      ],
      budgetLevel: BudgetLevel.high,
      cookingTime: CookingTime.extended,
      mealsPerDay: 5,
      cookingSkillLevel: CookingSkillLevel.advanced,
      
      // Funzionalità avanzate
      allowCheatMeals: true,
      cheatMealsPerWeek: 1,
      preferOrganicFoods: true,
      preferLocalFoods: true,
      enableCalorieCycling: true,
      enableMealTiming: true,
      hasConsultedDoctor: true,
    );
  }

  Future<void> _generateHybridPlan() async {
    if (_profile == null) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      print('🧠 Generazione piano con sistema ultra-avanzato...');

      final generator = await UltraAdvancedDietGenerator.getInstance();
      final plan = await generator.generateUltraPersonalizedDiet(
        profile: _profile!,
        targetDate: DateTime.now(),
        enableRefeedDay: _enableRefeedDay,
      );

      setState(() {
        _dailyPlan = plan;
        _isLoading = false;
      });

      print('✅ Piano ibrido generato con successo!');

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore generazione piano ibrido: $e';
        _isLoading = false;
      });
      print('❌ Errore generazione: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dr. Staffilano Pro'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: _profile != null && _dailyPlan != null
          ? TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(icon: Icon(Icons.dashboard), text: 'Riepilogo'),
                Tab(icon: Icon(Icons.restaurant_menu), text: 'Pasti Dettagliati'),
                Tab(icon: Icon(Icons.analytics), text: 'Analisi Avanzata'),
              ],
            )
          : null,
        actions: [
          if (_profile != null) ...[
            IconButton(
              icon: Icon(_enableRefeedDay ? Icons.restaurant : Icons.restaurant_outlined),
              tooltip: _enableRefeedDay ? 'Disabilita Refeed Day' : 'Abilita Refeed Day',
              onPressed: () {
                setState(() {
                  _enableRefeedDay = !_enableRefeedDay;
                });
                _generateHybridPlan();
              },
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'Rigenera Piano',
              onPressed: _generateHybridPlan,
            ),
          ],
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
            const SizedBox(height: 16),
            Text(
              'Generazione piano ultra-personalizzato...',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Sistema ibrido in azione 🚀',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                'Errore Sistema Ibrido',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _initializeSystem,
                icon: const Icon(Icons.refresh),
                label: const Text('Riprova'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_profile == null || _dailyPlan == null) {
      return const Center(
        child: Text('Nessun dato disponibile'),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildSummaryTab(),
        _buildDetailedMealsTab(),
        _buildAdvancedAnalysisTab(),
      ],
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // Riepilogo nutrizionale giornaliero
          DailyNutritionSummary(
            dailyPlan: _dailyPlan!,
            profile: _profile!,
          ),
          
          const SizedBox(height: 16),
          
          // Informazioni sistema ultra-avanzato
          _buildSystemInfoCard(),
        ],
      ),
    );
  }

  Widget _buildDetailedMealsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _dailyPlan!.meals.length,
      itemBuilder: (context, index) {
        final meal = _dailyPlan!.meals[index];
        return DetailedFoodTable(
          meal: meal,
          onFoodTapped: (food) {
            _showFoodDetailsDialog(food);
          },
        );
      },
    );
  }

  Widget _buildAdvancedAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analisi Sistema Ultra-Avanzato',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // Validazione nutrizionale
          _buildNutritionalValidationCard(),
          const SizedBox(height: 16),
          
          // Calcoli metabolici
          _buildMetabolicCalculationsCard(),
          const SizedBox(height: 16),
          
          // Personalizzazioni applicate
          _buildPersonalizationCard(),
        ],
      ),
    );
  }

  Widget _buildSystemInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.rocket_launch,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Sistema Ibrido Attivo',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Questo piano è stato generato utilizzando:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            _buildFeatureItem('🧠 Backend Ultra-Avanzato', 'Profilazione dettagliata con 50+ parametri'),
            _buildFeatureItem('📊 Frontend Classico', 'Visualizzazione dettagliata con tabelle e grafici'),
            _buildFeatureItem('🔬 Calcoli Scientifici', 'BMR, TDEE, distribuzione macronutrienti personalizzata'),
            _buildFeatureItem('🎯 Personalizzazione Totale', 'Basato su composizione corporea, obiettivi e preferenze'),
            if (_enableRefeedDay)
              _buildFeatureItem('🔄 Refeed Day', 'Calorie cycling per ottimizzazione metabolica'),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionalValidationCard() {
    final validation = _profile!.validateNutritionalSafety();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Validazione Nutrizionale',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  validation['isValid'] ? Icons.check_circle : Icons.warning,
                  color: validation['isValid'] ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  validation['isValid'] ? 'Piano nutrizionalmente sicuro' : 'Attenzione richiesta',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: validation['isValid'] ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
            if (validation['warnings'].isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Avvertenze: ${validation['warnings'].join(', ')}',
                style: TextStyle(color: Colors.orange[700]),
              ),
            ],
            if (validation['requiresMedicalSupervision']) ...[
              const SizedBox(height: 8),
              Text(
                '⚕️ Si raccomanda supervisione medica',
                style: TextStyle(
                  color: Colors.red[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetabolicCalculationsCard() {
    final bmr = _profile!.calculateBMR();
    final tdee = _profile!.calculateTDEE();
    final calorieTarget = _profile!.calculateCalorieTarget();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calcoli Metabolici Avanzati',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildMetricRow('BMR (Metabolismo Basale)', '${bmr.round()} kcal'),
            _buildMetricRow('TDEE (Dispendio Totale)', '${tdee.round()} kcal'),
            _buildMetricRow('Target Calorico', '${calorieTarget} kcal'),
            _buildMetricRow('Formula BMR', _profile!.preferredBMRFormula.toString().split('.').last),
            if (_profile!.leanMass != null)
              _buildMetricRow('Massa Magra', '${_profile!.leanMass!.toStringAsFixed(1)} kg'),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalizationCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personalizzazioni Applicate',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildMetricRow('Obiettivo Primario', _profile!.primaryGoal.toString().split('.').last),
            if (_profile!.dietaryRegimen != null)
              _buildMetricRow('Regime Alimentare', _profile!.dietaryRegimen.toString().split('.').last),
            _buildMetricRow('Pasti al Giorno', '${_profile!.mealsPerDay}'),
            _buildMetricRow('Livello NEAT', _profile!.neatLevel.toString().split('.').last),
            _buildMetricRow('Attività Lavorativa', _profile!.workActivity.toString().split('.').last),
            if (_profile!.plannedExercises.isNotEmpty)
              _buildMetricRow('Esercizi Pianificati', '${_profile!.plannedExercises.length}'),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: Colors.grey[700]),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _showFoodDetailsDialog(Food food) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(food.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Valori nutrizionali per 100g:'),
            const SizedBox(height: 8),
            Text('Calorie: ${food.calories} kcal'),
            Text('Proteine: ${food.proteins.toStringAsFixed(1)}g'),
            Text('Carboidrati: ${food.carbs.toStringAsFixed(1)}g'),
            Text('Grassi: ${food.fats.toStringAsFixed(1)}g'),
            Text('Fibre: ${food.fiber.toStringAsFixed(1)}g'),
            Text('Sodio: ${(food.sodium ?? 0).toStringAsFixed(0)}mg'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Chiudi'),
          ),
        ],
      ),
    );
  }
}
