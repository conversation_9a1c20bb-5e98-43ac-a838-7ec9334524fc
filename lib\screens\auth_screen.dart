import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/supabase_auth_service.dart';
import '../theme/dr_staffilano_theme.dart';
import 'auth/email_confirmation_screen.dart';

/// Schermata di autenticazione con login e registrazione
class AuthScreen extends StatefulWidget {
  const AuthScreen({Key? key}) : super(key: key);

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  final _authService = SupabaseAuthService();
  final _formKey = GlobalKey<FormState>();
  
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nomeController = TextEditingController();
  final _usernameController = TextEditingController();
  
  bool _isLogin = true;
  bool _isLoading = false;
  bool _obscurePassword = true;
  
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _nomeController.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 40),
              _buildHeader(),
              const SizedBox(height: 40),
              _buildAuthForm(),
              const SizedBox(height: 24),
              _buildSocialLogin(),
              const SizedBox(height: 24),
              _buildToggleButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.primaryGreen,
            borderRadius: BorderRadius.circular(60),
            boxShadow: [
              BoxShadow(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            FontAwesomeIcons.heartPulse,
            color: Colors.white,
            size: 50,
          ),
        ).animate().scale(delay: 200.ms, duration: 600.ms),
        const SizedBox(height: 24),
        Text(
          'Dr. Staffilano',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: DrStaffilanoTheme.primaryGreen,
          ),
        ).animate().fadeIn(delay: 400.ms),
        const SizedBox(height: 8),
        Text(
          _isLogin ? 'Bentornato!' : 'Inizia il tuo viaggio',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ).animate().fadeIn(delay: 600.ms),
      ],
    );
  }

  Widget _buildAuthForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              _isLogin ? 'Accedi' : 'Registrati',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // Campi registrazione
            if (!_isLogin) ...[
              _buildTextField(
                controller: _nomeController,
                label: 'Nome completo',
                icon: FontAwesomeIcons.user,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Inserisci il tuo nome';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _usernameController,
                label: 'Username',
                icon: FontAwesomeIcons.at,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Inserisci un username';
                  }
                  if (value!.length < 3) {
                    return 'Username troppo corto';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],
            
            // Email
            _buildTextField(
              controller: _emailController,
              label: 'Email',
              icon: FontAwesomeIcons.envelope,
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Inserisci la tua email';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                  return 'Email non valida';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Password
            _buildTextField(
              controller: _passwordController,
              label: 'Password',
              icon: FontAwesomeIcons.lock,
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? FontAwesomeIcons.eye : FontAwesomeIcons.eyeSlash,
                  size: 16,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Inserisci la password';
                }
                if (!_isLogin && value!.length < 6) {
                  return 'Password troppo corta (min 6 caratteri)';
                }
                return null;
              },
            ),
            
            if (_isLogin) ...[
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: _resetPassword,
                  child: Text(
                    'Password dimenticata?',
                    style: TextStyle(color: DrStaffilanoTheme.primaryGreen),
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Pulsante principale
            ElevatedButton(
              onPressed: _isLoading ? null : _handleAuth,
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      _isLogin ? 'Accedi' : 'Registrati',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ],
        ),
      ),
    ).animate().slideY(begin: 0.3, end: 0, delay: 800.ms);
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, size: 18),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: DrStaffilanoTheme.primaryGreen),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
    );
  }

  Widget _buildSocialLogin() {
    return Column(
      children: [
        Text(
          'Oppure continua con',
          style: TextStyle(color: Colors.grey.shade600),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSocialButton(
                icon: FontAwesomeIcons.google,
                label: 'Google',
                color: Colors.red,
                onPressed: _signInWithGoogle,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSocialButton(
                icon: FontAwesomeIcons.apple,
                label: 'Apple',
                color: Colors.black,
                onPressed: _signInWithApple,
              ),
            ),
          ],
        ),
      ],
    ).animate().fadeIn(delay: 1000.ms);
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: _isLoading ? null : onPressed,
      icon: Icon(icon, size: 18, color: color),
      label: Text(label, style: TextStyle(color: color)),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        side: BorderSide(color: color.withOpacity(0.3)),
      ),
    );
  }

  Widget _buildToggleButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _isLogin ? 'Non hai un account?' : 'Hai già un account?',
          style: TextStyle(color: Colors.grey.shade600),
        ),
        TextButton(
          onPressed: () {
            setState(() {
              _isLogin = !_isLogin;
            });
          },
          child: Text(
            _isLogin ? 'Registrati' : 'Accedi',
            style: TextStyle(
              color: DrStaffilanoTheme.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    ).animate().fadeIn(delay: 1200.ms);
  }

  Future<void> _handleAuth() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isLogin) {
        // LOGIN: Comportamento normale
        await _authService.signIn(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );

        // Con AuthGate, non è necessario navigare manualmente
        // L'AuthGate rileverà automaticamente il cambio di stato auth

      } else {
        // REGISTRAZIONE: Nuovo comportamento con conferma email
        final response = await _authService.signUp(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          nome: _nomeController.text.trim(),
          username: _usernameController.text.trim(),
        );

        if (mounted && response.user != null) {
          // Registrazione avvenuta con successo
          // Naviga alla schermata di conferma email
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => EmailConfirmationScreen(
                email: _emailController.text.trim(),
              ),
            ),
          );

          // Mostra messaggio di successo
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 12),
                  Text('Registrazione completata! Controlla la tua email.'),
                ],
              ),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Errore durante ${_isLogin ? 'il login' : 'la registrazione'}';

        // Gestisci errori specifici
        if (e.toString().contains('email') && e.toString().contains('already')) {
          errorMessage = 'Questa email è già registrata. Prova ad accedere.';
        } else if (e.toString().contains('password')) {
          errorMessage = 'Password non valida. Deve essere di almeno 6 caratteri.';
        } else if (e.toString().contains('network')) {
          errorMessage = 'Errore di connessione. Verifica la tua connessione internet.';
        } else {
          errorMessage = 'Errore: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Chiama la funzione multipiattaforma
      await _authService.signInWithGoogle();

      if (mounted) {
        print('✅ Login Google avviato con successo');

        // Con AuthGate, non è necessario navigare manualmente
        // L'AuthGate rileverà automaticamente il cambio di stato auth
        // e gestirà l'onboarding se necessario

        // Mostra messaggio di successo
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text('Login Google completato! Preparazione del tuo profilo...'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Errore durante il login Google';

        // Gestisci diversi tipi di errore specifici
        if (e.toString().contains('Login annullato') || e.toString().contains('cancelled')) {
          errorMessage = 'Login annullato dall\'utente';
        } else if (e.toString().contains('network') || e.toString().contains('Network')) {
          errorMessage = 'Errore di connessione. Verifica la tua connessione internet.';
        } else if (e.toString().contains('Access Token') || e.toString().contains('ID Token')) {
          errorMessage = 'Errore nei token di autenticazione. Riprova.';
        } else {
          errorMessage = 'Errore login Google: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signInWithApple() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _authService.signInWithApple();
      if (success && mounted) {
        // Naviga alla home usando il routing dell'AuthWrapper
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore login Apple: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resetPassword() async {
    if (_emailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Inserisci la tua email per resettare la password'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      await _authService.resetPassword(_emailController.text.trim());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Email di reset inviata! Controlla la tua casella di posta'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
