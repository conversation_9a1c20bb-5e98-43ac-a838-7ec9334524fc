import 'package:flutter/material.dart';
import '../../models/supabase_profile.dart';
import '../../services/supabase_auth_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modal per modificare il profilo Supabase reale
class SupabaseEditProfileModal extends StatefulWidget {
  final SupabaseProfile? currentProfile;
  final VoidCallback? onProfileUpdated;

  const SupabaseEditProfileModal({
    Key? key,
    this.currentProfile,
    this.onProfileUpdated,
  }) : super(key: key);

  @override
  State<SupabaseEditProfileModal> createState() => _SupabaseEditProfileModalState();
}

class _SupabaseEditProfileModalState extends State<SupabaseEditProfileModal> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  final _avatarUrlController = TextEditingController();
  
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCurrentData();
  }

  void _loadCurrentData() {
    if (widget.currentProfile != null) {
      _nomeController.text = widget.currentProfile!.nome;
      _usernameController.text = widget.currentProfile!.username;
      _bioController.text = widget.currentProfile!.bio ?? '';
      _avatarUrlController.text = widget.currentProfile!.fotoProfiloUrl ?? '';
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _avatarUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Modifica Profilo',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Avatar preview
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                            border: Border.all(
                              color: DrStaffilanoTheme.primaryGreen,
                              width: 3,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              _nomeController.text.isNotEmpty 
                                ? _nomeController.text[0].toUpperCase() 
                                : 'U',
                              style: const TextStyle(
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                                color: DrStaffilanoTheme.primaryGreen,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 20),

                      // Nome visualizzato
                      _buildFormField(
                        controller: _nomeController,
                        label: 'Nome visualizzato',
                        hint: 'Come vuoi essere chiamato?',
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Il nome è obbligatorio';
                          }
                          if (value.trim().length < 2) {
                            return 'Il nome deve avere almeno 2 caratteri';
                          }
                          return null;
                        },
                        onChanged: (value) => setState(() {}), // Per aggiornare l'avatar
                      ),

                      const SizedBox(height: 16),

                      // Username
                      _buildFormField(
                        controller: _usernameController,
                        label: 'Username',
                        hint: 'Il tuo identificativo unico',
                        icon: Icons.alternate_email,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'L\'username è obbligatorio';
                          }
                          if (value.trim().length < 3) {
                            return 'L\'username deve avere almeno 3 caratteri';
                          }
                          if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
                            return 'L\'username può contenere solo lettere, numeri e underscore';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Bio
                      _buildFormField(
                        controller: _bioController,
                        label: 'Bio (Opzionale)',
                        hint: 'Raccontaci qualcosa di te...',
                        icon: Icons.description,
                        maxLines: 3,
                        isRequired: false,
                      ),

                      const SizedBox(height: 16),

                      // URL Avatar
                      _buildFormField(
                        controller: _avatarUrlController,
                        label: 'URL Avatar (Opzionale)',
                        hint: 'https://esempio.com/avatar.jpg',
                        icon: Icons.image,
                        isRequired: false,
                      ),

                      const SizedBox(height: 20),

                      // Messaggio di errore
                      if (_errorMessage.isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _errorMessage,
                                  style: TextStyle(color: Colors.red[700], fontSize: 14),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Pulsanti
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: const BorderSide(color: DrStaffilanoTheme.primaryGreen),
                      ),
                      child: const Text(
                        'Annulla',
                        style: TextStyle(color: DrStaffilanoTheme.primaryGreen),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DrStaffilanoTheme.primaryGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text('Salva'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool isRequired = true,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: DrStaffilanoTheme.primaryGreen),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(color: Colors.red),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400]),
            filled: true,
            fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: DrStaffilanoTheme.primaryGreen, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final authService = SupabaseAuthService();
      
      final success = await authService.updateUserProfile(
        nome: _nomeController.text.trim(),
        username: _usernameController.text.trim(),
        bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
        fotoProfiloUrl: _avatarUrlController.text.trim().isEmpty ? null : _avatarUrlController.text.trim(),
      );

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          
          // Mostra messaggio di successo
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 12),
                  Text('Profilo aggiornato con successo!'),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Notifica il parent widget
          widget.onProfileUpdated?.call();
        }
      } else {
        setState(() {
          _errorMessage = 'Errore durante il salvataggio del profilo';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Errore: ${e.toString()}';
        _isLoading = false;
      });
    }
  }
}
