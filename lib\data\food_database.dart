import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food.dart';
import '../models/diet_type.dart';
import '../utils/micronutrients_helper.dart';
import 'sample_foods.dart';
import 'italian_foods.dart';
import 'regional_italian_foods.dart';
import 'basic_foods.dart';
import 'seasonal_foods.dart';
import 'exact_nutrition_foods.dart';
import 'complete_food_database.dart';
import 'micronutrient_foods.dart';
import 'safe_italian_proteins.dart';
import 'safe_cooked_foods.dart';
import '../services/food_safety_service.dart';

class FoodDatabase {
  static const String _foodsKey = 'foods_database';

  // Ottieni tutti gli alimenti dal database
  Future<List<Food>> getAllFoods() async {
    print('Caricamento alimenti dal database...');
    final prefs = await SharedPreferences.getInstance();
    final foodsJson = prefs.getString(_foodsKey);

    if (foodsJson == null) {
      print('Nessun alimento trovato nel database, inizializzazione con alimenti di esempio...');
      // Se non ci sono alimenti salvati, inizializza il database con alimenti di esempio
      await _initializeDatabase();

      // Verifica che l'inizializzazione sia avvenuta correttamente
      final sampleFoods = SampleFoods.getSampleFoods();
      print('Numero di alimenti di esempio: ${sampleFoods.length}');
      return sampleFoods;
    }

    try {
      final List<dynamic> decoded = jsonDecode(foodsJson);
      final foods = decoded.map((foodJson) => Food.fromMap(foodJson as Map<String, dynamic>)).toList();
      print('Alimenti caricati dal database: ${foods.length}');

      // Se per qualche motivo il database è vuoto, inizializza con alimenti di esempio
      if (foods.isEmpty) {
        print('Database vuoto dopo il caricamento, inizializzazione con alimenti di esempio...');
        await _initializeDatabase();
        return SampleFoods.getSampleFoods();
      }

      return foods;
    } catch (e) {
      print('Errore nel caricamento degli alimenti: $e');
      print('Inizializzazione con alimenti di esempio a causa dell\'errore...');

      // In caso di errore, reinizializza il database
      await resetDatabase();
      return SampleFoods.getSampleFoods();
    }
  }

  // Salva tutti gli alimenti nel database
  Future<void> saveAllFoods(List<Food> foods) async {
    print('Salvataggio di ${foods.length} alimenti nel database...');
    try {
      final prefs = await SharedPreferences.getInstance();
      final foodsList = foods.map((food) => food.toMap()).toList();
      final foodsJson = jsonEncode(foodsList);

      print('Dimensione JSON: ${foodsJson.length} caratteri');

      final success = await prefs.setString(_foodsKey, foodsJson);
      print('Salvataggio completato: $success');

      // Verifica che il salvataggio sia avvenuto correttamente
      final savedJson = prefs.getString(_foodsKey);
      if (savedJson == null) {
        print('ERRORE: Il salvataggio non è riuscito, il valore salvato è null');
      } else {
        print('Verifica salvataggio: ${savedJson.length} caratteri');
      }
    } catch (e) {
      print('ERRORE durante il salvataggio degli alimenti: $e');
      throw Exception('Impossibile salvare gli alimenti nel database: $e');
    }
  }

  // Aggiungi un nuovo alimento al database
  Future<void> addFood(Food food) async {
    final foods = await getAllFoods();

    // Verifica se l'alimento esiste già
    final existingIndex = foods.indexWhere((f) => f.id == food.id);

    if (existingIndex >= 0) {
      // Aggiorna l'alimento esistente
      foods[existingIndex] = food;
    } else {
      // Aggiungi il nuovo alimento
      foods.add(food);
    }

    await saveAllFoods(foods);
  }

  // Rimuovi un alimento dal database
  Future<void> removeFood(String foodId) async {
    final foods = await getAllFoods();
    foods.removeWhere((food) => food.id == foodId);
    await saveAllFoods(foods);
  }

  // Cerca alimenti per nome
  Future<List<Food>> searchFoodsByName(String query) async {
    final foods = await getAllFoods();

    if (query.isEmpty) {
      return foods;
    }

    final lowercaseQuery = query.toLowerCase();
    return foods.where((food) =>
      food.name.toLowerCase().contains(lowercaseQuery) ||
      food.description.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  // Filtra alimenti per categoria
  Future<List<Food>> filterFoodsByCategory(FoodCategory category) async {
    final foods = await getAllFoods();
    return foods.where((food) => food.categories.contains(category)).toList();
  }

  // Filtra alimenti per tipo di pasto
  Future<List<Food>> filterFoodsByMealType(MealType mealType) async {
    final foods = await getAllFoods();
    return foods.where((food) => food.suitableForMeals.contains(mealType)).toList();
  }

  // Filtra alimenti per tipo di dieta
  Future<List<Food>> filterFoodsByDietType(String dietType) async {
    final foods = await getAllFoods();

    switch (dietType) {
      case 'vegetarian':
        return foods.where((food) => food.isVegetarian).toList();
      case 'vegan':
        return foods.where((food) => food.isVegan).toList();
      case 'glutenFree':
        return foods.where((food) => food.isGlutenFree).toList();
      case 'dairyFree':
        return foods.where((food) => food.isDairyFree).toList();
      default:
        return foods;
    }
  }

  // Filtra alimenti per complessità
  Future<List<Food>> filterFoodsByComplexity(int maxComplexity) async {
    final foods = await getAllFoods();
    return foods.where((food) => food.complexity <= maxComplexity).toList();
  }

  // Ottieni alimenti di stagione per il mese corrente
  Future<List<Food>> getSeasonalFoods() async {
    final foods = await getAllFoods();
    final currentMonth = DateTime.now().month;
    return foods.where((food) =>
      !food.isSeasonal || food.seasonalMonths.contains(currentMonth)
    ).toList();
  }

  // Ottieni alimenti di stagione per un mese specifico
  Future<List<Food>> getSeasonalFoodsForMonth(int month) async {
    if (month < 1 || month > 12) {
      throw ArgumentError('Il mese deve essere compreso tra 1 e 12');
    }

    final foods = await getAllFoods();
    return foods.where((food) =>
      !food.isSeasonal || food.seasonalMonths.contains(month)
    ).toList();
  }

  // Ottieni alimenti di stagione per una stagione specifica
  Future<List<Food>> getSeasonalFoodsForSeason(Season season) async {
    final List<int> months;

    switch (season) {
      case Season.spring:
        months = [3, 4, 5]; // Primavera: marzo, aprile, maggio
        break;
      case Season.summer:
        months = [6, 7, 8]; // Estate: giugno, luglio, agosto
        break;
      case Season.autumn:
        months = [9, 10, 11]; // Autunno: settembre, ottobre, novembre
        break;
      case Season.winter:
        months = [12, 1, 2]; // Inverno: dicembre, gennaio, febbraio
        break;
    }

    final foods = await getAllFoods();
    return foods.where((food) =>
      !food.isSeasonal || food.seasonalMonths.any((month) => months.contains(month))
    ).toList();
  }

  // Ottieni solo alimenti stagionali (escludendo quelli disponibili tutto l'anno)
  Future<List<Food>> getOnlySeasonalFoods() async {
    final foods = await getAllFoods();
    return foods.where((food) => food.isSeasonal).toList();
  }

  // Ottieni alimenti per regione italiana
  Future<List<Food>> getFoodsByRegion(ItalianRegion region) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.italianRegions.contains(region) ||
      food.tags.contains(region.toString().split('.').last)
    ).toList();
  }

  // Ottieni alimenti tradizionali italiani
  Future<List<Food>> getTraditionalItalianFoods() async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.isTraditionalItalian ||
      food.tags.contains('tradizionale') ||
      food.tags.contains('italiano')
    ).toList();
  }

  // Ottieni alimenti per area geografica italiana (Nord, Centro, Sud, Isole)
  Future<List<Food>> getFoodsByGeographicArea(GeographicArea area) async {
    final foods = await getAllFoods();
    final List<ItalianRegion> regionsInArea;

    switch (area) {
      case GeographicArea.north:
        regionsInArea = [
          ItalianRegion.lombardia,
          ItalianRegion.piemonte,
          ItalianRegion.valleDAosta,
          ItalianRegion.liguria,
          ItalianRegion.veneto,
          ItalianRegion.trentinoAltoAdige,
          ItalianRegion.friuliVeneziaGiulia,
          ItalianRegion.emiliaRomagna,
        ];
        break;
      case GeographicArea.center:
        regionsInArea = [
          ItalianRegion.toscana,
          ItalianRegion.umbria,
          ItalianRegion.marche,
          ItalianRegion.lazio,
        ];
        break;
      case GeographicArea.south:
        regionsInArea = [
          ItalianRegion.abruzzo,
          ItalianRegion.molise,
          ItalianRegion.campania,
          ItalianRegion.puglia,
          ItalianRegion.basilicata,
          ItalianRegion.calabria,
        ];
        break;
      case GeographicArea.islands:
        regionsInArea = [
          ItalianRegion.sicilia,
          ItalianRegion.sardegna,
        ];
        break;
    }

    return foods.where((food) =>
      food.italianRegions.any((region) => regionsInArea.contains(region)) ||
      food.tags.contains(area.toString().split('.').last) ||
      regionsInArea.any((region) => food.tags.contains(region.toString().split('.').last))
    ).toList();
  }

  // Ottieni alimenti con valori nutrizionali esatti verificati
  Future<List<Food>> getExactNutritionFoods() async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.id.startsWith('exact_') ||
      food.tags.contains('exact') ||
      food.tags.contains('verificato')
    ).toList();
  }

  // Ottieni alimenti per nome esatto
  Future<List<Food>> getFoodsByExactName(String exactName) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.name.toLowerCase() == exactName.toLowerCase()
    ).toList();
  }

  // Ottieni alimenti per categoria
  Future<List<Food>> getFoodsByCategory(FoodCategory category) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.categories.contains(category)
    ).toList();
  }

  // Ottieni alimenti per tipo di pasto
  Future<List<Food>> getFoodsByMealType(MealType mealType) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.suitableForMeals.contains(mealType)
    ).toList();
  }

  // Ottieni alimenti per range calorico
  Future<List<Food>> getFoodsByCalorieRange(int minCalories, int maxCalories) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.calories >= minCalories && food.calories <= maxCalories
    ).toList();
  }

  // Ottieni alimenti per contenuto proteico
  Future<List<Food>> getFoodsByProteinContent(double minProtein) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.proteins >= minProtein
    ).toList();
  }

  // Ottieni alimenti per dieta specifica
  Future<List<Food>> getFoodsByDietType(DietType dietType) async {
    final foods = await getAllFoods();

    switch (dietType) {
      case DietType.vegetarian:
        return foods.where((food) => food.isVegetarian).toList();
      case DietType.vegan:
        return foods.where((food) => food.isVegan).toList();
      case DietType.glutenFree:
        return foods.where((food) => food.isGlutenFree).toList();
      case DietType.dairyFree:
        return foods.where((food) => food.isDairyFree).toList();
      case DietType.keto:
        return foods.where((food) => food.carbs <= 5).toList();
      case DietType.paleo:
        return foods.where((food) =>
          !food.categories.contains(FoodCategory.grain) &&
          !food.categories.contains(FoodCategory.dairy) &&
          !food.tags.contains('processed')
        ).toList();
      case DietType.pescatarian:
        return foods.where((food) =>
          food.isVegetarian ||
          food.tags.contains('pesce') ||
          food.tags.contains('fish')
        ).toList();
      case DietType.omnivore:
      default:
        return foods;
    }
  }

  // Ottieni alimenti con micronutrienti
  Future<List<Food>> getFoodsWithMicronutrients() async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.isNotEmpty
    ).toList();
  }

  // Ottieni alimenti per contenuto di calcio
  Future<List<Food>> getFoodsByCalciumContent(double minCalcium) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM) &&
      food.micronutrients[MicronutrientsHelper.CALCIUM]! >= minCalcium
    ).toList();
  }

  // Ottieni alimenti per contenuto di fosforo
  Future<List<Food>> getFoodsByPhosphorusContent(double minPhosphorus) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS) &&
      food.micronutrients[MicronutrientsHelper.PHOSPHORUS]! >= minPhosphorus
    ).toList();
  }

  // Ottieni alimenti per contenuto di magnesio
  Future<List<Food>> getFoodsByMagnesiumContent(double minMagnesium) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.MAGNESIUM) &&
      food.micronutrients[MicronutrientsHelper.MAGNESIUM]! >= minMagnesium
    ).toList();
  }

  // Ottieni alimenti per contenuto di potassio
  Future<List<Food>> getFoodsByPotassiumContent(double minPotassium) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.POTASSIUM) &&
      food.micronutrients[MicronutrientsHelper.POTASSIUM]! >= minPotassium
    ).toList();
  }

  // Ottieni alimenti per contenuto di sodio (basso)
  Future<List<Food>> getFoodsByLowSodiumContent(double maxSodium) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.SODIUM) &&
      food.micronutrients[MicronutrientsHelper.SODIUM]! <= maxSodium
    ).toList();
  }

  // Ottieni alimenti per rapporto calcio/fosforo
  Future<List<Food>> getFoodsByCalciumPhosphorusRatio(double minRatio) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM) &&
      food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS) &&
      food.micronutrients[MicronutrientsHelper.PHOSPHORUS]! > 0 &&
      (food.micronutrients[MicronutrientsHelper.CALCIUM]! / food.micronutrients[MicronutrientsHelper.PHOSPHORUS]!) >= minRatio
    ).toList();
  }

  // Ottieni alimenti per contenuto di vitamina C
  Future<List<Food>> getFoodsByVitaminCContent(double minVitaminC) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.VITAMIN_C) &&
      food.micronutrients[MicronutrientsHelper.VITAMIN_C]! >= minVitaminC
    ).toList();
  }

  // Ottieni alimenti per contenuto di ferro
  Future<List<Food>> getFoodsByIronContent(double minIron) async {
    final foods = await getAllFoods();
    return foods.where((food) =>
      food.micronutrients.containsKey(MicronutrientsHelper.IRON) &&
      food.micronutrients[MicronutrientsHelper.IRON]! >= minIron
    ).toList();
  }

  // Ottieni alimenti preferiti (da implementare con un sistema di preferiti)
  Future<List<Food>> getFavoriteFoods() async {
    // Implementazione di esempio
    return [];
  }

  // Inizializza il database con tutti gli alimenti disponibili
  Future<void> _initializeDatabase() async {
    print('Inizializzazione del database con tutti gli alimenti disponibili...');

    // Raccogliamo tutti gli alimenti dai vari database
    final sampleFoods = SampleFoods.getSampleFoods();
    final italianFoods = ItalianFoods.getItalianFoods();
    final regionalFoods = RegionalItalianFoods.getRegionalFoods();
    final basicFoods = BasicFoods.getBasicFoods();
    final seasonalFoods = SeasonalFoods.getSeasonalFoods();
    final exactNutritionFoods = ExactNutritionFoods.getExactNutritionFoods();
    final completeFoods = CompleteFoodDatabase.getAllFoods();
    final micronutrientFoods = MicronutrientFoods.getAllFoods();
    final safeProteins = SafeItalianProteins.getSafeProteins();
    final safeCookedFoods = SafeCookedFoods.getSafeCookedFoods();

    // Combiniamo tutti gli alimenti in un'unica lista
    final allFoods = [
      ...safeProteins,        // MASSIMA PRIORITÀ: Proteine sicure e cotte
      ...safeCookedFoods,     // ALTA PRIORITÀ: Verdure e cereali sicuri e cotti
      ...micronutrientFoods,  // Poi agli alimenti con dati completi sui micronutrienti
      ...completeFoods,       // Poi al database completo con valori nutrizionali esatti
      ...exactNutritionFoods, // Poi gli alimenti con valori nutrizionali esatti
      ...basicFoods,          // Poi gli alimenti base
      ...seasonalFoods,       // Poi gli alimenti stagionali
      ...italianFoods,        // Poi gli alimenti italiani
      ...regionalFoods,       // Poi gli alimenti regionali
      ...sampleFoods,         // Infine gli alimenti di esempio
    ];

    // Rimuoviamo eventuali duplicati (basati sull'ID)
    final Map<String, Food> uniqueFoodsMap = {};
    for (var food in allFoods) {
      uniqueFoodsMap[food.id] = food;
    }

    // FILTRO DI SICUREZZA ALIMENTARE CRITICO!
    // Rimuovi TUTTI gli alimenti non sicuri prima di salvare nel database
    final allUniqueFoods = uniqueFoodsMap.values.toList();
    final safeFoods = <Food>[];
    final removedUnsafeFoods = <String>[];

    for (var food in allUniqueFoods) {
      if (FoodSafetyService.isFoodSafe(food)) {
        safeFoods.add(food);
      } else {
        removedUnsafeFoods.add('${food.name} (${food.id})');
        print('RIMOSSO ALIMENTO NON SICURO: ${food.name} - ${food.description}');

        // Prova a convertire in versione sicura
        final safeVersion = FoodSafetyService.getCookedVersion(food);
        if (safeVersion != null && FoodSafetyService.isFoodSafe(safeVersion)) {
          safeFoods.add(safeVersion);
          print('AGGIUNTA VERSIONE SICURA: ${safeVersion.name}');
        }
      }
    }

    print('FILTRO DI SICUREZZA COMPLETATO:');
    print('- Alimenti totali: ${allUniqueFoods.length}');
    print('- Alimenti sicuri: ${safeFoods.length}');
    print('- Alimenti rimossi: ${removedUnsafeFoods.length}');
    if (removedUnsafeFoods.isNotEmpty) {
      print('- Alimenti rimossi: ${removedUnsafeFoods.join(', ')}');
    }

    final uniqueFoods = safeFoods;
    print('Numero totale di alimenti da salvare: ${uniqueFoods.length}');

    if (uniqueFoods.isEmpty) {
      print('ERRORE: Nessun alimento disponibile!');
      return;
    }

    try {
      await saveAllFoods(uniqueFoods);
      print('Database inizializzato con successo');

      // Stampa statistiche sul database
      _printDatabaseStatistics(uniqueFoods);
    } catch (e) {
      print('ERRORE durante l\'inizializzazione del database: $e');
    }
  }

  // Stampa statistiche sul database degli alimenti
  void _printDatabaseStatistics(List<Food> foods) {
    // Conteggio per categoria
    final Map<FoodCategory, int> categoryCounts = {};
    for (var category in FoodCategory.values) {
      categoryCounts[category] = 0;
    }

    for (var food in foods) {
      for (var category in food.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    // Conteggio per tipo di pasto
    final Map<MealType, int> mealTypeCounts = {};
    for (var mealType in MealType.values) {
      mealTypeCounts[mealType] = 0;
    }

    for (var food in foods) {
      for (var mealType in food.suitableForMeals) {
        mealTypeCounts[mealType] = (mealTypeCounts[mealType] ?? 0) + 1;
      }
    }

    // Conteggio per dieta
    int vegetarianCount = 0;
    int veganCount = 0;
    int glutenFreeCount = 0;
    int dairyFreeCount = 0;

    for (var food in foods) {
      if (food.isVegetarian) vegetarianCount++;
      if (food.isVegan) veganCount++;
      if (food.isGlutenFree) glutenFreeCount++;
      if (food.isDairyFree) dairyFreeCount++;
    }

    // Stampa statistiche
    print('\n===== STATISTICHE DATABASE ALIMENTI =====');
    print('Numero totale di alimenti: ${foods.length}');

    print('\nDistribuzione per categoria:');
    categoryCounts.forEach((category, count) {
      print('- ${category.toString().split('.').last}: $count alimenti');
    });

    print('\nDistribuzione per tipo di pasto:');
    mealTypeCounts.forEach((mealType, count) {
      print('- ${mealType.toString().split('.').last}: $count alimenti');
    });

    print('\nDistribuzione per dieta:');
    print('- Vegetariani: $vegetarianCount alimenti (${(vegetarianCount / foods.length * 100).toStringAsFixed(1)}%)');
    print('- Vegani: $veganCount alimenti (${(veganCount / foods.length * 100).toStringAsFixed(1)}%)');
    print('- Senza glutine: $glutenFreeCount alimenti (${(glutenFreeCount / foods.length * 100).toStringAsFixed(1)}%)');
    print('- Senza lattosio: $dairyFreeCount alimenti (${(dairyFreeCount / foods.length * 100).toStringAsFixed(1)}%)');

    print('=======================================\n');
  }

  // Resetta il database (utile per debug)
  Future<void> resetDatabase() async {
    print('Reset del database...');
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_foodsKey);
      print('Database rimosso');
      await _initializeDatabase();
    } catch (e) {
      print('ERRORE durante il reset del database: $e');
    }
  }
}
