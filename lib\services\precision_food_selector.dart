import 'dart:math';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../models/user_profile.dart';
import 'food_database_service.dart';
import '../data/food_database.dart';
import 'food_variety_manager.dart';
import 'enhanced_food_selector.dart';
import 'meal_appropriateness_validator.dart';

/// Servizio specializzato nella selezione precisa degli alimenti dal database
/// per la generazione di piani dietetici con grammature accurate.
class PrecisionFoodSelector {
  final FoodDatabaseService _databaseService;
  final FoodDatabase _foodDatabase;
  final FoodVarietyManager _varietyManager;
  final EnhancedFoodSelector _enhancedSelector;

  // Tolleranze per la precisione
  static const double _calorieTolerancePercent = 0.03; // 3% di tolleranza per le calorie
  static const int _calorieToleranceAbsolute = 15; // ±15 kcal di tolleranza assoluta
  static const int _macroToleranceGrams = 2; // ±2g di tolleranza per i macronutrienti

  // Singleton
  static PrecisionFoodSelector? _instance;

  static Future<PrecisionFoodSelector> getInstance() async {
    if (_instance == null) {
      final databaseService = await FoodDatabaseService.getInstance();
      final foodDatabase = FoodDatabase();
      final varietyManager = await FoodVarietyManager.getInstance();
      final enhancedSelector = await EnhancedFoodSelector.create();
      _instance = PrecisionFoodSelector._(databaseService, foodDatabase, varietyManager, enhancedSelector);
    }
    return _instance!;
  }

  PrecisionFoodSelector._(this._databaseService, this._foodDatabase, this._varietyManager, this._enhancedSelector);

  /// Seleziona gli alimenti per un pasto con grammature precise e massima varietà
  Future<List<FoodPortion>> selectFoodsForMeal({
    required UserProfile userProfile,
    required String mealType,
    required int targetCalories,
    required Map<String, int> targetMacros,
    Map<String, int>? usedFoodsCount,
    DateTime? forDate,
  }) async {
    print('Selezione alimenti di precisione con varietà per pasto di tipo $mealType');
    print('Obiettivo calorico: $targetCalories kcal');
    print('Obiettivi macronutrienti: $targetMacros');

    // Usa il selettore migliorato per massimizzare la varietà
    try {
      final enhancedResult = await _enhancedSelector.selectFoodsForMeal(
        userProfile: userProfile,
        mealType: mealType,
        targetCalories: targetCalories,
        targetMacros: targetMacros,
        forDate: forDate,
      );

      if (enhancedResult.isNotEmpty) {
        print('Selezione migliorata completata con ${enhancedResult.length} alimenti');
        return enhancedResult;
      }
    } catch (e) {
      print('Errore nel selettore migliorato, fallback al metodo tradizionale: $e');
    }

    // Fallback al metodo tradizionale se il selettore migliorato fallisce
    print('Utilizzo del metodo di selezione tradizionale come fallback');
    return _selectFoodsTraditionalMethod(
      userProfile,
      mealType,
      targetCalories,
      targetMacros,
      usedFoodsCount,
    );
  }

  /// Metodo di selezione tradizionale (fallback)
  Future<List<FoodPortion>> _selectFoodsTraditionalMethod(
    UserProfile userProfile,
    String mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    Map<String, int>? usedFoodsCount,
  ) async {
    // Ottieni gli alimenti adatti per questo tipo di pasto
    final suitableFoods = await _filterSuitableFoods(userProfile, mealType);

    if (suitableFoods.isEmpty) {
      print('Nessun alimento adatto trovato per il pasto di tipo $mealType');
      return [];
    }

    print('Trovati ${suitableFoods.length} alimenti adatti per il pasto di tipo $mealType');

    // Organizza gli alimenti per categoria
    final Map<FoodCategory, List<Food>> foodsByCategory = {};
    for (var food in suitableFoods) {
      for (var category in food.categories) {
        if (!foodsByCategory.containsKey(category)) {
          foodsByCategory[category] = [];
        }
        foodsByCategory[category]!.add(food);
      }
    }

    // Definisci le categorie di alimenti da includere in base al tipo di pasto
    final categoriesToInclude = _getCategoriesForMealType(mealType);
    print('Categorie da includere: $categoriesToInclude');

    // Seleziona gli alimenti e calcola le porzioni precise con varietà migliorata
    return _selectFoodsWithPrecisePortionsAndVariety(
      foodsByCategory,
      categoriesToInclude,
      targetCalories,
      targetMacros,
      mealType,
      usedFoodsCount,
    );
  }

  /// Filtra gli alimenti adatti in base alle preferenze dell'utente e al tipo di pasto
  Future<List<Food>> _filterSuitableFoods(UserProfile userProfile, String mealType) async {
    // Ottieni tutti gli alimenti dal database
    final allFoods = await _foodDatabase.getAllFoods();
    print('Numero totale di alimenti nel database: ${allFoods.length}');

    // Se non ci sono alimenti nel database, restituisci una lista vuota
    if (allFoods.isEmpty) {
      print('ERRORE: Database alimenti vuoto');
      // Prova a reinizializzare il database
      await _foodDatabase.resetDatabase();

      // Verifica nuovamente
      final newFoods = await _foodDatabase.getAllFoods();
      print('Dopo reinizializzazione, numero di alimenti: ${newFoods.length}');

      if (newFoods.isEmpty) {
        print('ERRORE CRITICO: Impossibile inizializzare il database degli alimenti');
        return [];
      }
    }

    // Converti il tipo di pasto in enum
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );

    // STEP 1: Filtra per tipo di pasto (basic filtering)
    final basicMealTypeFoods = allFoods
        .where((food) => food.suitableForMeals.contains(mealTypeEnum))
        .toList();

    print('Alimenti base per il tipo di pasto $mealType: ${basicMealTypeFoods.length}');

    // STEP 2: APPLICA VALIDAZIONE APPROPRIATEZZA CULTURALE (CRITICO!)
    final mealTypeFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      basicMealTypeFoods,
      mealTypeEnum,
    );

    print('Alimenti appropriati dopo validazione culturale per $mealType: ${mealTypeFoods.length}');

    // Log degli alimenti esclusi per debugging
    final excludedFoods = basicMealTypeFoods.where((food) => !mealTypeFoods.contains(food)).toList();
    if (excludedFoods.isNotEmpty) {
      print('🚫 Alimenti esclusi dalla validazione appropriatezza:');
      for (final food in excludedFoods.take(5)) { // Mostra solo i primi 5
        print('   - ${food.name} (${food.categories.map((c) => c.toString().split('.').last).join(', ')})');
      }
    }

    // Filtra per tipo di dieta
    final dietTypeFoods = mealTypeFoods.where((food) {
      switch (userProfile.dietType) {
        case DietType.vegetarian:
          return food.isVegetarian;
        case DietType.vegan:
          return food.isVegan;
        case DietType.pescatarian:
          return food.isVegetarian || food.categories.contains(FoodCategory.protein);
        case DietType.keto:
          return food.carbs <= 5; // Esempio semplificato per dieta keto
        case DietType.paleo:
          return !food.categories.contains(FoodCategory.grain) &&
                 !food.categories.contains(FoodCategory.dairy);
        case DietType.omnivore:
        default:
          return true;
      }
    }).toList();

    // Se non ci sono alimenti adatti per questo tipo di dieta, usa tutti gli alimenti del tipo di pasto
    if (dietTypeFoods.isEmpty) {
      print('Attenzione: Nessun alimento trovato per il tipo di dieta ${userProfile.dietType}. Uso tutti gli alimenti per il tipo di pasto $mealType');
      return mealTypeFoods;
    }

    // Filtra per allergie
    final nonAllergenicFoods = dietTypeFoods.where((food) {
      for (var allergen in userProfile.allergies) {
        if (food.allergens.contains(allergen.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Se non ci sono alimenti senza allergeni, usa tutti gli alimenti del tipo di dieta
    if (nonAllergenicFoods.isEmpty && userProfile.allergies.isNotEmpty) {
      print('Attenzione: Nessun alimento trovato senza gli allergeni specificati. Ignoro le allergie.');
      return dietTypeFoods;
    }

    // Filtra per cibi non graditi
    final preferredFoods = nonAllergenicFoods.where((food) {
      for (var dislikedFood in userProfile.dislikedFoods) {
        if (food.name.toLowerCase().contains(dislikedFood.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Se non ci sono alimenti preferiti, usa tutti gli alimenti senza allergeni
    if (preferredFoods.isEmpty && userProfile.dislikedFoods.isNotEmpty) {
      print('Attenzione: Nessun alimento trovato dopo aver filtrato i cibi non graditi. Ignoro le preferenze.');
      return nonAllergenicFoods.isEmpty ? dietTypeFoods : nonAllergenicFoods;
    }

    return preferredFoods;
  }

  /// Ottiene le categorie di alimenti da includere in base al tipo di pasto
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    if (mealType == 'breakfast') {
      // Colazione: cereali/carboidrati + proteine + frutta
      return [
        FoodCategory.grain,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fruit,
      ];
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      // Pranzo/Cena: proteine + carboidrati + verdure + grassi
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
        FoodCategory.fat,
      ];
    } else if (mealType == 'snack') {
      // Spuntino: frutta + proteine o frutta secca
      return [
        FoodCategory.fruit,
        FoodCategory.protein,
        FoodCategory.dairy,
        FoodCategory.fat,
      ];
    } else {
      // Default
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
      ];
    }
  }

  /// Seleziona gli alimenti con porzioni precise e varietà migliorata
  List<FoodPortion> _selectFoodsWithPrecisePortionsAndVariety(
    Map<FoodCategory, List<Food>> foodsByCategory,
    List<FoodCategory> categoriesToInclude,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
    Map<String, int>? usedFoodsCount,
  ) {
    final selectedFoods = <FoodPortion>[];

    // Calorie e macronutrienti correnti
    int currentCalories = 0;
    int currentProteins = 0;
    int currentCarbs = 0;
    int currentFats = 0;

    // Target macronutrienti
    final targetProtein = targetMacros['proteins'] ?? 0;
    final targetCarbs = targetMacros['carbs'] ?? 0;
    final targetFats = targetMacros['fats'] ?? 0;

    // Seleziona alimenti da ciascuna categoria necessaria
    for (var category in categoriesToInclude) {
      if (foodsByCategory.containsKey(category) && foodsByCategory[category]!.isNotEmpty) {
        // Ordina gli alimenti per contenuto di macronutrienti in base alla categoria
        List<Food> categoryFoods = foodsByCategory[category]!;

        // Usa il variety manager per selezionare alimenti vari
        final variedFoods = _varietyManager.selectVariedFoods(
          categoryFoods,
          maxSelections: 3, // Considera i 3 migliori per varietà
          preferredCategories: [category],
        );

        // Se non ci sono alimenti vari, usa il metodo tradizionale
        if (variedFoods.isEmpty) {
          _sortFoodsByCategory(categoryFoods, category);
          if (usedFoodsCount != null && usedFoodsCount.isNotEmpty) {
            categoryFoods.sort((a, b) {
              final countA = usedFoodsCount[a.id] ?? 0;
              final countB = usedFoodsCount[b.id] ?? 0;
              return countA - countB;
            });
          }
        }

        // Seleziona il miglior alimento dalla lista varia o tradizionale
        final candidateFoods = variedFoods.isNotEmpty ? variedFoods : categoryFoods;
        if (candidateFoods.isNotEmpty) {
          // Seleziona il primo alimento che soddisfa i requisiti nutrizionali
          Food? selectedFood;
          int bestPortion = 0;

          for (final food in candidateFoods.take(3)) { // Prova i primi 3 candidati
            // Calcola la porzione precisa in base ai macronutrienti target
            int portion = _calculatePrecisePortion(
              food,
              category,
              mealType,
              targetCalories - currentCalories,
              {
                'proteins': targetProtein - currentProteins,
                'carbs': targetCarbs - currentCarbs,
                'fats': targetFats - currentFats,
              }
            );

            if (portion >= 20) { // Porzione minima significativa
              selectedFood = food;
              bestPortion = portion;
              break; // Usa il primo alimento valido per massimizzare la varietà
            }
          }

          // Aggiungi l'alimento selezionato se trovato
          if (selectedFood != null && bestPortion > 0) {
            final foodPortion = FoodPortion(food: selectedFood, grams: bestPortion);
            selectedFoods.add(foodPortion);

            // Aggiorna le calorie e i macronutrienti correnti
            currentCalories += foodPortion.calories;
            currentProteins += foodPortion.proteins.round();
            currentCarbs += foodPortion.carbs.round();
            currentFats += foodPortion.fats.round();

            print('Aggiunto ${selectedFood.name} ($bestPortion g): ${foodPortion.calories} kcal, ${foodPortion.proteins.round()} g proteine, ${foodPortion.carbs.round()} g carb, ${foodPortion.fats.round()} g grassi');

            // Registra l'utilizzo dell'alimento
            _varietyManager.recordFoodUsage(selectedFood.id);

            // Rimuovi l'alimento selezionato da tutte le categorie
            for (var cat in foodsByCategory.keys) {
              foodsByCategory[cat]!.removeWhere((f) => f.id == selectedFood!.id);
            }
          }
        }
      }
    }

    // Verifica se abbiamo raggiunto gli obiettivi nutrizionali
    // Evita divisioni per zero
    final caloriePercentage = targetCalories > 0 ? currentCalories / targetCalories : 0.0;
    final proteinPercentage = targetProtein > 0 ? currentProteins / targetProtein : 0.0;
    final carbsPercentage = targetCarbs > 0 ? currentCarbs / targetCarbs : 0.0;
    final fatsPercentage = targetFats > 0 ? currentFats / targetFats : 0.0;

    print('Riepilogo pasto:');
    print('- Calorie: $currentCalories / $targetCalories kcal (${(caloriePercentage * 100).round()}%)');
    print('- Proteine: $currentProteins / $targetProtein g (${(proteinPercentage * 100).round()}%)');
    print('- Carboidrati: $currentCarbs / $targetCarbs g (${(carbsPercentage * 100).round()}%)');
    print('- Grassi: $currentFats / $targetFats g (${(fatsPercentage * 100).round()}%)');

    return selectedFoods;
  }

  /// Ordina gli alimenti in base alla categoria
  void _sortFoodsByCategory(List<Food> foods, FoodCategory category) {
    switch (category) {
      case FoodCategory.protein:
        // Ordina per contenuto proteico decrescente
        foods.sort((a, b) => b.proteins.compareTo(a.proteins));
        break;
      case FoodCategory.grain:
        // Ordina per contenuto di carboidrati decrescente
        foods.sort((a, b) => b.carbs.compareTo(a.carbs));
        break;
      case FoodCategory.vegetable:
        // Ordina per contenuto di fibre decrescente
        foods.sort((a, b) => b.fiber.compareTo(a.fiber));
        break;
      case FoodCategory.fruit:
        // Ordina per contenuto di zuccheri crescente (preferisci frutta meno zuccherina)
        foods.sort((a, b) => a.sugar.compareTo(b.sugar));
        break;
      case FoodCategory.dairy:
        // Ordina per contenuto proteico decrescente
        foods.sort((a, b) => b.proteins.compareTo(a.proteins));
        break;
      case FoodCategory.fat:
        // Ordina per contenuto di grassi decrescente
        foods.sort((a, b) => b.fats.compareTo(a.fats));
        break;
      default:
        // Nessun ordinamento specifico
        break;
    }
  }

  /// Calcola la porzione precisa in base ai macronutrienti target
  int _calculatePrecisePortion(
    Food food,
    FoodCategory category,
    String mealType,
    int remainingCalories,
    Map<String, int> remainingMacros
  ) {
    // Porzioni standard per categoria di alimento
    final Map<FoodCategory, int> standardPortions = {
      FoodCategory.protein: 100, // 100g di proteine (carne, pesce, tofu, ecc.)
      FoodCategory.grain: 80,    // 80g di carboidrati (pasta, riso, ecc.)
      FoodCategory.vegetable: 150, // 150g di verdure
      FoodCategory.fruit: 150,   // 150g di frutta
      FoodCategory.dairy: 150,   // 150g/ml di latticini
      FoodCategory.fat: 15,      // 15g di grassi (olio, burro, ecc.)
      FoodCategory.sweet: 50,    // 50g di dolci
      FoodCategory.beverage: 250, // 250ml di bevande
      FoodCategory.mixed: 200,   // 200g di piatti misti
    };

    // Usa la porzione standard se disponibile, altrimenti usa la porzione dell'alimento
    int basePortion = standardPortions[category] ?? food.servingSizeGrams;

    // Adatta la porzione in base al tipo di pasto
    if (mealType == 'breakfast') {
      // Colazione: porzioni standard
    } else if (mealType == 'lunch') {
      // Pranzo: porzioni leggermente più grandi
      basePortion = (basePortion * 1.2).round();
    } else if (mealType == 'dinner') {
      // Cena: porzioni standard
    } else if (mealType == 'snack') {
      // Spuntino: porzioni più piccole
      basePortion = (basePortion * 0.7).round();
    }

    // Calcola le porzioni in base ai macronutrienti target
    List<int> portionCandidates = [];

    // Calcola la porzione in base alle proteine
    if (remainingMacros['proteins']! > 0 && food.proteins > 0.1) {
      final proteinPortion = (remainingMacros['proteins']! * 100 / food.proteins).round();
      if (proteinPortion > 0 && proteinPortion.isFinite) {
        portionCandidates.add(proteinPortion);
      }
    }

    // Calcola la porzione in base ai carboidrati
    if (remainingMacros['carbs']! > 0 && food.carbs > 0.1) {
      final carbsPortion = (remainingMacros['carbs']! * 100 / food.carbs).round();
      if (carbsPortion > 0 && carbsPortion.isFinite) {
        portionCandidates.add(carbsPortion);
      }
    }

    // Calcola la porzione in base ai grassi
    if (remainingMacros['fats']! > 0 && food.fats > 0.1) {
      final fatsPortion = (remainingMacros['fats']! * 100 / food.fats).round();
      if (fatsPortion > 0 && fatsPortion.isFinite) {
        portionCandidates.add(fatsPortion);
      }
    }

    // Calcola la porzione in base alle calorie
    if (remainingCalories > 0 && food.calories > 0) {
      final caloriesPortion = (remainingCalories * 100 / food.calories).round();
      if (caloriesPortion > 0 && caloriesPortion.isFinite) {
        portionCandidates.add(caloriesPortion);
      }
    }

    // Aggiungi la porzione standard come candidato
    portionCandidates.add(basePortion);

    // Scegli la porzione più appropriata in base alla categoria
    int finalPortion;

    switch (category) {
      case FoodCategory.protein:
        // Per le proteine, priorità al target proteico
        if (portionCandidates.isNotEmpty) {
          try {
            finalPortion = portionCandidates.reduce((a, b) =>
              (a - basePortion).abs() < (b - basePortion).abs() ? a : b);
          } catch (e) {
            print('Errore nel calcolo della porzione per proteine: $e');
            finalPortion = basePortion;
          }
        } else {
          finalPortion = basePortion;
        }
        break;
      case FoodCategory.grain:
        // Per i carboidrati, priorità al target di carboidrati
        if (portionCandidates.isNotEmpty) {
          try {
            finalPortion = portionCandidates.reduce((a, b) =>
              (a - basePortion).abs() < (b - basePortion).abs() ? a : b);
          } catch (e) {
            print('Errore nel calcolo della porzione per carboidrati: $e');
            finalPortion = basePortion;
          }
        } else {
          finalPortion = basePortion;
        }
        break;
      default:
        // Per le altre categorie, usa la porzione standard o la media dei candidati
        if (portionCandidates.isNotEmpty) {
          try {
            final sum = portionCandidates.reduce((a, b) => a + b);
            final avg = sum / portionCandidates.length;
            finalPortion = avg.isFinite ? avg.round() : basePortion;
          } catch (e) {
            print('Errore nel calcolo della porzione media: $e');
            finalPortion = basePortion;
          }
        } else {
          finalPortion = basePortion;
        }
    }

    // Limita la porzione a un valore ragionevole
    finalPortion = min(finalPortion, 300); // Massimo 300g
    finalPortion = max(finalPortion, 20);  // Minimo 20g

    return finalPortion;
  }
}
