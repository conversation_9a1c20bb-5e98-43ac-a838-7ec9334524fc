import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_user.dart';
import '../../models/supabase_profile.dart';
import '../../services/community_profile_service.dart';
import '../../services/friendship_service.dart';
import '../../services/supabase_auth_service.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../widgets/community/user_stats_widget.dart';
import '../../widgets/community/user_badges_widget.dart';
import '../../widgets/community/user_posts_widget.dart';
import '../../widgets/community/edit_profile_modal.dart';
import '../../widgets/community/supabase_edit_profile_modal.dart';

/// Schermata del profilo utente
class UserProfileScreen extends StatefulWidget {
  final CommunityUser? user; // Se null, mostra il profilo dell'utente corrente
  final bool isCurrentUser;

  const UserProfileScreen({
    super.key,
    this.user,
    this.isCurrentUser = false,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  CommunityUser? _displayUser;
  SupabaseProfile? _supabaseProfile;
  bool _isLoading = true;
  bool _isOwnProfile = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  /// Carica i dati dell'utente
  Future<void> _loadUserData() async {
    setState(() => _isLoading = true);

    try {
      // Carica prima i dati reali da Supabase
      final authService = SupabaseAuthService();
      _supabaseProfile = await authService.getCurrentUserProfile();

      print('🔍 PROFILE_SCREEN: Profilo Supabase caricato: $_supabaseProfile');

      final profileService = context.read<CommunityProfileService>();

      if (widget.user != null) {
        _displayUser = widget.user;
      } else {
        // Se abbiamo dati Supabase, crea/aggiorna l'utente community con dati reali
        if (_supabaseProfile != null) {
          _displayUser = await _createCommunityUserFromSupabase(_supabaseProfile!);
        } else {
          // Fallback: carica l'utente corrente o crea uno predefinito
          _displayUser = profileService.currentUser;

          if (_displayUser == null) {
            _displayUser = await profileService.createDefaultUser();
          }
        }
      }

      // Determina se è il proprio profilo
      final currentUser = authService.currentUser;
      if (currentUser != null && _displayUser != null) {
        _isOwnProfile = _displayUser!.id == currentUser.id;
      }

    } catch (e) {
      print('❌ PROFILE_SCREEN: Errore caricamento profilo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore caricamento profilo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Crea un CommunityUser dai dati Supabase
  Future<CommunityUser> _createCommunityUserFromSupabase(SupabaseProfile supabaseProfile) async {
    return CommunityUser(
      id: supabaseProfile.id,
      displayName: supabaseProfile.nome.isNotEmpty ? supabaseProfile.nome : 'Utente',
      username: supabaseProfile.username.isNotEmpty ? supabaseProfile.username : 'user',
      bio: supabaseProfile.bio ?? 'Benvenuto in Staffilano InnerCircle™! 🏥💚',
      avatarUrl: supabaseProfile.fotoProfiloUrl,
      membershipLevel: MembershipLevel.basic,
      joinDate: supabaseProfile.createdAt,
      lastActive: DateTime.now(),
      isVerified: supabaseProfile.isVerified,
      interests: ['Cardiologia', 'Prevenzione', 'Benessere'],
    );
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Profilo'),
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: DrStaffilanoTheme.primaryGreen,
          ),
        ),
      );
    }

    if (_displayUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Profilo'),
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'Profilo non trovato',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    // Sempre usa layout semplificato per evitare errori Material
    return Scaffold(
      appBar: AppBar(
        title: Text(_displayUser!.displayName),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          if (_isOwnProfile)
            IconButton(
              onPressed: _showEditProfile,
              icon: const Icon(Icons.edit),
              tooltip: 'Modifica profilo',
            ),
        ],
      ),
      body: _buildSimpleProfile(),
    );
  }

  /// Costruisce un profilo semplificato per l'uso nelle tab
  Widget _buildSimpleProfile() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Header semplificato
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.white,
                  backgroundImage: _displayUser!.avatarUrl != null
                      ? NetworkImage(_displayUser!.avatarUrl!)
                      : null,
                  child: _displayUser!.avatarUrl == null
                      ? Text(
                          _displayUser!.displayName.isNotEmpty
                              ? _displayUser!.displayName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: DrStaffilanoTheme.primaryGreen,
                          ),
                        )
                      : null,
                ),
                const SizedBox(height: 12),

                // Nome e username
                Text(
                  _displayUser!.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '@${_displayUser!.username}',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),

                const SizedBox(height: 8),

                // Membership level
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_displayUser!.membershipLevel.icon} ${_displayUser!.membershipLevel.displayName}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Statistiche rapide
          _buildQuickStats(),

          const SizedBox(height: 20),

          // Interessi
          if (_displayUser!.interests.isNotEmpty) ...[
            _buildSectionTitle('Interessi'),
            const SizedBox(height: 12),
            _buildInterestsChips(),
            const SizedBox(height: 20),
          ],

          // Informazioni
          _buildSectionTitle('Informazioni'),
          const SizedBox(height: 12),
          _buildInfoCards(),
        ],
      ),
    );
  }



  /// Costruisce le statistiche rapide
  Widget _buildQuickStats() {
    return Consumer<CommunityProfileService>(
      builder: (context, profileService, child) {
        final stats = profileService.getUserStats(_displayUser!.id);

        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Post',
                stats['totalPosts']?.toString() ?? '0',
                Icons.article,
                DrStaffilanoTheme.primaryGreen,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Like',
                stats['totalLikes']?.toString() ?? '0',
                Icons.favorite,
                Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Streak',
                '${stats['streak'] ?? 0} giorni',
                Icons.local_fire_department,
                Colors.orange,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Costruisce una card delle statistiche
  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce il titolo di una sezione
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  /// Costruisce i chip degli interessi
  Widget _buildInterestsChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _displayUser!.interests.map((interest) {
        return Chip(
          label: Text(
            interest,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          backgroundColor: DrStaffilanoTheme.secondaryBlue,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );
      }).toList(),
    );
  }

  /// Costruisce le card informative
  Widget _buildInfoCards() {
    return Column(
      children: [
        _buildInfoCard(
          'Membro da',
          _formatDate(_displayUser!.joinDate),
          Icons.calendar_today,
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          'Ultimo accesso',
          _formatLastActive(_displayUser!.lastActive),
          Icons.access_time,
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          'Punti Community',
          _displayUser!.communityPoints.toString(),
          Icons.stars,
        ),
        if (_displayUser!.followersCount > 0) ...[
          const SizedBox(height: 12),
          _buildInfoCard(
            'Follower',
            _displayUser!.followersCount.toString(),
            Icons.people,
          ),
        ],
      ],
    );
  }

  /// Costruisce una card informativa
  Widget _buildInfoCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: DrStaffilanoTheme.primaryGreen,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce il pulsante di amicizia
  Widget _buildFriendshipButton() {
    return Consumer<FriendshipService>(
      builder: (context, friendshipService, child) {
        final currentUser = context.read<CommunityProfileService>().currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        final areFriends = friendshipService.areFriends(currentUser.id, _displayUser!.id);
        final hasOutgoingRequest = friendshipService.getOutgoingRequests(currentUser.id)
            .any((req) => req.toUserId == _displayUser!.id);

        if (areFriends) {
          return IconButton(
            onPressed: () => _showFriendOptions(),
            icon: const Icon(Icons.people),
            tooltip: 'Opzioni amicizia',
          );
        } else if (hasOutgoingRequest) {
          return IconButton(
            onPressed: null,
            icon: const Icon(Icons.hourglass_empty),
            tooltip: 'Richiesta inviata',
          );
        } else {
          return IconButton(
            onPressed: () => _sendFriendRequest(),
            icon: const Icon(Icons.person_add),
            tooltip: 'Aggiungi amico',
          );
        }
      },
    );
  }

  /// Mostra il modal di modifica profilo
  void _showEditProfile() {
    showDialog(
      context: context,
      builder: (context) => SupabaseEditProfileModal(
        currentProfile: _supabaseProfile,
        onProfileUpdated: () {
          // Ricarica i dati dopo la modifica
          _loadUserData();
        },
      ),
    );
  }

  /// Mostra le opzioni per gli amici
  void _showFriendOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.message, color: DrStaffilanoTheme.primaryGreen),
              title: const Text('Invia messaggio'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implementa messaggi
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('💬 Messaggi - Coming soon!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_remove, color: Colors.red),
              title: const Text('Rimuovi amico'),
              onTap: () {
                Navigator.pop(context);
                _removeFriend();
              },
            ),
            ListTile(
              leading: const Icon(Icons.block, color: Colors.red),
              title: const Text('Blocca utente'),
              onTap: () {
                Navigator.pop(context);
                _blockUser();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Invia una richiesta di amicizia
  Future<void> _sendFriendRequest() async {
    try {
      final currentUser = context.read<CommunityProfileService>().currentUser;
      if (currentUser == null) return;

      final friendshipService = context.read<FriendshipService>();
      final success = await friendshipService.sendFriendRequest(
        fromUserId: currentUser.id,
        toUserId: _displayUser!.id,
        message: 'Ciao! Vorrei aggiungerti come amico su Staffilano InnerCircle™',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('📨 Richiesta di amicizia inviata a ${_displayUser!.displayName}'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Rimuove un amico
  Future<void> _removeFriend() async {
    try {
      final currentUser = context.read<CommunityProfileService>().currentUser;
      if (currentUser == null) return;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Rimuovi amico'),
          content: Text('Sei sicuro di voler rimuovere ${_displayUser!.displayName} dai tuoi amici?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Annulla'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Rimuovi', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final friendshipService = context.read<FriendshipService>();
        final success = await friendshipService.removeFriend(currentUser.id, _displayUser!.id);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('💔 ${_displayUser!.displayName} rimosso dagli amici'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Blocca un utente
  Future<void> _blockUser() async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Blocca utente'),
          content: Text('Sei sicuro di voler bloccare ${_displayUser!.displayName}? Non potrai più vedere i suoi contenuti.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Annulla'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Blocca', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final friendshipService = context.read<FriendshipService>();
        final success = await friendshipService.blockUser(_displayUser!.id);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('🚫 ${_displayUser!.displayName} bloccato'),
              backgroundColor: Colors.red,
            ),
          );
          Navigator.pop(context); // Torna indietro
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Formatta una data
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 30) {
      return '${difference.inDays} giorni fa';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'mese' : 'mesi'} fa';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'anno' : 'anni'} fa';
    }
  }

  /// Formatta l'ultimo accesso
  String _formatLastActive(DateTime lastActive) {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inMinutes < 5) {
      return 'Online ora';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minuti fa';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} ore fa';
    } else {
      return '${difference.inDays} giorni fa';
    }
  }
}
