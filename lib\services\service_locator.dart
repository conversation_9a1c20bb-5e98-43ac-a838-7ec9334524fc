import 'package:get_it/get_it.dart';
import 'food_database_service.dart';
import 'diet_generator_service.dart';
import 'advanced_diet_generator_service.dart';
import 'user_profile_service.dart';
import 'storage_service.dart';
import '../controllers/advanced_diet_controller.dart';
import '../controllers/welljourney_controller.dart';
import '../ai/services/ai_service.dart';

/// Singleton per la gestione delle dipendenze
final GetIt serviceLocator = GetIt.instance;

/// Inizializza il service locator
Future<void> setupServiceLocator() async {
  // Servizi
  final foodDatabaseService = await FoodDatabaseService.getInstance();
  serviceLocator.registerSingleton<FoodDatabaseService>(foodDatabaseService);

  final dietGeneratorService = await DietGeneratorService.getInstance();
  serviceLocator.registerSingleton<DietGeneratorService>(dietGeneratorService);

  final advancedDietGeneratorService = await AdvancedDietGeneratorService.getInstance();
  serviceLocator.registerSingleton<AdvancedDietGeneratorService>(advancedDietGeneratorService);

  final userProfileService = UserProfileService();
  serviceLocator.registerSingleton<UserProfileService>(userProfileService);

  final storageService = await StorageService.getInstance();
  serviceLocator.registerSingleton<StorageService>(storageService);

  final aiService = await AIService.getInstance();
  serviceLocator.registerSingleton<AIService>(aiService);

  // Controllers
  serviceLocator.registerFactory<AdvancedDietController>(() => AdvancedDietController(
    dietGeneratorService: serviceLocator<AdvancedDietGeneratorService>(),
    foodDatabaseService: serviceLocator<FoodDatabaseService>(),
  ));

  serviceLocator.registerSingleton<WellJourneyController>(WellJourneyController());
}
