import 'dart:convert';

enum Gender { male, female }

enum ActivityLevel {
  sedentary,
  lightlyActive,
  moderatelyActive,
  veryActive,
  extremelyActive
}

enum Goal { weightLoss, maintenance, weightGain }
// Alias per compatibilità con il codice esistente
typedef DietGoal = Goal;

enum DietType { omnivore, vegetarian, vegan, pescatarian, keto, paleo }

class UserProfile {
  final String id;
  final String name;
  final int age;
  final Gender gender;
  final int height; // in cm
  final int weight; // in kg
  final ActivityLevel activityLevel;
  final Goal goal;
  final DietType dietType;
  final List<String> allergies;
  final List<String> dislikedFoods;
  final List<String> dietaryPreferences;
  final int mealsPerDay;
  final int targetWeight; // in kg, optional for weight loss/gain
  final double weeklyGoal; // in kg per week, for weight loss/gain

  UserProfile({
    required this.id,
    this.name = 'Utente',
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.activityLevel,
    required this.goal,
    this.dietType = DietType.omnivore,
    this.allergies = const [],
    this.dislikedFoods = const [],
    this.dietaryPreferences = const [],
    this.mealsPerDay = 3,
    this.targetWeight = 0,
    this.weeklyGoal = 0.5,
  });

  // Calcola il BMI (Indice di Massa Corporea)
  double get bmi {
    // BMI = peso (kg) / (altezza (m))²
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  // Ottieni la categoria di BMI
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue < 18.5) {
      return 'Sottopeso';
    } else if (bmiValue < 25) {
      return 'Normopeso';
    } else if (bmiValue < 30) {
      return 'Sovrappeso';
    } else {
      return 'Obesità';
    }
  }

  // Calcola il BMR (Metabolismo Basale) usando la formula di Mifflin-St Jeor
  double calculateBMR() {
    if (gender == Gender.male) {
      return (10 * weight) + (6.25 * height) - (5 * age) + 5;
    } else {
      return (10 * weight) + (6.25 * height) - (5 * age) - 161;
    }
  }

  // Ottieni il moltiplicatore per il livello di attività
  double get activityMultiplier {
    switch (activityLevel) {
      case ActivityLevel.sedentary:
        return 1.2; // Poco o nessun esercizio
      case ActivityLevel.lightlyActive:
        return 1.375; // Esercizio leggero 1-3 volte/settimana
      case ActivityLevel.moderatelyActive:
        return 1.55; // Esercizio moderato 3-5 volte/settimana
      case ActivityLevel.veryActive:
        return 1.725; // Esercizio intenso 6-7 volte/settimana
      case ActivityLevel.extremelyActive:
        return 1.9; // Esercizio molto intenso, lavoro fisico
    }
  }

  // Calcola il TDEE (Fabbisogno Calorico Giornaliero Totale)
  int calculateTDEE() {
    return (calculateBMR() * activityMultiplier).round();
  }

  // Calcola l'obiettivo calorico giornaliero in base all'obiettivo
  int calculateCalorieTarget() {
    final tdee = calculateTDEE();

    switch (goal) {
      case Goal.weightLoss:
        // Deficit calorico per perdita di peso (500-750 kcal/giorno per perdere ~0.5-0.75 kg/settimana)
        final deficit = (weeklyGoal * 1000).round(); // 1 kg di grasso = ~7700 kcal
        final target = tdee - (deficit ~/ 7);

        // Assicurati che l'obiettivo non sia troppo basso (minimo 1200 per donne, 1500 per uomini)
        final minCalories = gender == Gender.female ? 1200 : 1500;
        return target < minCalories ? minCalories : target;

      case Goal.maintenance:
        return tdee;

      case Goal.weightGain:
        // Surplus calorico per aumento di massa (250-500 kcal/giorno)
        final surplus = (weeklyGoal * 1000).round();
        return tdee + (surplus ~/ 7);
    }
  }

  // Calcola la distribuzione dei macronutrienti in base all'obiettivo
  Map<String, double> calculateMacroDistribution() {
    switch (goal) {
      case Goal.weightLoss:
        return {
          'proteine': 0.35, // 35% proteine per sazietà e preservazione muscolare
          'carboidrati': 0.40, // 40% carboidrati
          'grassi': 0.25, // 25% grassi
          'proteins': 0.35, // Inglese
          'carbs': 0.40, // Inglese
          'fats': 0.25, // Inglese
        };

      case Goal.maintenance:
        return {
          'proteine': 0.30, // 30% proteine
          'carboidrati': 0.45, // 45% carboidrati
          'grassi': 0.25, // 25% grassi
          'proteins': 0.30, // Inglese
          'carbs': 0.45, // Inglese
          'fats': 0.25, // Inglese
        };

      case Goal.weightGain:
        return {
          'proteine': 0.30, // 30% proteine per costruzione muscolare
          'carboidrati': 0.50, // 50% carboidrati per energia
          'grassi': 0.20, // 20% grassi
          'proteins': 0.30, // Inglese
          'carbs': 0.50, // Inglese
          'fats': 0.20, // Inglese
        };
    }
  }

  // Calcola i grammi di macronutrienti in base all'obiettivo calorico
  Map<String, int> calculateMacroGrams() {
    final calorieTarget = calculateCalorieTarget();
    final macroDistribution = calculateMacroDistribution();

    // Verifica che la mappa contenga tutte le chiavi necessarie
    if (!macroDistribution.containsKey('proteine') ||
        !macroDistribution.containsKey('carboidrati') ||
        !macroDistribution.containsKey('grassi')) {
      print('ERRORE: La mappa macroDistribution non contiene tutte le chiavi necessarie: $macroDistribution');

      // Usa valori predefiniti se mancano le chiavi
      return {
        'proteine': 50,     // Valore predefinito
        'carboidrati': 100, // Valore predefinito
        'grassi': 30,       // Valore predefinito
        'proteins': 50,     // Valore predefinito (inglese)
        'carbs': 100,       // Valore predefinito (inglese)
        'fats': 30,         // Valore predefinito (inglese)
      };
    }

    // 1g proteine = 4 kcal, 1g carboidrati = 4 kcal, 1g grassi = 9 kcal
    final proteineValue = macroDistribution['proteine'] ?? 0.3; // Valore predefinito 30%
    final carboidratiValue = macroDistribution['carboidrati'] ?? 0.4; // Valore predefinito 40%
    final grassiValue = macroDistribution['grassi'] ?? 0.3; // Valore predefinito 30%

    final proteineGrams = ((calorieTarget * proteineValue) / 4).round();
    final carboidratiGrams = ((calorieTarget * carboidratiValue) / 4).round();
    final grassiGrams = ((calorieTarget * grassiValue) / 9).round();

    return {
      'proteine': proteineGrams,     // Italiano
      'carboidrati': carboidratiGrams, // Italiano
      'grassi': grassiGrams,         // Italiano
      'proteins': proteineGrams,     // Inglese
      'carbs': carboidratiGrams,     // Inglese
      'fats': grassiGrams,           // Inglese
    };
  }

  // Converti da UserProfile a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'gender': gender.toString().split('.').last,
      'height': height,
      'weight': weight,
      'activityLevel': activityLevel.toString().split('.').last,
      'dietGoal': goal.toString().split('.').last,
      'dietType': dietType.toString().split('.').last,
      'allergies': allergies,
      'dislikedFoods': dislikedFoods,
      'mealsPerDay': mealsPerDay,
      'targetWeight': targetWeight,
      'weeklyGoal': weeklyGoal,
    };
  }

  // Converti da Map a UserProfile
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id'] as String,
      name: map['name'] as String? ?? 'Utente',
      age: map['age'] as int,
      gender: Gender.values.firstWhere(
        (e) => e.toString().split('.').last == map['gender'],
      ),
      height: (map['height'] is int) ? map['height'] : (map['height'] as double).toInt(),
      weight: (map['weight'] is int) ? map['weight'] : (map['weight'] as double).toInt(),
      activityLevel: ActivityLevel.values.firstWhere(
        (e) => e.toString().split('.').last == map['activityLevel'],
      ),
      goal: Goal.values.firstWhere(
        (e) => e.toString().split('.').last == map['dietGoal'],
        orElse: () => Goal.maintenance,
      ),
      dietType: DietType.values.firstWhere(
        (e) => e.toString().split('.').last == map['dietType'],
      ),
      allergies: List<String>.from(map['allergies']),
      dislikedFoods: List<String>.from(map['dislikedFoods']),
      mealsPerDay: map['mealsPerDay'] as int,
      targetWeight: map['targetWeight'] != null ? (map['targetWeight'] is int ? map['targetWeight'] : map['targetWeight'] as double) : 0,
      weeklyGoal: map['weeklyGoal'] != null ? (map['weeklyGoal'] is int ? map['weeklyGoal'].toDouble() : map['weeklyGoal'] as double) : 0.5,
    );
  }

  // Converti da UserProfile a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a UserProfile
  factory UserProfile.fromJson(String source) =>
      UserProfile.fromMap(json.decode(source) as Map<String, dynamic>);

  // Crea una copia del profilo con possibilità di sovrascrivere alcuni campi
  UserProfile copyWith({
    String? id,
    String? name,
    int? age,
    Gender? gender,
    int? height,
    int? weight,
    ActivityLevel? activityLevel,
    Goal? goal,
    DietType? dietType,
    List<String>? allergies,
    List<String>? dislikedFoods,
    List<String>? dietaryPreferences,
    int? mealsPerDay,
    int? targetWeight,
    double? weeklyGoal,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      activityLevel: activityLevel ?? this.activityLevel,
      goal: goal ?? this.goal,
      dietType: dietType ?? this.dietType,
      allergies: allergies ?? this.allergies,
      dislikedFoods: dislikedFoods ?? this.dislikedFoods,
      dietaryPreferences: dietaryPreferences ?? this.dietaryPreferences,
      mealsPerDay: mealsPerDay ?? this.mealsPerDay,
      targetWeight: targetWeight ?? this.targetWeight,
      weeklyGoal: weeklyGoal ?? this.weeklyGoal,
    );
  }
}
