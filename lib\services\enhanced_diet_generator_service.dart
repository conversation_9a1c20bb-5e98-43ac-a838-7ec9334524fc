import 'dart:math';
import '../models/user_profile.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../models/planned_meal.dart';
import '../models/food_portion.dart';
import '../data/food_database.dart';
import '../data/expanded_italian_foods.dart';
import 'meal_appropriateness_validator.dart';
import 'athletic_food_selector_service.dart';

/// Servizio di generazione diete potenziato con validazione appropriatezza pasti
/// e database espanso di alimenti italiani
class EnhancedDietGeneratorService {
  static EnhancedDietGeneratorService? _instance;
  late FoodDatabase _foodDatabase;
  final Random _random = Random();

  EnhancedDietGeneratorService._();

  static Future<EnhancedDietGeneratorService> getInstance() async {
    if (_instance == null) {
      _instance = EnhancedDietGeneratorService._();
      await _instance!._initialize();
    }
    return _instance!;
  }

  Future<void> _initialize() async {
    _foodDatabase = FoodDatabase();
    await _foodDatabase.initializeDatabase();
    print('Enhanced Diet Generator Service inizializzato');
  }

  /// Genera un piano dietetico settimanale con validazione appropriatezza
  Future<WeeklyDietPlan> generateWeeklyDietPlan(UserProfile userProfile) async {
    print('Generazione piano settimanale potenziato per ${userProfile.name}');
    
    final dailyPlans = <DailyDietPlan>[];
    final startDate = DateTime.now();

    for (int day = 0; day < 7; day++) {
      final currentDate = startDate.add(Duration(days: day));
      final dailyPlan = await generateDailyDietPlan(userProfile, currentDate);
      dailyPlans.add(dailyPlan);
    }

    return WeeklyDietPlan(
      id: 'weekly_${DateTime.now().millisecondsSinceEpoch}',
      userId: userProfile.id,
      startDate: startDate,
      endDate: startDate.add(const Duration(days: 6)),
      dailyPlans: dailyPlans,
      totalCalories: dailyPlans.fold(0, (sum, plan) => sum + plan.calorieTarget),
      createdAt: DateTime.now(),
    );
  }

  /// Genera un piano dietetico giornaliero con appropriatezza garantita
  Future<DailyDietPlan> generateDailyDietPlan(UserProfile userProfile, DateTime date) async {
    print('Generazione piano giornaliero per ${date.toIso8601String().split('T')[0]}');

    final calorieTarget = userProfile.calculateCalorieTarget();
    final macroGrams = userProfile.calculateMacroGrams();
    
    // Calcola calorie per pasto
    final mealCalories = _distributeMealCalories(calorieTarget, userProfile.mealsPerDay);
    
    final meals = <PlannedMeal>[];
    
    // Genera ogni pasto con validazione appropriatezza
    for (int i = 0; i < userProfile.mealsPerDay; i++) {
      final mealType = _getMealTypeForIndex(i, userProfile.mealsPerDay);
      final mealCalorieTarget = mealCalories[i];
      
      final meal = await _generateMealWithValidation(
        userProfile,
        mealType,
        mealCalorieTarget,
        macroGrams,
        date,
      );
      
      meals.add(meal);
    }

    return DailyDietPlan(
      id: 'daily_${date.millisecondsSinceEpoch}',
      userId: userProfile.id,
      date: date,
      calorieTarget: calorieTarget,
      targetMacros: macroGrams,
      meals: meals,
      createdAt: DateTime.now(),
    );
  }

  /// Genera un pasto con validazione completa dell'appropriatezza
  Future<PlannedMeal> _generateMealWithValidation(
    UserProfile userProfile,
    MealType mealType,
    int calorieTarget,
    Map<String, int> dailyMacros,
    DateTime date,
  ) async {
    print('Generazione pasto ${mealType.toString().split('.').last} con validazione');

    // Ottieni alimenti appropriati per questo tipo di pasto
    final appropriateFoods = await _getAppropriateFoods(userProfile, mealType);
    
    if (appropriateFoods.isEmpty) {
      throw Exception('Nessun alimento appropriato trovato per ${mealType.toString().split('.').last}');
    }

    // Calcola macro target per questo pasto
    final mealMacros = _calculateMealMacros(dailyMacros, userProfile.mealsPerDay);
    
    // Seleziona alimenti con algoritmo migliorato
    final selectedFoods = await _selectFoodsForMeal(
      appropriateFoods,
      userProfile,
      mealType,
      calorieTarget,
      mealMacros,
    );

    // Calcola totali effettivi
    final totalMacros = _calculateTotalMacros(selectedFoods);
    final totalCalories = _calculateTotalCalories(selectedFoods);

    return PlannedMeal(
      id: 'meal_${DateTime.now().millisecondsSinceEpoch}_${mealType.toString().split('.').last}',
      mealType: mealType,
      targetCalories: calorieTarget,
      actualCalories: totalCalories,
      targetMacros: mealMacros,
      totalMacros: totalMacros,
      foods: selectedFoods,
      mealTime: _getMealTime(mealType),
      isCompleted: false,
    );
  }

  /// Ottieni alimenti appropriati per tipo di pasto con database espanso
  Future<List<Food>> _getAppropriateFoods(UserProfile userProfile, MealType mealType) async {
    // Combina database esistente con alimenti espansi
    final existingFoods = await _foodDatabase.getAllFoods();
    final expandedFoods = ExpandedItalianFoods.getAllExpandedFoods();
    
    // Unisci evitando duplicati
    final allFoods = <Food>[];
    final existingIds = <String>{};
    
    // Aggiungi prima gli alimenti espansi (priorità)
    for (final food in expandedFoods) {
      if (!existingIds.contains(food.id)) {
        allFoods.add(food);
        existingIds.add(food.id);
      }
    }
    
    // Aggiungi alimenti esistenti non duplicati
    for (final food in existingFoods) {
      if (!existingIds.contains(food.id)) {
        allFoods.add(food);
        existingIds.add(food.id);
      }
    }

    print('Database combinato: ${allFoods.length} alimenti totali');

    // Filtra per tipo di pasto con validazione appropriatezza
    final mealTypeFoods = allFoods
        .where((food) => food.suitableForMeals.contains(mealType))
        .toList();

    // Applica validazione appropriatezza rigorosa
    final appropriateFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      mealTypeFoods,
      mealType,
    );

    print('Alimenti appropriati per ${mealType.toString().split('.').last}: ${appropriateFoods.length}');

    // Applica selezione atletica se necessario
    if (_isAthlete(userProfile)) {
      return AthleticFoodSelectorService.selectHighProteinItalianFoods(
        appropriateFoods,
        userProfile,
        mealType,
      );
    }

    return appropriateFoods;
  }

  /// Seleziona alimenti per un pasto specifico
  Future<List<FoodPortion>> _selectFoodsForMeal(
    List<Food> availableFoods,
    UserProfile userProfile,
    MealType mealType,
    int calorieTarget,
    Map<String, int> macroTarget,
  ) async {
    final selectedFoods = <FoodPortion>[];
    int remainingCalories = calorieTarget;
    
    // Strategia di selezione basata sul tipo di pasto
    switch (mealType) {
      case MealType.breakfast:
        return _selectBreakfastFoods(availableFoods, calorieTarget, macroTarget);
      case MealType.lunch:
        return _selectLunchFoods(availableFoods, calorieTarget, macroTarget);
      case MealType.dinner:
        return _selectDinnerFoods(availableFoods, calorieTarget, macroTarget);
      case MealType.snack:
        return _selectSnackFoods(availableFoods, calorieTarget, macroTarget);
    }
  }

  /// Selezione specifica per colazione
  List<FoodPortion> _selectBreakfastFoods(
    List<Food> foods,
    int calorieTarget,
    Map<String, int> macroTarget,
  ) {
    final selected = <FoodPortion>[];
    
    // Priorità: cereali/pane + latticini + frutta
    final cereals = foods.where((f) => f.categories.contains(FoodCategory.grain)).toList();
    final dairy = foods.where((f) => f.categories.contains(FoodCategory.dairy)).toList();
    final fruits = foods.where((f) => f.categories.contains(FoodCategory.fruit)).toList();
    
    int remainingCalories = calorieTarget;
    
    // Seleziona cereale base (40% calorie)
    if (cereals.isNotEmpty) {
      final cereal = cereals[_random.nextInt(cereals.length)];
      final targetCalories = (calorieTarget * 0.4).round();
      final portion = _calculateOptimalPortion(cereal, targetCalories);
      selected.add(portion);
      remainingCalories -= portion.calories.round();
    }
    
    // Seleziona latticino (30% calorie)
    if (dairy.isNotEmpty && remainingCalories > 0) {
      final dairyFood = dairy[_random.nextInt(dairy.length)];
      final targetCalories = (calorieTarget * 0.3).round();
      final portion = _calculateOptimalPortion(dairyFood, targetCalories);
      selected.add(portion);
      remainingCalories -= portion.calories.round();
    }
    
    // Seleziona frutta (30% calorie rimanenti)
    if (fruits.isNotEmpty && remainingCalories > 0) {
      final fruit = fruits[_random.nextInt(fruits.length)];
      final portion = _calculateOptimalPortion(fruit, remainingCalories);
      selected.add(portion);
    }
    
    return selected;
  }

  /// Selezione specifica per spuntini (con validazione rigorosa)
  List<FoodPortion> _selectSnackFoods(
    List<Food> foods,
    int calorieTarget,
    Map<String, int> macroTarget,
  ) {
    // Per spuntini, seleziona solo 1-2 alimenti leggeri
    final lightFoods = foods.where((food) {
      final caloriesPerServing = (food.calories * food.servingSizeGrams / 100).round();
      return caloriesPerServing <= 200 && // Max 200 calorie per porzione
             food.servingSizeGrams <= 150; // Max 150g per porzione
    }).toList();
    
    if (lightFoods.isEmpty) {
      return [];
    }
    
    final selected = <FoodPortion>[];
    final selectedFood = lightFoods[_random.nextInt(lightFoods.length)];
    
    // Calcola porzione appropriata per spuntino
    final maxCalories = math.min(calorieTarget, 200);
    final portion = _calculateOptimalPortion(selectedFood, maxCalories);
    selected.add(portion);
    
    return selected;
  }

  /// Metodi di utilità
  bool _isAthlete(UserProfile userProfile) {
    return userProfile.activityLevel == ActivityLevel.veryActive ||
           userProfile.activityLevel == ActivityLevel.extremelyActive;
  }

  List<int> _distributeMealCalories(int totalCalories, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return [
          (totalCalories * 0.25).round(), // Colazione 25%
          (totalCalories * 0.40).round(), // Pranzo 40%
          (totalCalories * 0.35).round(), // Cena 35%
        ];
      case 4:
        return [
          (totalCalories * 0.25).round(), // Colazione 25%
          (totalCalories * 0.35).round(), // Pranzo 35%
          (totalCalories * 0.10).round(), // Spuntino 10%
          (totalCalories * 0.30).round(), // Cena 30%
        ];
      case 5:
        return [
          (totalCalories * 0.20).round(), // Colazione 20%
          (totalCalories * 0.10).round(), // Spuntino mattina 10%
          (totalCalories * 0.35).round(), // Pranzo 35%
          (totalCalories * 0.10).round(), // Spuntino pomeriggio 10%
          (totalCalories * 0.25).round(), // Cena 25%
        ];
      default:
        final caloriesPerMeal = (totalCalories / mealsPerDay).round();
        return List.filled(mealsPerDay, caloriesPerMeal);
    }
  }

  MealType _getMealTypeForIndex(int index, int totalMeals) {
    switch (totalMeals) {
      case 3:
        return [MealType.breakfast, MealType.lunch, MealType.dinner][index];
      case 4:
        return [MealType.breakfast, MealType.lunch, MealType.snack, MealType.dinner][index];
      case 5:
        return [MealType.breakfast, MealType.snack, MealType.lunch, MealType.snack, MealType.dinner][index];
      default:
        if (index == 0) return MealType.breakfast;
        if (index == totalMeals - 1) return MealType.dinner;
        if (index == (totalMeals / 2).floor()) return MealType.lunch;
        return MealType.snack;
    }
  }

  // Placeholder methods - da implementare completamente
  List<FoodPortion> _selectLunchFoods(List<Food> foods, int calorieTarget, Map<String, int> macroTarget) {
    // Implementazione semplificata
    return _selectBreakfastFoods(foods, calorieTarget, macroTarget);
  }

  List<FoodPortion> _selectDinnerFoods(List<Food> foods, int calorieTarget, Map<String, int> macroTarget) {
    // Implementazione semplificata
    return _selectBreakfastFoods(foods, calorieTarget, macroTarget);
  }

  Map<String, int> _calculateMealMacros(Map<String, int> dailyMacros, int mealsPerDay) {
    return dailyMacros.map((key, value) => MapEntry(key, (value / mealsPerDay).round()));
  }

  FoodPortion _calculateOptimalPortion(Food food, int targetCalories) {
    final gramsNeeded = (targetCalories * 100 / food.calories).round();
    return FoodPortion(
      food: food,
      grams: gramsNeeded,
      calories: targetCalories.toDouble(),
      proteins: (food.proteins * gramsNeeded / 100),
      carbs: (food.carbs * gramsNeeded / 100),
      fats: (food.fats * gramsNeeded / 100),
    );
  }

  Map<String, double> _calculateTotalMacros(List<FoodPortion> foods) {
    return {
      'proteins': foods.fold(0.0, (sum, food) => sum + food.proteins),
      'carbs': foods.fold(0.0, (sum, food) => sum + food.carbs),
      'fats': foods.fold(0.0, (sum, food) => sum + food.fats),
    };
  }

  int _calculateTotalCalories(List<FoodPortion> foods) {
    return foods.fold(0, (sum, food) => sum + food.calories.round());
  }

  String _getMealTime(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast: return '08:00';
      case MealType.lunch: return '13:00';
      case MealType.dinner: return '20:00';
      case MealType.snack: return '16:00';
    }
  }
}
