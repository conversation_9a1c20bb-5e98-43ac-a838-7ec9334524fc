import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../data/specific_diet_foods.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';
import 'meal_appropriateness_validator.dart';

/// Enum per i ruoli nutrizionali degli alimenti
enum NutritionalRole {
  primaryProtein,    // Proteine principali (carne, pesce, uova, legumi)
  secondaryProtein,  // Proteine secondarie (latticini, frutta secca)
  complexCarbs,      // Carboidrati complessi (cereali integrali, patate)
  simpleCarbs,       // Carboidrati semplici (frutta)
  healthyFats,       // Grassi sani (olio EVO, avocado, frutta secca)
  vegetables,        // Verdure
  condiments,        // Condimenti
}

/// Struttura per la composizione ideale di un pasto
class MealComposition {
  final String mealType;
  final Map<NutritionalRole, double> rolePercentages; // Percentuali caloriche per ruolo
  final Map<NutritionalRole, int> maxItemsPerRole;    // Massimo numero di alimenti per ruolo
  final List<NutritionalRole> requiredRoles;          // Ruoli obbligatori
  final List<NutritionalRole> optionalRoles;          // Ruoli opzionali

  MealComposition({
    required this.mealType,
    required this.rolePercentages,
    required this.maxItemsPerRole,
    required this.requiredRoles,
    required this.optionalRoles,
  });
}

/// Servizio specializzato per la generazione di diete utilizzando solo gli alimenti specifici
class SpecificDietGeneratorService {
  final _uuid = Uuid();
  final Random _random = Random();

  // Singleton
  static SpecificDietGeneratorService? _instance;

  // COMPOSIZIONI IDEALI DEI PASTI SECONDO LOGICA NUTRIZIONALE
  static final Map<String, MealComposition> _mealCompositions = {
    'breakfast': MealComposition(
      mealType: 'breakfast',
      rolePercentages: {
        NutritionalRole.primaryProtein: 0.30,    // 30% proteine (yogurt, uova, fiocchi latte)
        NutritionalRole.complexCarbs: 0.50,      // 50% carboidrati complessi (avena, pane integrale)
        NutritionalRole.healthyFats: 0.15,       // 15% grassi sani (frutta secca, semi)
        NutritionalRole.simpleCarbs: 0.05,       // 5% frutta fresca (opzionale)
      },
      maxItemsPerRole: {
        NutritionalRole.primaryProtein: 1,       // MAX 1 fonte proteica principale
        NutritionalRole.complexCarbs: 1,         // MAX 1 fonte di carboidrati
        NutritionalRole.healthyFats: 1,          // MAX 1 fonte di grassi
        NutritionalRole.simpleCarbs: 1,          // MAX 1 frutto
      },
      requiredRoles: [
        NutritionalRole.primaryProtein,
        NutritionalRole.complexCarbs,
      ],
      optionalRoles: [
        NutritionalRole.healthyFats,
        NutritionalRole.simpleCarbs,
      ],
    ),

    'lunch': MealComposition(
      mealType: 'lunch',
      rolePercentages: {
        NutritionalRole.primaryProtein: 0.25,    // 25% proteine magre
        NutritionalRole.complexCarbs: 0.25,      // 25% carboidrati integrali
        NutritionalRole.vegetables: 0.40,        // 40% verdure (metà piatto)
        NutritionalRole.healthyFats: 0.10,       // 10% olio EVO
      },
      maxItemsPerRole: {
        NutritionalRole.primaryProtein: 1,       // MAX 1 fonte proteica
        NutritionalRole.complexCarbs: 1,         // MAX 1 fonte di carboidrati
        NutritionalRole.vegetables: 2,           // MAX 2 tipi di verdure
        NutritionalRole.healthyFats: 1,          // MAX 1 condimento grasso
      },
      requiredRoles: [
        NutritionalRole.primaryProtein,
        NutritionalRole.complexCarbs,
        NutritionalRole.vegetables,
        NutritionalRole.healthyFats,
      ],
      optionalRoles: [],
    ),

    'dinner': MealComposition(
      mealType: 'dinner',
      rolePercentages: {
        NutritionalRole.primaryProtein: 0.30,    // 30% proteine (più del pranzo)
        NutritionalRole.complexCarbs: 0.20,      // 20% carboidrati (meno del pranzo)
        NutritionalRole.vegetables: 0.45,        // 45% verdure (più del pranzo)
        NutritionalRole.healthyFats: 0.05,       // 5% grassi (meno del pranzo)
      },
      maxItemsPerRole: {
        NutritionalRole.primaryProtein: 1,
        NutritionalRole.complexCarbs: 1,
        NutritionalRole.vegetables: 2,
        NutritionalRole.healthyFats: 1,
      },
      requiredRoles: [
        NutritionalRole.primaryProtein,
        NutritionalRole.vegetables,
        NutritionalRole.healthyFats,
      ],
      optionalRoles: [
        NutritionalRole.complexCarbs,  // Carboidrati opzionali a cena
      ],
    ),

    'snack': MealComposition(
      mealType: 'snack',
      rolePercentages: {
        NutritionalRole.primaryProtein: 0.40,    // 40% proteine per sazietà
        NutritionalRole.simpleCarbs: 0.30,       // 30% frutta o carboidrati semplici
        NutritionalRole.healthyFats: 0.30,       // 30% grassi sani
      },
      maxItemsPerRole: {
        NutritionalRole.primaryProtein: 1,       // MAX 1 fonte proteica
        NutritionalRole.simpleCarbs: 1,          // MAX 1 frutto o carboidrato
        NutritionalRole.healthyFats: 1,          // MAX 1 fonte di grassi
      },
      requiredRoles: [
        NutritionalRole.primaryProtein,
      ],
      optionalRoles: [
        NutritionalRole.simpleCarbs,
        NutritionalRole.healthyFats,
      ],
    ),
  };

  SpecificDietGeneratorService._();

  static Future<SpecificDietGeneratorService> getInstance() async {
    if (_instance == null) {
      _instance = SpecificDietGeneratorService._();
    }
    return _instance!;
  }

  /// Ottieni tutti gli alimenti disponibili dal database
  Future<List<Food>> getAllAvailableFoods() async {
    final foodDatabase = FoodDatabase();
    return await foodDatabase.getAllFoods();
  }

  /// Genera un piano dietetico giornaliero (metodo pubblico per test)
  Future<DailyDietPlan> generateDailyDietPlan(UserProfile userProfile, DateTime date) async {
    return await _generateDailyDietPlan(userProfile, date);
  }

  /// CLASSIFICAZIONE NUTRIZIONALE DEGLI ALIMENTI
  /// Determina il ruolo nutrizionale di un alimento secondo la logica dietetica
  NutritionalRole _classifyFoodRole(Food food) {
    // PROTEINE PRINCIPALI (priorità assoluta)
    if (food.categories.contains(FoodCategory.protein)) {
      // Carni, pesci, uova, legumi = proteine principali
      if (food.name.toLowerCase().contains('pollo') ||
          food.name.toLowerCase().contains('tacchino') ||
          food.name.toLowerCase().contains('manzo') ||
          food.name.toLowerCase().contains('maiale') ||
          food.name.toLowerCase().contains('merluzzo') ||
          food.name.toLowerCase().contains('salmone') ||
          food.name.toLowerCase().contains('tonno') ||
          food.name.toLowerCase().contains('gambero') ||
          food.name.toLowerCase().contains('uovo') ||
          food.name.toLowerCase().contains('albume') ||
          food.name.toLowerCase().contains('tuorlo') ||
          food.name.toLowerCase().contains('lenticchie') ||
          food.name.toLowerCase().contains('ceci') ||
          food.name.toLowerCase().contains('fagioli') ||
          food.name.toLowerCase().contains('tofu') ||
          food.name.toLowerCase().contains('tempeh') ||
          food.name.toLowerCase().contains('seitan')) {
        return NutritionalRole.primaryProtein;
      }

      // Latticini = proteine secondarie
      if (food.categories.contains(FoodCategory.dairy)) {
        return NutritionalRole.secondaryProtein;
      }
    }

    // LATTICINI (proteine secondarie)
    if (food.categories.contains(FoodCategory.dairy)) {
      return NutritionalRole.secondaryProtein;
    }

    // CARBOIDRATI COMPLESSI
    if (food.categories.contains(FoodCategory.grain)) {
      // Cereali integrali, pasta, riso, patate
      if (food.name.toLowerCase().contains('integrale') ||
          food.name.toLowerCase().contains('avena') ||
          food.name.toLowerCase().contains('farro') ||
          food.name.toLowerCase().contains('orzo') ||
          food.name.toLowerCase().contains('quinoa') ||
          food.name.toLowerCase().contains('riso') ||
          food.name.toLowerCase().contains('pasta') ||
          food.name.toLowerCase().contains('pane') ||
          food.name.toLowerCase().contains('patata')) {
        return NutritionalRole.complexCarbs;
      }
    }

    // CARBOIDRATI SEMPLICI (frutta)
    if (food.categories.contains(FoodCategory.fruit)) {
      return NutritionalRole.simpleCarbs;
    }

    // GRASSI SANI
    if (food.categories.contains(FoodCategory.fat)) {
      // Olio EVO, avocado, frutta secca, semi
      if (food.name.toLowerCase().contains('olio') ||
          food.name.toLowerCase().contains('oliva') ||
          food.name.toLowerCase().contains('avocado') ||
          food.name.toLowerCase().contains('noci') ||
          food.name.toLowerCase().contains('mandorle') ||
          food.name.toLowerCase().contains('nocciole') ||
          food.name.toLowerCase().contains('semi') ||
          food.name.toLowerCase().contains('chia') ||
          food.name.toLowerCase().contains('lino')) {
        return NutritionalRole.healthyFats;
      }
    }

    // VERDURE
    if (food.categories.contains(FoodCategory.vegetable)) {
      return NutritionalRole.vegetables;
    }

    // CONDIMENTI
    if (food.categories.contains(FoodCategory.condiment) ||
        food.name.toLowerCase().contains('sale') ||
        food.name.toLowerCase().contains('pepe') ||
        food.name.toLowerCase().contains('spezie') ||
        food.name.toLowerCase().contains('erbe')) {
      return NutritionalRole.condiments;
    }

    // DEFAULT: se non classificabile, considera come carboidrato semplice
    return NutritionalRole.simpleCarbs;
  }

  /// Genera un piano dietetico settimanale
  Future<WeeklyDietPlan> generateWeeklyDietPlan(
    UserProfile userProfile, {
    int weeks = 1,
  }) async {
    print('Generazione piano dietetico settimanale personalizzato');
    print('Durata: $weeks settimane');

    // Calcola l'obiettivo calorico giornaliero
    final calorieTarget = userProfile.calculateCalorieTarget();
    print('Obiettivo calorico: $calorieTarget kcal');

    // Calcola gli obiettivi di macronutrienti
    final macroTargets = userProfile.calculateMacroGrams();
    print('Obiettivi macronutrienti: $macroTargets');

    // Genera i piani giornalieri
    final dailyPlans = <DailyDietPlan>[];

    // Data di inizio (oggi)
    final startDate = DateTime.now();

    // Genera un piano per ogni giorno
    for (int i = 0; i < weeks * 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      print('Generazione piano per il giorno $dateString');

      final dailyPlan = await _generateDailyPlan(
        userProfile,
        dateString,
        calorieTarget,
        macroTargets,
      );

      dailyPlans.add(dailyPlan);
    }

    // Crea il piano settimanale
    return WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano dietetico personalizzato',
      startDate: DateTime.now().toString().split(' ')[0],
      dailyPlans: dailyPlans,
      userProfile: userProfile,
    );
  }

  /// Genera un piano dietetico giornaliero
  Future<DailyDietPlan> _generateDailyPlan(
    UserProfile userProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
  ) async {
    print('Generazione piano giornaliero per $date');

    // Determina il numero di pasti in base alle preferenze dell'utente
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Distribuisci le calorie tra i pasti
    final mealCalorieDistribution = _distributeMealCalories(calorieTarget, mealsPerDay);
    print('Distribuzione calorie per pasto: $mealCalorieDistribution');

    // Distribuisci i macronutrienti tra i pasti
    final mealMacroDistribution = _distributeMealMacros(macroTargets, mealsPerDay);
    print('Distribuzione macronutrienti per pasto: $mealMacroDistribution');

    // Orari predefiniti per i pasti
    final mealTimes = _getMealTimes(mealsPerDay);

    // Ottieni tutti gli alimenti dal database completo (include comprehensive foods)
    final foodDatabase = FoodDatabase();
    final allFoods = await foodDatabase.getAllFoods();
    print('Numero di alimenti disponibili dal database completo: ${allFoods.length}');

    // FILTRO DI SICUREZZA ALIMENTARE - CRITICO!
    // Rimuovi tutti gli alimenti non sicuri (es. pollo crudo, spinaci crudi, ecc.)
    final safeFoods = FoodSafetyService.filterSafeFoods(allFoods);
    print('Alimenti sicuri dopo filtro di sicurezza: ${safeFoods.length}');

    // Se non ci sono alimenti sicuri, prova a convertire quelli non sicuri in versioni cotte
    if (safeFoods.isEmpty && allFoods.isNotEmpty) {
      print('ATTENZIONE: Nessun alimento sicuro trovato, tentativo di conversione in versioni cotte...');
      final convertedFoods = <Food>[];
      for (var food in allFoods) {
        if (FoodSafetyService.requiresCooking(food)) {
          final cookedVersion = FoodSafetyService.getCookedVersion(food);
          if (cookedVersion != null) {
            convertedFoods.add(cookedVersion);
          }
        } else {
          convertedFoods.add(food);
        }
      }

      print('Alimenti convertiti in versioni sicure: ${convertedFoods.length}');
      final finalFoods = convertedFoods.isNotEmpty ? convertedFoods : allFoods;
      print('Numero finale di alimenti disponibili: ${finalFoods.length}');
    } else {
      final finalFoods = safeFoods.isNotEmpty ? safeFoods : allFoods;
      print('Numero finale di alimenti disponibili: ${finalFoods.length}');
    }

    // Usa gli alimenti sicuri per la generazione
    final finalFoodList = safeFoods.isNotEmpty ? safeFoods : allFoods;

    // Genera i pasti
    final meals = <PlannedMeal>[];

    // Genera ogni pasto
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final mealCalories = mealCalorieDistribution[i];
      final mealMacros = mealMacroDistribution[i];

      print('Generazione pasto $i: tipo=$mealType, calorie=$mealCalories, macros=$mealMacros');

      final meal = _generateMeal(
        mealType,
        mealCalories,
        mealMacros,
        mealTimes[i],
        finalFoodList,
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // STEP 3: VALIDAZIONE E CORREZIONE CALORIE (CRITICO!)
    return _validateAndCorrectCalories(dailyPlan, userProfile);
  }

  /// Distribuisci le calorie tra i pasti
  List<int> _distributeMealCalories(int totalCalories, int mealsPerDay) {
    // Percentuali di distribuzione delle calorie per pasto
    final List<double> mealPercentages = [];

    switch (mealsPerDay) {
      case 3:
        // Colazione 25%, Pranzo 40%, Cena 35%
        mealPercentages.addAll([0.25, 0.40, 0.35]);
        break;
      case 4:
        // Colazione 20%, Pranzo 35%, Spuntino 10%, Cena 35%
        mealPercentages.addAll([0.20, 0.35, 0.10, 0.35]);
        break;
      case 5:
        // Colazione 20%, Spuntino 10%, Pranzo 30%, Spuntino 10%, Cena 30%
        mealPercentages.addAll([0.20, 0.10, 0.30, 0.10, 0.30]);
        break;
      case 6:
        // Colazione 15%, Spuntino 10%, Pranzo 25%, Spuntino 10%, Cena 25%, Spuntino 15%
        mealPercentages.addAll([0.15, 0.10, 0.25, 0.10, 0.25, 0.15]);
        break;
      default:
        // Distribuzione uniforme
        final percentage = 1.0 / mealsPerDay;
        for (int i = 0; i < mealsPerDay; i++) {
          mealPercentages.add(percentage);
        }
    }

    // Calcola le calorie per ogni pasto
    final List<int> mealCalories = [];
    int remainingCalories = totalCalories;

    for (int i = 0; i < mealsPerDay - 1; i++) {
      final calories = (totalCalories * mealPercentages[i]).round();
      mealCalories.add(calories);
      remainingCalories -= calories;
    }

    // Assegna le calorie rimanenti all'ultimo pasto
    mealCalories.add(remainingCalories);

    return mealCalories;
  }

  /// Distribuisci i macronutrienti tra i pasti
  List<Map<String, int>> _distributeMealMacros(Map<String, int> totalMacros, int mealsPerDay) {
    // Usa le stesse percentuali della distribuzione delle calorie
    final mealPercentages = [];

    switch (mealsPerDay) {
      case 3:
        mealPercentages.addAll([0.25, 0.40, 0.35]);
        break;
      case 4:
        mealPercentages.addAll([0.20, 0.35, 0.10, 0.35]);
        break;
      case 5:
        mealPercentages.addAll([0.20, 0.10, 0.30, 0.10, 0.30]);
        break;
      case 6:
        mealPercentages.addAll([0.15, 0.10, 0.25, 0.10, 0.25, 0.15]);
        break;
      default:
        final percentage = 1.0 / mealsPerDay;
        for (int i = 0; i < mealsPerDay; i++) {
          mealPercentages.add(percentage);
        }
    }

    // Calcola i macronutrienti per ogni pasto
    final List<Map<String, int>> mealMacros = [];
    final Map<String, int> remainingMacros = Map.from(totalMacros);

    for (int i = 0; i < mealsPerDay - 1; i++) {
      final Map<String, int> macros = {};

      totalMacros.forEach((key, value) {
        final amount = (value * mealPercentages[i]).round();
        macros[key] = amount;
        remainingMacros[key] = remainingMacros[key]! - amount;
      });

      mealMacros.add(macros);
    }

    // Assegna i macronutrienti rimanenti all'ultimo pasto
    mealMacros.add(remainingMacros);

    return mealMacros;
  }

  /// Ottieni gli orari predefiniti per i pasti
  List<String> _getMealTimes(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return ['08:00', '13:00', '20:00'];
      case 4:
        return ['08:00', '13:00', '16:30', '20:00'];
      case 5:
        return ['08:00', '10:30', '13:00', '16:30', '20:00'];
      case 6:
        return ['08:00', '10:30', '13:00', '16:30', '20:00', '22:00'];
      default:
        // Genera orari equidistanti dalle 8:00 alle 22:00
        final List<String> times = [];
        final int intervalHours = 14 ~/ mealsPerDay;

        for (int i = 0; i < mealsPerDay; i++) {
          final hour = 8 + (i * intervalHours);
          times.add('${hour.toString().padLeft(2, '0')}:00');
        }

        return times;
    }
  }

  /// Ottieni il tipo di pasto in base all'indice
  MealType _getMealType(int index, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        // Colazione, Pranzo, Cena
        return [MealType.breakfast, MealType.lunch, MealType.dinner][index];
      case 4:
        // Colazione, Pranzo, Spuntino, Cena
        return [MealType.breakfast, MealType.lunch, MealType.snack, MealType.dinner][index];
      case 5:
        // Colazione, Spuntino, Pranzo, Spuntino, Cena
        return [MealType.breakfast, MealType.snack, MealType.lunch, MealType.snack, MealType.dinner][index];
      case 6:
        // Colazione, Spuntino, Pranzo, Spuntino, Cena, Spuntino
        return [MealType.breakfast, MealType.snack, MealType.lunch, MealType.snack, MealType.dinner, MealType.snack][index];
      default:
        // Per default, alterna tra i tipi di pasto
        if (index == 0) return MealType.breakfast;
        if (index == mealsPerDay - 1) return MealType.dinner;
        if (index == mealsPerDay ~/ 2) return MealType.lunch;
        return MealType.snack;
    }
  }







  /// Genera un pasto con alimenti specifici
  PlannedMeal _generateMeal(
    MealType mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    String time,
    List<Food> allFoods,
  ) {
    // STEP 1: Filtra per tipo di pasto (basic filtering)
    final basicSuitableFoods = allFoods.where((food) =>
      food.suitableForMeals.contains(mealType)
    ).toList();

    // STEP 2: APPLICA VALIDAZIONE APPROPRIATEZZA CULTURALE (CRITICO!)
    final suitableFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      basicSuitableFoods,
      mealType,
    );

    final mealTypeString = mealType.toString().split('.').last;
    print('Alimenti base per $mealTypeString: ${basicSuitableFoods.length}');
    print('Alimenti appropriati dopo validazione culturale: ${suitableFoods.length}');

    // Se non ci sono alimenti adatti, usa tutti gli alimenti
    if (suitableFoods.isEmpty) {
      print('Attenzione: Nessun alimento adatto trovato per il pasto di tipo $mealTypeString');
      return PlannedMeal(
        id: _uuid.v4(),
        name: _getMealName(mealTypeString),
        type: mealType,
        foods: [],
        time: time,
      );
    }

    // Seleziona gli alimenti per il pasto
    final selectedFoods = _selectFoodsForMeal(
      suitableFoods,
      targetCalories,
      targetMacros,
      mealTypeString,
    );

    // Crea il pasto
    return PlannedMeal(
      id: _uuid.v4(),
      name: _getMealName(mealTypeString),
      type: mealType,
      foods: selectedFoods,
      time: time,
    );
  }

  /// NUOVA LOGICA: Seleziona gli alimenti per un pasto secondo composizione nutrizionale
  List<FoodPortion> _selectFoodsForMeal(
    List<Food> suitableFoods,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
  ) {
    print('🍽️ NUOVA LOGICA NUTRIZIONALE per $mealType: ${suitableFoods.length} alimenti, $targetCalories kcal');

    // STEP 1: Ottieni la composizione ideale per questo tipo di pasto
    final composition = _mealCompositions[mealType];
    if (composition == null) {
      print('❌ Composizione non trovata per $mealType, uso logica fallback');
      return _selectFoodsForMealFallback(suitableFoods, targetCalories, targetMacros, mealType);
    }

    print('📋 Composizione $mealType: ${composition.rolePercentages}');
    print('🔢 Ruoli richiesti: ${composition.requiredRoles}');
    print('🔢 Ruoli opzionali: ${composition.optionalRoles}');

    // STEP 2: Classifica gli alimenti per ruolo nutrizionale
    final Map<NutritionalRole, List<Food>> foodsByRole = {};
    for (final role in NutritionalRole.values) {
      foodsByRole[role] = [];
    }

    for (final food in suitableFoods) {
      final role = _classifyFoodRole(food);
      foodsByRole[role]!.add(food);
    }

    // Log della classificazione
    for (final role in NutritionalRole.values) {
      if (foodsByRole[role]!.isNotEmpty) {
        print('🏷️ $role: ${foodsByRole[role]!.length} alimenti');
      }
    }

    // STEP 3: Seleziona gli alimenti secondo la composizione ideale
    final List<FoodPortion> selectedFoods = [];
    final Set<String> selectedFoodIds = {}; // Evita duplicati
    int currentCalories = 0;

    // STEP 3A: Seleziona alimenti per ruoli OBBLIGATORI
    for (final role in composition.requiredRoles) {
      final availableFoods = foodsByRole[role]!.where((food) => !selectedFoodIds.contains(food.id)).toList();

      if (availableFoods.isEmpty) {
        print('⚠️ Nessun alimento disponibile per ruolo obbligatorio $role');
        continue;
      }

      // Calcola le calorie target per questo ruolo
      final rolePercentage = composition.rolePercentages[role] ?? 0.0;
      final roleTargetCalories = (targetCalories * rolePercentage).round();

      print('🎯 Ruolo $role: ${roleTargetCalories}kcal (${(rolePercentage * 100).round()}%)');

      // Seleziona UN alimento per questo ruolo (evita conflitti come uovo+albume)
      final selectedFood = _selectFoodWithVarietyForRole(availableFoods, role);
      selectedFoodIds.add(selectedFood.id);

      // Calcola la porzione per raggiungere le calorie target del ruolo
      int grams = selectedFood.calories > 0
          ? (roleTargetCalories / selectedFood.calories * 100).round()
          : 100;

      // Applica limiti realistici per il ruolo
      grams = _limitToRealisticPortionForRole(selectedFood, grams, role);

      final portion = FoodPortion(food: selectedFood, grams: grams);
      selectedFoods.add(portion);
      currentCalories += portion.calories;

      print('✅ Selezionato per $role: ${selectedFood.name} (${grams}g, ${portion.calories}kcal)');
    }

    // STEP 3B: Seleziona alimenti per ruoli OPZIONALI (se c'è spazio calorico)
    for (final role in composition.optionalRoles) {
      if (currentCalories >= targetCalories * 0.9) break; // Ferma se vicino al target

      final availableFoods = foodsByRole[role]!.where((food) => !selectedFoodIds.contains(food.id)).toList();
      if (availableFoods.isEmpty) continue;

      final rolePercentage = composition.rolePercentages[role] ?? 0.0;
      final roleTargetCalories = (targetCalories * rolePercentage).round();

      if (roleTargetCalories < 50) continue; // Salta ruoli con calorie troppo basse

      print('🎯 Ruolo opzionale $role: ${roleTargetCalories}kcal');

      final selectedFood = _selectFoodWithVarietyForRole(availableFoods, role);
      selectedFoodIds.add(selectedFood.id);

      int grams = selectedFood.calories > 0
          ? (roleTargetCalories / selectedFood.calories * 100).round()
          : 50; // Porzioni più piccole per opzionali

      grams = _limitToRealisticPortionForRole(selectedFood, grams, role);

      final portion = FoodPortion(food: selectedFood, grams: grams);
      selectedFoods.add(portion);
      currentCalories += portion.calories;

      print('✅ Selezionato opzionale per $role: ${selectedFood.name} (${grams}g, ${portion.calories}kcal)');
    }

    // STEP 4: Verifica risultato
    print('📊 Pasto $mealType completato: ${selectedFoods.length} alimenti, ${currentCalories}kcal/${targetCalories}kcal');

    // Se non abbiamo alimenti, usa fallback
    if (selectedFoods.isEmpty) {
      print('⚠️ Nessun alimento selezionato, uso fallback');
      return _selectFoodsForMealFallback(suitableFoods, targetCalories, targetMacros, mealType);
    }

    return selectedFoods;
  }

  /// Seleziona un alimento per un ruolo specifico con varietà
  Food _selectFoodWithVarietyForRole(List<Food> availableFoods, NutritionalRole role) {
    if (availableFoods.isEmpty) {
      throw Exception('Nessun alimento disponibile per ruolo $role');
    }

    // Evita alimenti usati di recente
    final recentlyUsedFoods = _getRecentlyUsedFoods();
    final freshFoods = availableFoods.where((food) => !recentlyUsedFoods.contains(food.id)).toList();
    final foodsToConsider = freshFoods.isNotEmpty ? freshFoods : availableFoods;

    // Seleziona il migliore per il ruolo
    final selectedFood = foodsToConsider.first; // Per ora semplice, poi miglioreremo
    _addToRecentlyUsed(selectedFood.id);

    return selectedFood;
  }

  /// Limita le porzioni a quantità realistiche per ruolo
  int _limitToRealisticPortionForRole(Food food, int grams, NutritionalRole role) {
    switch (role) {
      case NutritionalRole.primaryProtein:
        // Proteine: 80-200g (carne/pesce), 1-2 uova, 150-300g legumi
        if (food.name.toLowerCase().contains('uovo')) {
          return grams.clamp(50, 120); // 1-2 uova
        } else if (food.name.toLowerCase().contains('lenticchie') ||
                   food.name.toLowerCase().contains('ceci') ||
                   food.name.toLowerCase().contains('fagioli')) {
          return grams.clamp(80, 150); // Legumi secchi
        } else {
          return grams.clamp(80, 200); // Carne/pesce
        }

      case NutritionalRole.secondaryProtein:
        return grams.clamp(100, 250); // Latticini

      case NutritionalRole.complexCarbs:
        // Cereali: 60-120g secchi, 150-300g cotti
        if (food.name.toLowerCase().contains('pasta') ||
            food.name.toLowerCase().contains('riso')) {
          return grams.clamp(60, 120); // Peso secco
        } else {
          return grams.clamp(50, 150); // Pane, altri cereali
        }

      case NutritionalRole.simpleCarbs:
        return grams.clamp(100, 300); // Frutta

      case NutritionalRole.healthyFats:
        // Grassi: porzioni piccole ma concentrate
        if (food.name.toLowerCase().contains('olio')) {
          return grams.clamp(5, 20); // Olio
        } else {
          return grams.clamp(15, 40); // Frutta secca, semi
        }

      case NutritionalRole.vegetables:
        return grams.clamp(100, 400); // Verdure abbondanti

      case NutritionalRole.condiments:
        return grams.clamp(1, 10); // Condimenti minimi

      default:
        return grams.clamp(50, 200);
    }
  }

  /// Metodo fallback per la selezione degli alimenti (logica precedente semplificata)
  List<FoodPortion> _selectFoodsForMealFallback(
    List<Food> suitableFoods,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
  ) {
    print('🔄 Usando logica fallback per $mealType');

    if (suitableFoods.isEmpty) return [];

    // Seleziona 1-2 alimenti casuali
    final selectedFoods = <FoodPortion>[];
    final numFoods = min(2, suitableFoods.length);

    for (int i = 0; i < numFoods; i++) {
      final food = suitableFoods[_random.nextInt(suitableFoods.length)];
      final grams = (targetCalories / numFoods / food.calories * 100).round().clamp(50, 200);

      selectedFoods.add(FoodPortion(food: food, grams: grams));
    }

    return selectedFoods;
  }

  /// METODO LEGACY - mantenuto per compatibilità
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    // Questo metodo è ora sostituito dalla logica dei ruoli nutrizionali
    switch (mealType) {
      case 'breakfast':
        return [FoodCategory.protein, FoodCategory.grain, FoodCategory.fruit];
      case 'lunch':
      case 'dinner':
        return [FoodCategory.protein, FoodCategory.grain, FoodCategory.vegetable];
      case 'snack':
        return [FoodCategory.fruit, FoodCategory.protein];
      default:
        return [FoodCategory.protein, FoodCategory.grain];
    }
  }

  /// METODO LEGACY - mantenuto per compatibilità
  bool _isFoodSuitableForMealType(Food food, String mealType) {
    // Ora usiamo MealAppropriatenessValidator, questo è solo fallback
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );
    return food.suitableForMeals.contains(mealTypeEnum);
  }

  /// METODO LEGACY - mantenuto per compatibilità
  Food? _selectFoodWithVarietyForMacro(List<Food> availableFoods, String macroType) {
    if (availableFoods.isEmpty) return null;

    // Selezione semplice basata sul macro richiesto
    availableFoods.sort((a, b) {
      switch (macroType) {
        case 'proteins':
          return b.proteins.compareTo(a.proteins);
        case 'carbs':
          return b.carbs.compareTo(a.carbs);
        case 'fats':
          return b.fats.compareTo(a.fats);
        default:
          return 0;
      }
    });

    return availableFoods.first;
  }

  /// METODO LEGACY - mantenuto per compatibilità
  int _limitToRealisticPortion(Food food, int grams) {
    // Usa la nuova logica per ruolo, con fallback generico
    final role = _classifyFoodRole(food);
    return _limitToRealisticPortionForRole(food, grams, role);
  }

  /// METODO LEGACY - mantenuto per compatibilità
  Food _selectFoodWithVariety(List<Food> availableFoods, FoodCategory category) {
    // Converte categoria in ruolo e usa la nuova logica
    NutritionalRole role;
    switch (category) {
      case FoodCategory.protein:
        role = NutritionalRole.primaryProtein;
        break;
      case FoodCategory.grain:
        role = NutritionalRole.complexCarbs;
        break;
      case FoodCategory.fruit:
        role = NutritionalRole.simpleCarbs;
        break;
      case FoodCategory.vegetable:
        role = NutritionalRole.vegetables;
        break;
      case FoodCategory.fat:
        role = NutritionalRole.healthyFats;
        break;
      case FoodCategory.dairy:
        role = NutritionalRole.secondaryProtein;
        break;
      default:
        role = NutritionalRole.primaryProtein;
    }

    return _selectFoodWithVarietyForRole(availableFoods, role);
  }

  /// Ottieni il nome del pasto in italiano
  String _getMealName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return 'Pasto';
    }
  }





  /// VALIDAZIONE E CORREZIONE CALORIE - CRITICO PER PRECISIONE
  DailyDietPlan _validateAndCorrectCalories(DailyDietPlan dailyPlan, UserProfile userProfile) {
    print('🔍 VALIDAZIONE CALORIE: Inizio controllo precisione...');

    // Calcola le calorie totali attuali
    int totalActualCalories = 0;
    for (final meal in dailyPlan.meals) {
      for (final portion in meal.foods) {
        totalActualCalories += portion.calories;
      }
    }

    final calorieTarget = dailyPlan.calorieTarget;
    final calorieDifference = calorieTarget - totalActualCalories;
    final toleranceCalories = 50; // ±50 calorie di tolleranza

    print('🎯 Target calorie: $calorieTarget kcal');
    print('📊 Calorie attuali: $totalActualCalories kcal');
    print('📈 Differenza: $calorieDifference kcal');

    // Se la differenza è entro la tolleranza, il piano è già accurato
    if (calorieDifference.abs() <= toleranceCalories) {
      print('✅ Piano già accurato (differenza: ${calorieDifference}kcal ≤ ${toleranceCalories}kcal)');
      return dailyPlan;
    }

    print('⚠️ Piano necessita correzione (differenza: ${calorieDifference}kcal > ${toleranceCalories}kcal)');

    // Crea una copia modificabile dei pasti
    final correctedMeals = <PlannedMeal>[];

    for (final meal in dailyPlan.meals) {
      // Calcola la correzione proporzionale per questo pasto
      final mealCurrentCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);
      final mealProportion = mealCurrentCalories / totalActualCalories;
      final mealCalorieAdjustment = (calorieDifference * mealProportion).round();

      print('🍽️ Pasto ${meal.name}: ${mealCurrentCalories}kcal, aggiustamento: ${mealCalorieAdjustment >= 0 ? '+' : ''}${mealCalorieAdjustment}kcal');

      // Correggi le porzioni del pasto
      final correctedFoods = _adjustMealPortions(meal.foods, mealCalorieAdjustment);

      // Crea il pasto corretto
      final correctedMeal = PlannedMeal(
        id: meal.id,
        name: meal.name,
        type: meal.type,
        foods: correctedFoods,
        time: meal.time,
        isCompleted: meal.isCompleted,
      );

      correctedMeals.add(correctedMeal);
    }

    // Verifica finale
    final finalCalories = correctedMeals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    print('🎯 Calorie finali dopo correzione: ${finalCalories}kcal (target: ${calorieTarget}kcal)');
    print('✅ Differenza finale: ${(calorieTarget - finalCalories).abs()}kcal');

    // Crea il piano corretto
    return DailyDietPlan(
      date: dailyPlan.date,
      meals: correctedMeals,
      calorieTarget: dailyPlan.calorieTarget,
      macroTargets: dailyPlan.macroTargets,
    );
  }

  /// Aggiusta le porzioni di un pasto per raggiungere le calorie target
  List<FoodPortion> _adjustMealPortions(List<FoodPortion> originalFoods, int calorieDifference) {
    if (originalFoods.isEmpty || calorieDifference == 0) {
      return originalFoods;
    }

    final adjustedFoods = <FoodPortion>[];

    for (final portion in originalFoods) {
      // Calcola il fattore di aggiustamento proporzionale
      final currentCalories = portion.calories.toDouble();
      if (currentCalories <= 0) {
        adjustedFoods.add(portion);
        continue;
      }

      // Calcola l'aggiustamento per questa porzione
      final portionAdjustment = (calorieDifference * (currentCalories / originalFoods.fold(0.0, (sum, p) => sum + p.calories.toDouble()))).round();
      final targetCalories = currentCalories + portionAdjustment;

      // Calcola i nuovi grammi basati sulle calorie target
      final newGrams = (targetCalories * portion.grams / currentCalories).round();

      // Limita a porzioni realistiche (min 10g, max 500g)
      final limitedGrams = newGrams.clamp(10, 500);

      // Crea la porzione aggiustata
      final adjustedPortion = FoodPortion(
        food: portion.food,
        grams: limitedGrams,
      );

      adjustedFoods.add(adjustedPortion);
    }

    return adjustedFoods;
  }

  // SISTEMA DI VARIETÀ E ALTERNATIVE
  static final Set<String> _recentlyUsedFoods = <String>{};
  static final Map<String, List<String>> _foodAlternatives = <String, List<String>>{};

  /// Ottieni gli alimenti usati di recente per evitare ripetizioni
  Set<String> _getRecentlyUsedFoods() {
    return Set.from(_recentlyUsedFoods);
  }

  /// Aggiungi un alimento alla lista degli usati di recente
  void _addToRecentlyUsed(String foodId) {
    _recentlyUsedFoods.add(foodId);

    // Mantieni solo gli ultimi 20 alimenti per evitare memoria eccessiva
    if (_recentlyUsedFoods.length > 20) {
      final oldestFood = _recentlyUsedFoods.first;
      _recentlyUsedFoods.remove(oldestFood);
    }
  }

  /// Pulisci la lista degli alimenti usati di recente (per nuovi piani settimanali)
  static void clearRecentlyUsed() {
    _recentlyUsedFoods.clear();
  }

  /// Ottieni alternative culturalmente appropriate per un alimento
  List<Food> getFoodAlternatives(Food originalFood, List<Food> availableFoods) {
    print('🔄 Ricerca alternative per: ${originalFood.name}');

    final alternatives = <Food>[];

    // Criteri per alternative appropriate:
    // 1. Stessa categoria principale
    // 2. Calorie simili (±30%)
    // 3. Stesso tipo di pasto appropriato
    // 4. Profilo nutrizionale simile

    final targetCalories = originalFood.calories;
    final calorieRange = (targetCalories * 0.3).round(); // ±30%
    final minCalories = targetCalories - calorieRange;
    final maxCalories = targetCalories + calorieRange;

    for (final food in availableFoods) {
      if (food.id == originalFood.id) continue; // Salta l'alimento originale

      // Verifica categoria principale condivisa
      final sharedCategories = originalFood.categories.where((cat) => food.categories.contains(cat)).toList();
      if (sharedCategories.isEmpty) continue;

      // Verifica calorie simili
      if (food.calories < minCalories || food.calories > maxCalories) continue;

      // Verifica appropriatezza per gli stessi tipi di pasto
      final sharedMealTypes = originalFood.suitableForMeals.where((meal) => food.suitableForMeals.contains(meal)).toList();
      if (sharedMealTypes.isEmpty) continue;

      // Verifica appropriatezza culturale per gli stessi pasti
      bool culturallyAppropriate = true;
      for (final mealType in sharedMealTypes) {
        if (!MealAppropriatenessValidator.isAppropriateForMeal(food, mealType)) {
          culturallyAppropriate = false;
          break;
        }
      }

      if (culturallyAppropriate) {
        alternatives.add(food);
      }
    }

    // Ordina per similarità nutrizionale
    alternatives.sort((a, b) {
      final aDiff = (a.calories - targetCalories).abs() +
                   (a.proteins - originalFood.proteins).abs() +
                   (a.carbs - originalFood.carbs).abs() +
                   (a.fats - originalFood.fats).abs();
      final bDiff = (b.calories - targetCalories).abs() +
                   (b.proteins - originalFood.proteins).abs() +
                   (b.carbs - originalFood.carbs).abs() +
                   (b.fats - originalFood.fats).abs();
      return aDiff.compareTo(bDiff);
    });

    // Restituisci le prime 3 alternative migliori
    final topAlternatives = alternatives.take(3).toList();

    print('   Trovate ${topAlternatives.length} alternative appropriate:');
    for (final alt in topAlternatives) {
      print('   - ${alt.name} (${alt.calories}kcal, categorie: ${alt.categories.map((c) => c.toString().split('.').last).join(', ')})');
    }

    return topAlternatives;
  }

  /// Genera un piano con alternative per ogni alimento selezionato
  Map<String, List<Food>> generateMealPlanWithAlternatives(DailyDietPlan dailyPlan, List<Food> allFoods) {
    print('🔄 Generazione alternative per piano giornaliero...');

    final mealAlternatives = <String, List<Food>>{};

    for (final meal in dailyPlan.meals) {
      for (final portion in meal.foods) {
        final alternatives = getFoodAlternatives(portion.food, allFoods);
        mealAlternatives['${meal.id}_${portion.food.id}'] = alternatives;
      }
    }

    print('✅ Generate alternative per ${mealAlternatives.length} alimenti');
    return mealAlternatives;
  }
}
