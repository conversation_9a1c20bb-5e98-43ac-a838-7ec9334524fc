import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../data/specific_diet_foods.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';
import 'meal_appropriateness_validator.dart';

/// Servizio specializzato per la generazione di diete utilizzando solo gli alimenti specifici
class SpecificDietGeneratorService {
  final _uuid = Uuid();
  final Random _random = Random();

  // Singleton
  static SpecificDietGeneratorService? _instance;

  SpecificDietGeneratorService._();

  static Future<SpecificDietGeneratorService> getInstance() async {
    if (_instance == null) {
      _instance = SpecificDietGeneratorService._();
    }
    return _instance!;
  }

  /// Genera un piano dietetico settimanale
  Future<WeeklyDietPlan> generateWeeklyDietPlan(
    UserProfile userProfile, {
    int weeks = 1,
  }) async {
    print('Generazione piano dietetico settimanale personalizzato');
    print('Durata: $weeks settimane');

    // Calcola l'obiettivo calorico giornaliero
    final calorieTarget = userProfile.calculateCalorieTarget();
    print('Obiettivo calorico: $calorieTarget kcal');

    // Calcola gli obiettivi di macronutrienti
    final macroTargets = userProfile.calculateMacroGrams();
    print('Obiettivi macronutrienti: $macroTargets');

    // Genera i piani giornalieri
    final dailyPlans = <DailyDietPlan>[];

    // Data di inizio (oggi)
    final startDate = DateTime.now();

    // Genera un piano per ogni giorno
    for (int i = 0; i < weeks * 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      print('Generazione piano per il giorno $dateString');

      final dailyPlan = await _generateDailyPlan(
        userProfile,
        dateString,
        calorieTarget,
        macroTargets,
      );

      dailyPlans.add(dailyPlan);
    }

    // Crea il piano settimanale
    return WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano dietetico personalizzato',
      startDate: DateTime.now().toString().split(' ')[0],
      dailyPlans: dailyPlans,
      userProfile: userProfile,
    );
  }

  /// Genera un piano dietetico giornaliero
  Future<DailyDietPlan> _generateDailyPlan(
    UserProfile userProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
  ) async {
    print('Generazione piano giornaliero per $date');

    // Determina il numero di pasti in base alle preferenze dell'utente
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Distribuisci le calorie tra i pasti
    final mealCalorieDistribution = _distributeMealCalories(calorieTarget, mealsPerDay);
    print('Distribuzione calorie per pasto: $mealCalorieDistribution');

    // Distribuisci i macronutrienti tra i pasti
    final mealMacroDistribution = _distributeMealMacros(macroTargets, mealsPerDay);
    print('Distribuzione macronutrienti per pasto: $mealMacroDistribution');

    // Orari predefiniti per i pasti
    final mealTimes = _getMealTimes(mealsPerDay);

    // Ottieni tutti gli alimenti dal database completo (include comprehensive foods)
    final foodDatabase = FoodDatabase();
    final allFoods = await foodDatabase.getAllFoods();
    print('Numero di alimenti disponibili dal database completo: ${allFoods.length}');

    // FILTRO DI SICUREZZA ALIMENTARE - CRITICO!
    // Rimuovi tutti gli alimenti non sicuri (es. pollo crudo, spinaci crudi, ecc.)
    final safeFoods = FoodSafetyService.filterSafeFoods(allFoods);
    print('Alimenti sicuri dopo filtro di sicurezza: ${safeFoods.length}');

    // Se non ci sono alimenti sicuri, prova a convertire quelli non sicuri in versioni cotte
    if (safeFoods.isEmpty && allFoods.isNotEmpty) {
      print('ATTENZIONE: Nessun alimento sicuro trovato, tentativo di conversione in versioni cotte...');
      final convertedFoods = <Food>[];
      for (var food in allFoods) {
        if (FoodSafetyService.requiresCooking(food)) {
          final cookedVersion = FoodSafetyService.getCookedVersion(food);
          if (cookedVersion != null) {
            convertedFoods.add(cookedVersion);
          }
        } else {
          convertedFoods.add(food);
        }
      }

      print('Alimenti convertiti in versioni sicure: ${convertedFoods.length}');
      final finalFoods = convertedFoods.isNotEmpty ? convertedFoods : allFoods;
      print('Numero finale di alimenti disponibili: ${finalFoods.length}');
    } else {
      final finalFoods = safeFoods.isNotEmpty ? safeFoods : allFoods;
      print('Numero finale di alimenti disponibili: ${finalFoods.length}');
    }

    // Usa gli alimenti sicuri per la generazione
    final finalFoodList = safeFoods.isNotEmpty ? safeFoods : allFoods;

    // Genera i pasti
    final meals = <PlannedMeal>[];

    // Genera ogni pasto
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final mealCalories = mealCalorieDistribution[i];
      final mealMacros = mealMacroDistribution[i];

      print('Generazione pasto $i: tipo=$mealType, calorie=$mealCalories, macros=$mealMacros');

      final meal = _generateMeal(
        mealType,
        mealCalories,
        mealMacros,
        mealTimes[i],
        finalFoodList,
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // STEP 3: VALIDAZIONE E CORREZIONE CALORIE (CRITICO!)
    return _validateAndCorrectCalories(dailyPlan, userProfile);
  }

  /// Distribuisci le calorie tra i pasti
  List<int> _distributeMealCalories(int totalCalories, int mealsPerDay) {
    // Percentuali di distribuzione delle calorie per pasto
    final List<double> mealPercentages = [];

    switch (mealsPerDay) {
      case 3:
        // Colazione 25%, Pranzo 40%, Cena 35%
        mealPercentages.addAll([0.25, 0.40, 0.35]);
        break;
      case 4:
        // Colazione 20%, Pranzo 35%, Spuntino 10%, Cena 35%
        mealPercentages.addAll([0.20, 0.35, 0.10, 0.35]);
        break;
      case 5:
        // Colazione 20%, Spuntino 10%, Pranzo 30%, Spuntino 10%, Cena 30%
        mealPercentages.addAll([0.20, 0.10, 0.30, 0.10, 0.30]);
        break;
      case 6:
        // Colazione 15%, Spuntino 10%, Pranzo 25%, Spuntino 10%, Cena 25%, Spuntino 15%
        mealPercentages.addAll([0.15, 0.10, 0.25, 0.10, 0.25, 0.15]);
        break;
      default:
        // Distribuzione uniforme
        final percentage = 1.0 / mealsPerDay;
        for (int i = 0; i < mealsPerDay; i++) {
          mealPercentages.add(percentage);
        }
    }

    // Calcola le calorie per ogni pasto
    final List<int> mealCalories = [];
    int remainingCalories = totalCalories;

    for (int i = 0; i < mealsPerDay - 1; i++) {
      final calories = (totalCalories * mealPercentages[i]).round();
      mealCalories.add(calories);
      remainingCalories -= calories;
    }

    // Assegna le calorie rimanenti all'ultimo pasto
    mealCalories.add(remainingCalories);

    return mealCalories;
  }

  /// Distribuisci i macronutrienti tra i pasti
  List<Map<String, int>> _distributeMealMacros(Map<String, int> totalMacros, int mealsPerDay) {
    // Usa le stesse percentuali della distribuzione delle calorie
    final mealPercentages = [];

    switch (mealsPerDay) {
      case 3:
        mealPercentages.addAll([0.25, 0.40, 0.35]);
        break;
      case 4:
        mealPercentages.addAll([0.20, 0.35, 0.10, 0.35]);
        break;
      case 5:
        mealPercentages.addAll([0.20, 0.10, 0.30, 0.10, 0.30]);
        break;
      case 6:
        mealPercentages.addAll([0.15, 0.10, 0.25, 0.10, 0.25, 0.15]);
        break;
      default:
        final percentage = 1.0 / mealsPerDay;
        for (int i = 0; i < mealsPerDay; i++) {
          mealPercentages.add(percentage);
        }
    }

    // Calcola i macronutrienti per ogni pasto
    final List<Map<String, int>> mealMacros = [];
    final Map<String, int> remainingMacros = Map.from(totalMacros);

    for (int i = 0; i < mealsPerDay - 1; i++) {
      final Map<String, int> macros = {};

      totalMacros.forEach((key, value) {
        final amount = (value * mealPercentages[i]).round();
        macros[key] = amount;
        remainingMacros[key] = remainingMacros[key]! - amount;
      });

      mealMacros.add(macros);
    }

    // Assegna i macronutrienti rimanenti all'ultimo pasto
    mealMacros.add(remainingMacros);

    return mealMacros;
  }

  /// Ottieni gli orari predefiniti per i pasti
  List<String> _getMealTimes(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return ['08:00', '13:00', '20:00'];
      case 4:
        return ['08:00', '13:00', '16:30', '20:00'];
      case 5:
        return ['08:00', '10:30', '13:00', '16:30', '20:00'];
      case 6:
        return ['08:00', '10:30', '13:00', '16:30', '20:00', '22:00'];
      default:
        // Genera orari equidistanti dalle 8:00 alle 22:00
        final List<String> times = [];
        final int intervalHours = 14 ~/ mealsPerDay;

        for (int i = 0; i < mealsPerDay; i++) {
          final hour = 8 + (i * intervalHours);
          times.add('${hour.toString().padLeft(2, '0')}:00');
        }

        return times;
    }
  }

  /// Ottieni il tipo di pasto in base all'indice
  String _getMealType(int index, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        // Colazione, Pranzo, Cena
        return ['breakfast', 'lunch', 'dinner'][index];
      case 4:
        // Colazione, Pranzo, Spuntino, Cena
        return ['breakfast', 'lunch', 'snack', 'dinner'][index];
      case 5:
        // Colazione, Spuntino, Pranzo, Spuntino, Cena
        return ['breakfast', 'snack', 'lunch', 'snack', 'dinner'][index];
      case 6:
        // Colazione, Spuntino, Pranzo, Spuntino, Cena, Spuntino
        return ['breakfast', 'snack', 'lunch', 'snack', 'dinner', 'snack'][index];
      default:
        // Per default, alterna tra i tipi di pasto
        if (index == 0) return 'breakfast';
        if (index == mealsPerDay - 1) return 'dinner';
        if (index == mealsPerDay ~/ 2) return 'lunch';
        return 'snack';
    }
  }







  /// Genera un pasto con alimenti specifici
  PlannedMeal _generateMeal(
    String mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    String time,
    List<Food> allFoods,
  ) {
    // Filtra gli alimenti adatti per questo tipo di pasto
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );

    // STEP 1: Filtra per tipo di pasto (basic filtering)
    final basicSuitableFoods = allFoods.where((food) =>
      food.suitableForMeals.contains(mealTypeEnum)
    ).toList();

    // STEP 2: APPLICA VALIDAZIONE APPROPRIATEZZA CULTURALE (CRITICO!)
    final suitableFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      basicSuitableFoods,
      mealTypeEnum,
    );

    print('Alimenti base per $mealType: ${basicSuitableFoods.length}');
    print('Alimenti appropriati dopo validazione culturale: ${suitableFoods.length}');

    // Se non ci sono alimenti adatti, usa tutti gli alimenti
    if (suitableFoods.isEmpty) {
      print('Attenzione: Nessun alimento adatto trovato per il pasto di tipo $mealType');
      return PlannedMeal(
        id: _uuid.v4(),
        name: _getMealName(mealType),
        type: mealType,
        foods: [],
        time: time,
      );
    }

    // Seleziona gli alimenti per il pasto
    final selectedFoods = _selectFoodsForMeal(
      suitableFoods,
      targetCalories,
      targetMacros,
      mealType,
    );

    // Crea il pasto
    return PlannedMeal(
      id: _uuid.v4(),
      name: _getMealName(mealType),
      type: mealType,
      foods: selectedFoods,
      time: time,
    );
  }

  /// Seleziona gli alimenti per un pasto con varietà migliorata
  List<FoodPortion> _selectFoodsForMeal(
    List<Food> suitableFoods,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
  ) {
    print('🍽️ _selectFoodsForMeal chiamato per $mealType: ${suitableFoods.length} alimenti, $targetCalories kcal');

    // Definisci le categorie di alimenti da includere in base al tipo di pasto
    final categoriesToInclude = _getCategoriesForMealType(mealType);
    print('   Categorie richieste: $categoriesToInclude');

    // Filtra gli alimenti adatti per questo tipo di pasto
    final List<Food> mealTypeSuitableFoods = suitableFoods
        .where((food) => _isFoodSuitableForMealType(food, mealType))
        .toList();

    // Se non ci sono alimenti adatti, usa tutti gli alimenti
    final List<Food> filteredFoods = mealTypeSuitableFoods.isEmpty ? suitableFoods : mealTypeSuitableFoods;

    // Raggruppa gli alimenti per categoria
    final Map<FoodCategory, List<Food>> foodsByCategory = {};
    for (var category in categoriesToInclude) {
      foodsByCategory[category] = filteredFoods
          .where((food) => food.categories.contains(category))
          .toList();
      print('   Categoria $category: ${foodsByCategory[category]!.length} alimenti disponibili');
    }

    // Seleziona gli alimenti e calcola le porzioni
    final List<FoodPortion> selectedFoods = [];
    int currentCalories = 0;
    final Map<String, double> currentMacros = {
      'proteins': 0,
      'carbs': 0,
      'fats': 0,
    };

    // Tieni traccia degli alimenti già selezionati per evitare duplicati
    final Set<String> selectedFoodIds = {};

    // Seleziona almeno un alimento da ciascuna categoria necessaria
    for (var category in categoriesToInclude) {
      if (foodsByCategory.containsKey(category) && foodsByCategory[category]!.isNotEmpty) {
        // Seleziona un alimento dalla categoria usando varietà intelligente
        final foodList = foodsByCategory[category]!;
        if (foodList.isEmpty) continue;

        // Filtra gli alimenti che non sono già stati selezionati
        final availableFoods = foodList.where((food) => !selectedFoodIds.contains(food.id)).toList();
        if (availableFoods.isEmpty) continue; // Salta se tutti gli alimenti sono già stati selezionati

        // Usa selezione intelligente con varietà invece di selezione casuale
        print('🔄 Selezione alimento per categoria $category...');
        final food = _selectFoodWithVariety(availableFoods, category);

        // Aggiungi l'ID dell'alimento alla lista degli alimenti selezionati
        selectedFoodIds.add(food.id);

        // Calcola la porzione in base alle calorie rimanenti
        final caloriesRemaining = targetCalories - currentCalories;
        final portionPercentage = min(0.3, caloriesRemaining / targetCalories);
        final portionCalories = (targetCalories * portionPercentage).round();

        // Calcola i grammi in base alle calorie
        // Assicurati che le calorie non siano zero per evitare divisione per zero
        int grams = food.calories > 0
            ? (portionCalories / food.calories * 100).round()
            : 100; // Default a 100g se le calorie sono zero

        // Limita le porzioni a quantità realistiche in base alla categoria dell'alimento
        grams = _limitToRealisticPortion(food, grams);

        // Crea la porzione
        final portion = FoodPortion(
          food: food,
          grams: grams,
        );

        // Aggiungi la porzione alla lista
        selectedFoods.add(portion);

        // Aggiorna le calorie e i macronutrienti correnti
        currentCalories += portion.calories;
        currentMacros['proteins'] = currentMacros['proteins']! + portion.proteins;
        currentMacros['carbs'] = currentMacros['carbs']! + portion.carbs;
        currentMacros['fats'] = currentMacros['fats']! + portion.fats;
      }
    }

    // Aggiungi alimenti aggiuntivi fino a raggiungere l'obiettivo calorico
    while (currentCalories < targetCalories * 0.9 && selectedFoods.length < 5) {
      // Calcola quale macronutriente è più carente
      final Map<String, double> macroDeficits = {};
      targetMacros.forEach((key, value) {
        if (currentMacros.containsKey(key)) {
          macroDeficits[key] = value - currentMacros[key]!;
        }
      });

      // Trova il macronutriente più carente
      String mostDeficientMacro = 'proteins';
      double maxDeficit = 0;
      macroDeficits.forEach((key, value) {
        if (value > maxDeficit) {
          maxDeficit = value;
          mostDeficientMacro = key;
        }
      });

      // Seleziona un alimento ricco del macronutriente carente
      Food? selectedFood;

      // Filtra gli alimenti adatti per questo tipo di pasto
      final List<Food> mealTypeSuitableFoods = suitableFoods
          .where((food) => _isFoodSuitableForMealType(food, mealType))
          .toList();

      // Se non ci sono alimenti adatti, usa tutti gli alimenti
      final List<Food> filteredFoods = mealTypeSuitableFoods.isEmpty ? suitableFoods : mealTypeSuitableFoods;

      // Filtra gli alimenti che non sono già stati selezionati
      final availableFoods = filteredFoods.where((food) => !selectedFoodIds.contains(food.id)).toList();
      if (availableFoods.isEmpty) break; // Esci se non ci sono più alimenti disponibili

      // Usa selezione intelligente con varietà per alimenti aggiuntivi
      print('🔄 Selezione alimento aggiuntivo per macro $mostDeficientMacro...');
      selectedFood = _selectFoodWithVarietyForMacro(availableFoods, mostDeficientMacro);

      if (selectedFood == null) break;

      // Aggiungi l'ID dell'alimento alla lista degli alimenti selezionati
      selectedFoodIds.add(selectedFood.id);

      // Calcola la porzione in base alle calorie rimanenti
      final caloriesRemaining = targetCalories - currentCalories;
      final portionCalories = min(caloriesRemaining, selectedFood.calories);

      // Calcola i grammi in base alle calorie
      int grams = selectedFood.calories > 0
          ? (portionCalories / selectedFood.calories * 100).round()
          : 100; // Default a 100g se le calorie sono zero

      // Limita le porzioni a quantità realistiche in base alla categoria dell'alimento
      grams = _limitToRealisticPortion(selectedFood, grams);

      // Crea la porzione
      final portion = FoodPortion(
        food: selectedFood,
        grams: grams,
      );

      // Aggiungi la porzione alla lista
      selectedFoods.add(portion);

      // Aggiorna le calorie e i macronutrienti correnti
      currentCalories += portion.calories;
      currentMacros['proteins'] = currentMacros['proteins']! + portion.proteins;
      currentMacros['carbs'] = currentMacros['carbs']! + portion.carbs;
      currentMacros['fats'] = currentMacros['fats']! + portion.fats;
    }

    // Se non è stato selezionato alcun alimento, aggiungi almeno un alimento predefinito
    if (selectedFoods.isEmpty && !suitableFoods.isEmpty) {
      final food = suitableFoods.first;
      final portion = FoodPortion(
        food: food,
        grams: 100, // Porzione standard
      );
      selectedFoods.add(portion);
    }

    return selectedFoods;
  }

  /// Seleziona un alimento con varietà migliorata da una lista
  Food _selectFoodWithVariety(List<Food> availableFoods, FoodCategory category) {
    print('🎯 _selectFoodWithVariety chiamato: ${availableFoods.length} alimenti, categoria: $category');

    if (availableFoods.isEmpty) {
      throw Exception('Nessun alimento disponibile per la selezione');
    }

    // Se c'è solo un alimento, selezionalo
    if (availableFoods.length == 1) {
      print('   Solo 1 alimento disponibile: ${availableFoods.first.name}');
      return availableFoods.first;
    }

    // SISTEMA DI VARIETÀ INTELLIGENTE: Evita ripetizioni recenti
    final recentlyUsedFoods = _getRecentlyUsedFoods();
    final freshFoods = availableFoods.where((food) => !recentlyUsedFoods.contains(food.id)).toList();

    // Se ci sono alimenti "freschi" (non usati di recente), preferiscili
    final foodsToConsider = freshFoods.isNotEmpty ? freshFoods : availableFoods;
    print('   Alimenti da considerare (evitando ripetizioni): ${foodsToConsider.length}');

    try {
      // Usa selezione intelligente basata su punteggi di varietà
      print('   Calcolo punteggi di varietà per ${foodsToConsider.length} alimenti...');

      // Ottieni i punteggi di varietà per gli alimenti disponibili
      final foodScores = <Food, double>{};

      for (final food in foodsToConsider) {
        // Calcola un punteggio di varietà basato su caratteristiche dell'alimento
        double varietyScore = 100.0; // Punteggio base

        // Bonus per alimenti con buoni valori nutrizionali
        if (food.proteins > 10 || food.fiber > 3) {
          varietyScore += 20.0;
        }

        // Bonus per alimenti vegetali (frutta e verdura)
        if (food.categories.contains(FoodCategory.fruit) ||
            food.categories.contains(FoodCategory.vegetable)) {
          varietyScore += 15.0;
        }

        // Bonus per alimenti integrali
        if (food.name.toLowerCase().contains('integrale') ||
            food.name.toLowerCase().contains('intero')) {
          varietyScore += 10.0;
        }

        // Penalità per alimenti con molti grassi saturi
        if ((food.saturatedFats ?? 0) > 5) {
          varietyScore -= 15.0;
        }

        foodScores[food] = varietyScore;
        print('     ${food.name}: ${varietyScore.toStringAsFixed(1)} punti');
      }

      // Ordina gli alimenti per punteggio di varietà (dal più alto al più basso)
      final sortedFoods = foodScores.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Seleziona casualmente tra i primi 3 alimenti con punteggio più alto
      final topFoods = sortedFoods.take(3).map((e) => e.key).toList();
      final selectedFood = topFoods[_random.nextInt(topFoods.length)];

      // Registra l'alimento come usato di recente
      _addToRecentlyUsed(selectedFood.id);

      print('   ✅ Selezionato: ${selectedFood.name} (punteggio: ${foodScores[selectedFood]!.toStringAsFixed(1)})');
      return selectedFood;

    } catch (e) {
      // Fallback alla selezione casuale se il variety manager fallisce
      print('   ⚠️ Fallback alla selezione casuale per categoria $category: $e');
      final selectedFood = availableFoods[_random.nextInt(availableFoods.length)];
      print('   🔄 Selezionato casualmente: ${selectedFood.name}');
      return selectedFood;
    }
  }

  /// Seleziona un alimento con varietà migliorata per un macronutriente specifico
  Food? _selectFoodWithVarietyForMacro(List<Food> availableFoods, String targetMacro) {
    print('🎯 _selectFoodWithVarietyForMacro chiamato: ${availableFoods.length} alimenti, macro: $targetMacro');

    if (availableFoods.isEmpty) {
      print('   ❌ Nessun alimento disponibile');
      return null;
    }

    try {
      // Calcola punteggi combinati: contenuto macronutriente + varietà
      print('   Calcolo punteggi combinati (nutrizione + varietà)...');
      final foodScores = <Food, double>{};

      for (final food in availableFoods) {
        if (food.calories <= 0) continue; // Salta alimenti con calorie zero

        // Calcola contenuto del macronutriente target
        double macroContent = 0;
        switch (targetMacro) {
          case 'proteins':
            macroContent = food.proteins;
            break;
          case 'carbs':
            macroContent = food.carbs;
            break;
          case 'fats':
            macroContent = food.fats;
            break;
          default:
            continue;
        }

        // Punteggio nutrizionale (contenuto macronutriente per caloria)
        double nutritionScore = (macroContent / food.calories) * 100;

        // Punteggio varietà
        double varietyScore = 50.0; // Punteggio base varietà

        // Bonus per alimenti con buoni valori nutrizionali
        if (food.proteins > 8 || food.fiber > 2) {
          varietyScore += 15.0;
        }

        // Bonus per alimenti vegetali
        if (food.categories.contains(FoodCategory.fruit) ||
            food.categories.contains(FoodCategory.vegetable)) {
          varietyScore += 10.0;
        }

        // Bonus per alimenti integrali
        if (food.name.toLowerCase().contains('integrale')) {
          varietyScore += 8.0;
        }

        // Penalità per alimenti con molti grassi saturi
        if ((food.saturatedFats ?? 0) > 3) {
          varietyScore -= 10.0;
        }

        // Punteggio combinato: 70% nutrizione + 30% varietà
        final combinedScore = (nutritionScore * 0.7) + (varietyScore * 0.3);
        foodScores[food] = combinedScore;

        print('     ${food.name}: nutriz=${nutritionScore.toStringAsFixed(1)}, var=${varietyScore.toStringAsFixed(1)}, tot=${combinedScore.toStringAsFixed(1)}');
      }

      if (foodScores.isEmpty) {
        print('   ❌ Nessun alimento con punteggio valido');
        return null;
      }

      // Ordina per punteggio combinato
      final sortedFoods = foodScores.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Seleziona casualmente tra i primi 3 alimenti con punteggio più alto
      final topFoods = sortedFoods.take(3).map((e) => e.key).toList();
      final selectedFood = topFoods[_random.nextInt(topFoods.length)];

      print('   ✅ Selezionato: ${selectedFood.name} (punteggio: ${foodScores[selectedFood]!.toStringAsFixed(1)})');
      return selectedFood;

    } catch (e) {
      // Fallback alla selezione tradizionale
      print('   ⚠️ Fallback alla selezione tradizionale per macro $targetMacro: $e');

      Food? bestFood;
      double bestScore = 0;

      for (var food in availableFoods) {
        if (food.calories <= 0) continue;

        double macroContent = 0;
        switch (targetMacro) {
          case 'proteins':
            macroContent = food.proteins;
            break;
          case 'carbs':
            macroContent = food.carbs;
            break;
          case 'fats':
            macroContent = food.fats;
            break;
          default:
            continue;
        }

        final score = macroContent / food.calories * 100;
        if (score > bestScore) {
          bestScore = score;
          bestFood = food;
        }
      }

      return bestFood;
    }
  }

  /// Ottieni le categorie di alimenti da includere in base al tipo di pasto
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    if (mealType == 'breakfast') {
      return [FoodCategory.grain, FoodCategory.dairy, FoodCategory.fruit];
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      return [FoodCategory.protein, FoodCategory.grain, FoodCategory.vegetable, FoodCategory.fat];
    } else if (mealType == 'snack') {
      return [FoodCategory.fruit, FoodCategory.dairy, FoodCategory.sweet];
    } else {
      return [FoodCategory.protein, FoodCategory.grain, FoodCategory.vegetable];
    }
  }

  /// Verifica se un alimento è adatto per un tipo di pasto specifico
  bool _isFoodSuitableForMealType(Food food, String mealType) {
    // Lista di alimenti non adatti per la colazione
    final List<String> notForBreakfast = [
      'Farina integrale',
      'Farina',
      'Spinaci crudi',
      'Spinaci',
      'Broccoli',
      'Cavolfiore',
      'Melanzane',
      'Zucchine',
      'Peperoni',
      'Pomodori',
      'Carote',
      'Patate',
      'Pasta',
      'Riso',
      'Quinoa',
      'Lenticchie',
      'Ceci',
      'Fagioli',
      'Pollo',
      'Tacchino',
      'Manzo',
      'Maiale',
      'Agnello',
      'Pesce',
      'Salmone',
      'Tonno',
    ];

    // Lista di alimenti non adatti per gli spuntini
    final List<String> notForSnacks = [
      'Pasta',
      'Riso',
      'Pollo',
      'Tacchino',
      'Manzo',
      'Maiale',
      'Agnello',
      'Pesce',
      'Salmone',
      'Tonno',
      'Spinaci',
      'Broccoli',
      'Cavolfiore',
      'Melanzane',
      'Zucchine',
    ];

    // Controlla se l'alimento è nella lista degli alimenti non adatti
    if (mealType == 'breakfast' && notForBreakfast.contains(food.name)) {
      return false;
    }

    if (mealType == 'snack' && notForSnacks.contains(food.name)) {
      return false;
    }

    // Verifica se l'alimento è adatto per il tipo di pasto in base alle categorie
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.values.first,
    );
    if (!food.suitableForMeals.contains(mealTypeEnum)) {
      return false;
    }

    return true;
  }

  /// Ottieni il nome del pasto in base al tipo
  String _getMealName(String mealType) {
    if (mealType == 'breakfast') {
      return 'Colazione';
    } else if (mealType == 'lunch') {
      return 'Pranzo';
    } else if (mealType == 'dinner') {
      return 'Cena';
    } else if (mealType == 'snack') {
      return 'Spuntino';
    } else {
      return 'Pasto';
    }
  }

  /// Limita le porzioni a quantità realistiche in base alla categoria dell'alimento
  int _limitToRealisticPortion(Food food, int calculatedGrams) {
    // Definisci limiti massimi realistici per categoria (in grammi)
    final Map<FoodCategory, int> maxPortionSizes = {
      FoodCategory.vegetable: 300, // Max 300g di verdure per porzione
      FoodCategory.fruit: 250,     // Max 250g di frutta per porzione
      FoodCategory.grain: 100,     // Max 100g di cereali/pasta/riso per porzione
      FoodCategory.protein: 200,   // Max 200g di carne/pesce/legumi per porzione
      FoodCategory.dairy: 250,     // Max 250g di latticini per porzione
      FoodCategory.fat: 30,        // Max 30g di grassi (olio, burro) per porzione
      FoodCategory.sweet: 50,      // Max 50g di dolci per porzione
      FoodCategory.beverage: 300,  // Max 300ml di bevande per porzione
      FoodCategory.mixed: 350,     // Max 350g di piatti misti per porzione
    };

    // Definisci limiti minimi realistici per categoria (in grammi)
    final Map<FoodCategory, int> minPortionSizes = {
      FoodCategory.vegetable: 50,  // Min 50g di verdure per porzione
      FoodCategory.fruit: 80,      // Min 80g di frutta per porzione
      FoodCategory.grain: 40,      // Min 40g di cereali/pasta/riso per porzione
      FoodCategory.protein: 50,    // Min 50g di carne/pesce/legumi per porzione
      FoodCategory.dairy: 50,      // Min 50g di latticini per porzione
      FoodCategory.fat: 5,         // Min 5g di grassi (olio, burro) per porzione
      FoodCategory.sweet: 15,      // Min 15g di dolci per porzione
      FoodCategory.beverage: 100,  // Min 100ml di bevande per porzione
      FoodCategory.mixed: 100,     // Min 100g di piatti misti per porzione
    };

    // Alimenti specifici con limiti personalizzati
    final Map<String, int> specificFoodMaxPortions = {
      'Farina integrale': 0,       // Non usare farina integrale direttamente
      'Olio di oliva': 15,         // Max 15g di olio per porzione
      'Burro': 10,                 // Max 10g di burro per porzione
      'Spinaci saltati': 150,      // Max 150g di spinaci saltati per porzione
      'Latte': 200,                // Max 200ml di latte per porzione
      'Yogurt': 150,               // Max 150g di yogurt per porzione
      'Pane': 80,                  // Max 80g di pane per porzione
      'Pasta': 80,                 // Max 80g di pasta (peso a secco) per porzione
      'Riso': 80,                  // Max 80g di riso (peso a secco) per porzione
      'Pollo': 150,                // Max 150g di pollo per porzione
      'Manzo': 120,                // Max 120g di manzo per porzione
      'Pesce': 150,                // Max 150g di pesce per porzione
      'Uova': 100,                 // Max 100g di uova (circa 2 uova) per porzione
      'Formaggio': 50,             // Max 50g di formaggio per porzione
      'Legumi secchi': 70,         // Max 70g di legumi secchi per porzione
      'Legumi cotti': 150,         // Max 150g di legumi cotti per porzione
      'Frutta secca': 30,          // Max 30g di frutta secca per porzione
    };

    // Controlla se l'alimento ha un limite specifico
    if (specificFoodMaxPortions.containsKey(food.name)) {
      final maxPortion = specificFoodMaxPortions[food.name]!;
      // Se il massimo è 0, significa che l'alimento non dovrebbe essere usato direttamente
      if (maxPortion == 0) {
        return 0;
      }
      return min(calculatedGrams, maxPortion);
    }

    // Altrimenti usa i limiti per categoria
    int maxPortion = 100; // Default
    int minPortion = 30;  // Default

    // Trova la categoria principale dell'alimento
    for (var category in food.categories) {
      if (maxPortionSizes.containsKey(category)) {
        maxPortion = maxPortionSizes[category]!;
        minPortion = minPortionSizes[category]!;
        break;
      }
    }

    // Limita la porzione tra il minimo e il massimo
    return max(min(calculatedGrams, maxPortion), minPortion);
  }

  /// VALIDAZIONE E CORREZIONE CALORIE - CRITICO PER PRECISIONE
  DailyDietPlan _validateAndCorrectCalories(DailyDietPlan dailyPlan, UserProfile userProfile) {
    print('🔍 VALIDAZIONE CALORIE: Inizio controllo precisione...');

    // Calcola le calorie totali attuali
    int totalActualCalories = 0;
    for (final meal in dailyPlan.meals) {
      for (final portion in meal.foods) {
        totalActualCalories += portion.calories;
      }
    }

    final calorieTarget = dailyPlan.calorieTarget;
    final calorieDifference = calorieTarget - totalActualCalories;
    final toleranceCalories = 50; // ±50 calorie di tolleranza

    print('🎯 Target calorie: $calorieTarget kcal');
    print('📊 Calorie attuali: $totalActualCalories kcal');
    print('📈 Differenza: $calorieDifference kcal');

    // Se la differenza è entro la tolleranza, il piano è già accurato
    if (calorieDifference.abs() <= toleranceCalories) {
      print('✅ Piano già accurato (differenza: ${calorieDifference}kcal ≤ ${toleranceCalories}kcal)');
      return dailyPlan;
    }

    print('⚠️ Piano necessita correzione (differenza: ${calorieDifference}kcal > ${toleranceCalories}kcal)');

    // Crea una copia modificabile dei pasti
    final correctedMeals = <PlannedMeal>[];

    for (final meal in dailyPlan.meals) {
      // Calcola la correzione proporzionale per questo pasto
      final mealCurrentCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);
      final mealProportion = mealCurrentCalories / totalActualCalories;
      final mealCalorieAdjustment = (calorieDifference * mealProportion).round();

      print('🍽️ Pasto ${meal.name}: ${mealCurrentCalories}kcal, aggiustamento: ${mealCalorieAdjustment >= 0 ? '+' : ''}${mealCalorieAdjustment}kcal');

      // Correggi le porzioni del pasto
      final correctedFoods = _adjustMealPortions(meal.foods, mealCalorieAdjustment);

      // Crea il pasto corretto
      final correctedMeal = PlannedMeal(
        id: meal.id,
        name: meal.name,
        type: meal.type,
        foods: correctedFoods,
        time: meal.time,
        isCompleted: meal.isCompleted,
      );

      correctedMeals.add(correctedMeal);
    }

    // Verifica finale
    final finalCalories = correctedMeals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    print('🎯 Calorie finali dopo correzione: ${finalCalories}kcal (target: ${calorieTarget}kcal)');
    print('✅ Differenza finale: ${(calorieTarget - finalCalories).abs()}kcal');

    // Crea il piano corretto
    return DailyDietPlan(
      date: dailyPlan.date,
      meals: correctedMeals,
      calorieTarget: dailyPlan.calorieTarget,
      macroTargets: dailyPlan.macroTargets,
    );
  }

  /// Aggiusta le porzioni di un pasto per raggiungere le calorie target
  List<FoodPortion> _adjustMealPortions(List<FoodPortion> originalFoods, int calorieDifference) {
    if (originalFoods.isEmpty || calorieDifference == 0) {
      return originalFoods;
    }

    final adjustedFoods = <FoodPortion>[];

    for (final portion in originalFoods) {
      // Calcola il fattore di aggiustamento proporzionale
      final currentCalories = portion.calories.toDouble();
      if (currentCalories <= 0) {
        adjustedFoods.add(portion);
        continue;
      }

      // Calcola l'aggiustamento per questa porzione
      final portionAdjustment = (calorieDifference * (currentCalories / originalFoods.fold(0.0, (sum, p) => sum + p.calories.toDouble()))).round();
      final targetCalories = currentCalories + portionAdjustment;

      // Calcola i nuovi grammi basati sulle calorie target
      final newGrams = (targetCalories * portion.grams / currentCalories).round();

      // Limita a porzioni realistiche (min 10g, max 500g)
      final limitedGrams = newGrams.clamp(10, 500);

      // Crea la porzione aggiustata
      final adjustedPortion = FoodPortion(
        food: portion.food,
        grams: limitedGrams,
      );

      adjustedFoods.add(adjustedPortion);
    }

    return adjustedFoods;
  }

  // SISTEMA DI VARIETÀ E ALTERNATIVE
  static final Set<String> _recentlyUsedFoods = <String>{};
  static final Map<String, List<String>> _foodAlternatives = <String, List<String>>{};

  /// Ottieni gli alimenti usati di recente per evitare ripetizioni
  Set<String> _getRecentlyUsedFoods() {
    return Set.from(_recentlyUsedFoods);
  }

  /// Aggiungi un alimento alla lista degli usati di recente
  void _addToRecentlyUsed(String foodId) {
    _recentlyUsedFoods.add(foodId);

    // Mantieni solo gli ultimi 20 alimenti per evitare memoria eccessiva
    if (_recentlyUsedFoods.length > 20) {
      final oldestFood = _recentlyUsedFoods.first;
      _recentlyUsedFoods.remove(oldestFood);
    }
  }

  /// Pulisci la lista degli alimenti usati di recente (per nuovi piani settimanali)
  static void clearRecentlyUsed() {
    _recentlyUsedFoods.clear();
  }

  /// Ottieni alternative culturalmente appropriate per un alimento
  List<Food> getFoodAlternatives(Food originalFood, List<Food> availableFoods) {
    print('🔄 Ricerca alternative per: ${originalFood.name}');

    final alternatives = <Food>[];

    // Criteri per alternative appropriate:
    // 1. Stessa categoria principale
    // 2. Calorie simili (±30%)
    // 3. Stesso tipo di pasto appropriato
    // 4. Profilo nutrizionale simile

    final targetCalories = originalFood.calories;
    final calorieRange = (targetCalories * 0.3).round(); // ±30%
    final minCalories = targetCalories - calorieRange;
    final maxCalories = targetCalories + calorieRange;

    for (final food in availableFoods) {
      if (food.id == originalFood.id) continue; // Salta l'alimento originale

      // Verifica categoria principale condivisa
      final sharedCategories = originalFood.categories.where((cat) => food.categories.contains(cat)).toList();
      if (sharedCategories.isEmpty) continue;

      // Verifica calorie simili
      if (food.calories < minCalories || food.calories > maxCalories) continue;

      // Verifica appropriatezza per gli stessi tipi di pasto
      final sharedMealTypes = originalFood.suitableForMeals.where((meal) => food.suitableForMeals.contains(meal)).toList();
      if (sharedMealTypes.isEmpty) continue;

      // Verifica appropriatezza culturale per gli stessi pasti
      bool culturallyAppropriate = true;
      for (final mealType in sharedMealTypes) {
        if (!MealAppropriatenessValidator.isAppropriateForMeal(food, mealType)) {
          culturallyAppropriate = false;
          break;
        }
      }

      if (culturallyAppropriate) {
        alternatives.add(food);
      }
    }

    // Ordina per similarità nutrizionale
    alternatives.sort((a, b) {
      final aDiff = (a.calories - targetCalories).abs() +
                   (a.proteins - originalFood.proteins).abs() +
                   (a.carbs - originalFood.carbs).abs() +
                   (a.fats - originalFood.fats).abs();
      final bDiff = (b.calories - targetCalories).abs() +
                   (b.proteins - originalFood.proteins).abs() +
                   (b.carbs - originalFood.carbs).abs() +
                   (b.fats - originalFood.fats).abs();
      return aDiff.compareTo(bDiff);
    });

    // Restituisci le prime 3 alternative migliori
    final topAlternatives = alternatives.take(3).toList();

    print('   Trovate ${topAlternatives.length} alternative appropriate:');
    for (final alt in topAlternatives) {
      print('   - ${alt.name} (${alt.calories}kcal, categorie: ${alt.categories.map((c) => c.toString().split('.').last).join(', ')})');
    }

    return topAlternatives;
  }

  /// Genera un piano con alternative per ogni alimento selezionato
  Map<String, List<Food>> generateMealPlanWithAlternatives(DailyDietPlan dailyPlan, List<Food> allFoods) {
    print('🔄 Generazione alternative per piano giornaliero...');

    final mealAlternatives = <String, List<Food>>{};

    for (final meal in dailyPlan.meals) {
      for (final portion in meal.foods) {
        final alternatives = getFoodAlternatives(portion.food, allFoods);
        mealAlternatives['${meal.id}_${portion.food.id}'] = alternatives;
      }
    }

    print('✅ Generate alternative per ${mealAlternatives.length} alimenti');
    return mealAlternatives;
  }
}
