/// Modello per il profilo utente Supabase
class SupabaseProfile {
  final String id;
  final String email;
  final String nome;
  final String username;
  final String? bio;
  final String? fotoProfiloUrl;
  final DateTime? dataNascita;
  final String? genere;
  final String? citta;
  final String? paese;
  final String? telefono;
  final bool isVerified;
  final bool isPremium;
  final String privacyLevel;
  final DateTime createdAt;
  final DateTime updatedAt;

  SupabaseProfile({
    required this.id,
    required this.email,
    required this.nome,
    required this.username,
    this.bio,
    this.fotoProfiloUrl,
    this.dataNascita,
    this.genere,
    this.citta,
    this.paese,
    this.telefono,
    this.isVerified = false,
    this.isPremium = false,
    this.privacyLevel = 'pubblico',
    required this.createdAt,
    required this.updatedAt,
  });

  /// Crea un SupabaseProfile da JSON
  factory SupabaseProfile.fromJson(Map<String, dynamic> json) {
    return SupabaseProfile(
      id: json['id'] as String,
      email: json['email'] as String,
      nome: json['nome'] as String? ?? '',
      username: json['username'] as String? ?? '',
      bio: json['bio'] as String?,
      fotoProfiloUrl: json['foto_profilo_url'] as String?,
      dataNascita: json['data_nascita'] != null 
        ? DateTime.parse(json['data_nascita'] as String)
        : null,
      genere: json['genere'] as String?,
      citta: json['citta'] as String?,
      paese: json['paese'] as String?,
      telefono: json['telefono'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      isPremium: json['is_premium'] as bool? ?? false,
      privacyLevel: json['privacy_level'] as String? ?? 'pubblico',
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Converte il SupabaseProfile in JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'nome': nome,
      'username': username,
      'bio': bio,
      'foto_profilo_url': fotoProfiloUrl,
      'data_nascita': dataNascita?.toIso8601String(),
      'genere': genere,
      'citta': citta,
      'paese': paese,
      'telefono': telefono,
      'is_verified': isVerified,
      'is_premium': isPremium,
      'privacy_level': privacyLevel,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Crea una copia del profilo con modifiche
  SupabaseProfile copyWith({
    String? id,
    String? email,
    String? nome,
    String? username,
    String? bio,
    String? fotoProfiloUrl,
    DateTime? dataNascita,
    String? genere,
    String? citta,
    String? paese,
    String? telefono,
    bool? isVerified,
    bool? isPremium,
    String? privacyLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SupabaseProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      nome: nome ?? this.nome,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      fotoProfiloUrl: fotoProfiloUrl ?? this.fotoProfiloUrl,
      dataNascita: dataNascita ?? this.dataNascita,
      genere: genere ?? this.genere,
      citta: citta ?? this.citta,
      paese: paese ?? this.paese,
      telefono: telefono ?? this.telefono,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SupabaseProfile(id: $id, nome: $nome, username: $username, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupabaseProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
