import 'dart:convert';
import 'meal.dart';
import 'food.dart';
import 'user_profile.dart';

class PlannedMeal {
  final String id;
  final String name;
  final String type;
  final List<FoodPortion> foods;
  final String time; // Orario del pasto, es. "08:00"
  final bool isCompleted;

  PlannedMeal({
    required this.id,
    required this.name,
    required this.type,
    required this.foods,
    required this.time,
    this.isCompleted = false,
  });

  // Calcola le calorie totali del pasto
  int get totalCalories {
    return foods.fold(0, (sum, food) => sum + food.calories);
  }

  // Calcola i macronutrienti totali del pasto
  Map<String, double> get totalMacros {
    double proteins = 0;
    double carbs = 0;
    double fats = 0;

    for (var food in foods) {
      proteins += food.proteins;
      carbs += food.carbs;
      fats += food.fats;
    }

    return {
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
    };
  }

  // Converti a Meal (per compatibilità con il resto dell'app)
  Meal toMeal() {
    // Converti FoodPortion in FoodItem per mantenere i dettagli degli alimenti
    final foodItems = foods.map((foodPortion) {
      return FoodItem(
        food: foodPortion.food,
        quantity: foodPortion.grams.toDouble(),
      );
    }).toList();

    return Meal(
      nome: name,
      orario: time,
      calorie: totalCalories,
      proteine: double.parse(getMacroValue('proteins').toStringAsFixed(1)),
      carboidrati: double.parse(getMacroValue('carbs').toStringAsFixed(1)),
      grassi: double.parse(getMacroValue('fats').toStringAsFixed(1)),
      completato: isCompleted,
      foods: foodItems, // Mantieni i dettagli degli alimenti
    );
  }

  // Converti da PlannedMeal a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'foods': foods.map((food) => food.toMap()).toList(),
      'time': time,
      'isCompleted': isCompleted,
    };
  }

  // Converti da Map a PlannedMeal
  factory PlannedMeal.fromMap(Map<String, dynamic> map) {
    return PlannedMeal(
      id: map['id'] as String,
      name: map['name'] as String,
      type: map['type'] as String,
      foods: (map['foods'] as List)
          .map((foodMap) => FoodPortion.fromMap(foodMap as Map<String, dynamic>))
          .toList(),
      time: map['time'] as String,
      isCompleted: map['isCompleted'] as bool? ?? false,
    );
  }

  // Ottieni un macronutriente specifico
  double getMacroValue(String macroKey) {
    double total = 0;
    for (var foodPortion in foods) {
      switch (macroKey) {
        case 'proteins':
          total += foodPortion.proteins;
          break;
        case 'carbs':
          total += foodPortion.carbs;
          break;
        case 'fats':
          total += foodPortion.fats;
          break;
      }
    }
    return total;
  }

  // Crea una copia del pasto con possibilità di sovrascrivere alcuni campi
  PlannedMeal copyWith({
    String? id,
    String? name,
    String? type,
    List<FoodPortion>? foods,
    String? time,
    bool? isCompleted,
  }) {
    return PlannedMeal(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      foods: foods ?? this.foods,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

class FoodPortion {
  final Food food;
  final int grams;
  final String notes;

  FoodPortion({
    required this.food,
    required this.grams,
    this.notes = '',
  });

  // Calcola le calorie per questa porzione
  int get calories {
    return food.calculateCaloriesForServing(grams);
  }

  // Calcola i macronutrienti per questa porzione
  double get proteins {
    final macros = food.calculateMacrosForServing(grams);
    return macros['proteins'] ?? 0.0;
  }

  double get carbs {
    final macros = food.calculateMacrosForServing(grams);
    return macros['carbs'] ?? 0.0;
  }

  double get fats {
    final macros = food.calculateMacrosForServing(grams);
    return macros['fats'] ?? 0.0;
  }

  // Converti da FoodPortion a Map
  Map<String, dynamic> toMap() {
    return {
      'food': food.toMap(),
      'grams': grams,
      'notes': notes,
    };
  }

  // Converti da Map a FoodPortion
  factory FoodPortion.fromMap(Map<String, dynamic> map) {
    return FoodPortion(
      food: Food.fromMap(map['food'] as Map<String, dynamic>),
      grams: map['grams'] as int,
      notes: map['notes'] as String? ?? '',
    );
  }

  // Crea una copia della porzione con possibilità di sovrascrivere alcuni campi
  FoodPortion copyWith({
    Food? food,
    int? grams,
    String? notes,
  }) {
    return FoodPortion(
      food: food ?? this.food,
      grams: grams ?? this.grams,
      notes: notes ?? this.notes,
    );
  }
}

class DailyDietPlan {
  final String date;
  final List<PlannedMeal> meals;
  final int calorieTarget;
  final Map<String, int> macroTargets; // in grammi

  DailyDietPlan({
    required this.date,
    required this.meals,
    required this.calorieTarget,
    required this.macroTargets,
  });

  // Calcola le calorie totali consumate
  int get totalCaloriesConsumed {
    return meals
        .where((meal) => meal.isCompleted)
        .fold(0, (sum, meal) => sum + meal.totalCalories);
  }

  // Calcola le calorie totali pianificate
  int get totalCaloriesPlanned {
    return meals.fold(0, (sum, meal) => sum + meal.totalCalories);
  }

  // Calcola i macronutrienti totali consumati
  Map<String, double> get totalMacrosConsumed {
    double proteins = 0;
    double carbs = 0;
    double fats = 0;

    for (var meal in meals.where((meal) => meal.isCompleted)) {
      proteins += meal.getMacroValue('proteins');
      carbs += meal.getMacroValue('carbs');
      fats += meal.getMacroValue('fats');
    }

    return {
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
    };
  }

  // Calcola i macronutrienti totali pianificati
  Map<String, double> get totalMacrosPlanned {
    double proteins = 0;
    double carbs = 0;
    double fats = 0;

    for (var meal in meals) {
      proteins += meal.getMacroValue('proteins');
      carbs += meal.getMacroValue('carbs');
      fats += meal.getMacroValue('fats');
    }

    return {
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
    };
  }

  // Ottieni un macronutriente specifico con gestione null-safety
  double getMacroValuePlanned(String macroKey) {
    final macros = totalMacrosPlanned;
    return macros[macroKey] ?? 0.0;
  }

  // Ottieni un macronutriente target con gestione null-safety
  int getMacroTarget(String macroKey) {
    return macroTargets[macroKey] ?? 0;
  }

  // Metodi di compatibilità per advanced_diet_plan_screen.dart
  int getTotalCalories() {
    return totalCaloriesPlanned;
  }

  double getTotalMacro(String macroKey) {
    return getMacroValuePlanned(macroKey);
  }

  // Converti da DailyDietPlan a Map
  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'meals': meals.map((meal) => meal.toMap()).toList(),
      'calorieTarget': calorieTarget,
      'macroTargets': macroTargets,
    };
  }

  // Converti da Map a DailyDietPlan
  factory DailyDietPlan.fromMap(Map<String, dynamic> map) {
    return DailyDietPlan(
      date: map['date'] as String,
      meals: (map['meals'] as List)
          .map((mealMap) => PlannedMeal.fromMap(mealMap as Map<String, dynamic>))
          .toList(),
      calorieTarget: map['calorieTarget'] as int,
      macroTargets: Map<String, int>.from(map['macroTargets'] as Map),
    );
  }

  // Crea una copia del piano giornaliero con possibilità di sovrascrivere alcuni campi
  DailyDietPlan copyWith({
    String? date,
    List<PlannedMeal>? meals,
    int? calorieTarget,
    Map<String, int>? macroTargets,
  }) {
    return DailyDietPlan(
      date: date ?? this.date,
      meals: meals ?? this.meals,
      calorieTarget: calorieTarget ?? this.calorieTarget,
      macroTargets: macroTargets ?? this.macroTargets,
    );
  }
}

class WeeklyDietPlan {
  final String id;
  final String name;
  final String startDate;
  final List<DailyDietPlan> dailyPlans;
  final UserProfile userProfile;

  WeeklyDietPlan({
    required this.id,
    required this.name,
    required this.startDate,
    required this.dailyPlans,
    required this.userProfile,
  });

  // Ottieni un piano giornaliero per una data specifica
  DailyDietPlan? getPlanForDate(String date) {
    try {
      return dailyPlans.firstWhere((plan) => plan.date == date);
    } catch (e) {
      return null;
    }
  }

  // Aggiorna o aggiungi un piano giornaliero
  WeeklyDietPlan updateDailyPlan(DailyDietPlan newPlan) {
    final existingIndex = dailyPlans.indexWhere((plan) => plan.date == newPlan.date);

    if (existingIndex >= 0) {
      // Aggiorna il piano esistente
      final updatedPlans = List<DailyDietPlan>.from(dailyPlans);
      updatedPlans[existingIndex] = newPlan;
      return copyWith(dailyPlans: updatedPlans);
    } else {
      // Aggiungi un nuovo piano
      final updatedPlans = List<DailyDietPlan>.from(dailyPlans)..add(newPlan);
      // Ordina i piani per data
      updatedPlans.sort((a, b) => a.date.compareTo(b.date));
      return copyWith(dailyPlans: updatedPlans);
    }
  }

  // Converti da WeeklyDietPlan a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'startDate': startDate,
      'dailyPlans': dailyPlans.map((plan) => plan.toMap()).toList(),
      'userProfile': userProfile.toMap(),
    };
  }

  // Converti da Map a WeeklyDietPlan
  factory WeeklyDietPlan.fromMap(Map<String, dynamic> map) {
    return WeeklyDietPlan(
      id: map['id'] as String,
      name: map['name'] as String,
      startDate: map['startDate'] as String,
      dailyPlans: (map['dailyPlans'] as List)
          .map((planMap) => DailyDietPlan.fromMap(planMap as Map<String, dynamic>))
          .toList(),
      userProfile: UserProfile.fromMap(map['userProfile'] as Map<String, dynamic>),
    );
  }

  // Converti da WeeklyDietPlan a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a WeeklyDietPlan
  factory WeeklyDietPlan.fromJson(String source) =>
      WeeklyDietPlan.fromMap(json.decode(source) as Map<String, dynamic>);

  // Crea una copia del piano settimanale con possibilità di sovrascrivere alcuni campi
  WeeklyDietPlan copyWith({
    String? id,
    String? name,
    String? startDate,
    List<DailyDietPlan>? dailyPlans,
    UserProfile? userProfile,
  }) {
    return WeeklyDietPlan(
      id: id ?? this.id,
      name: name ?? this.name,
      startDate: startDate ?? this.startDate,
      dailyPlans: dailyPlans ?? this.dailyPlans,
      userProfile: userProfile ?? this.userProfile,
    );
  }
}
