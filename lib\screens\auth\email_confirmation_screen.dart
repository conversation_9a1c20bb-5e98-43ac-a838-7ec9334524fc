// COPIA E INCOLLA TUTTO QUESTO CODICE NEL TUO FILE:

import 'package:flutter/material.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Schermata di conferma email dopo la registrazione
class EmailConfirmationScreen extends StatelessWidget {
  final String email;

  const EmailConfirmationScreen({
    Key? key,
    required this.email,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Conferma il Tuo Account'),
        automaticallyImplyLeading: false, // Nasconde la freccia per tornare indietro
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      // --- INIZIO MODIFICA ---
      // 1. Sostituisci "Center" con "SingleChildScrollView" per rendere la pagina scorrevole.
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            // 2. Rimuovi "mainAxisAlignment.center" perché non ha più effetto in una colonna scorrevole.
            //    Aggiungi uno spazio all'inizio e alla fine per l'estetica.
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20), // Spazio aggiunto all'inizio
              
              // Icona email con animazione
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.mark_email_read_outlined,
                  size: 60,
                  color: DrStaffilanoTheme.primaryGreen,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Titolo principale
              const Text(
                'Controlla la tua posta!',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.primaryGreen,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Sottotitolo con email
              Text(
                'Abbiamo inviato un link di conferma a:',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Email in evidenza
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  email,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DrStaffilanoTheme.primaryGreen,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Istruzioni dettagliate
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Cosa fare ora:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. Controlla la tua casella di posta elettronica\n'
                      '2. Cerca un\'email da Supabase Auth\n' // Modificato "Supabase Auth"
                      '3. Clicca sul link di conferma nell\'email\n'
                      '4. Torna qui per effettuare il login',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Nota spam
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.shade200,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      color: Colors.orange.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Non trovi l\'email? Controlla la cartella spam o posta indesiderata.',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.orange.shade800,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Pulsante per tornare al login
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    // Riporta l'utente alla schermata di login
                    Navigator.of(context).pushNamedAndRemoveUntil(
                      '/', 
                      (route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DrStaffilanoTheme.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'Torna al Login',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Pulsante secondario per reinviare email (opzionale)
              TextButton(
                onPressed: () {
                  // TODO: Implementare reinvio email se necessario
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Funzione di reinvio email in arrivo...'),
                      backgroundColor: DrStaffilanoTheme.primaryGreen,
                    ),
                  );
                },
                child: Text(
                  'Non hai ricevuto l\'email? Reinvia',
                  style: TextStyle(
                    color: DrStaffilanoTheme.primaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              const SizedBox(height: 20), // Spazio aggiunto alla fine
            ],
          ),
        ),
      ),
      // --- FINE MODIFICA ---
    );
  }
}