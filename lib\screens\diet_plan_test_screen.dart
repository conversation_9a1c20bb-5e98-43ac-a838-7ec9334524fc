import 'package:flutter/material.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../services/specific_diet_generator_service.dart';
import '../services/user_profile_service.dart';
import '../widgets/planned_meal_card.dart';
import '../theme/app_theme.dart';

/// Schermata di test per visualizzare i piani dietetici con alternative
class DietPlanTestScreen extends StatefulWidget {
  const DietPlanTestScreen({Key? key}) : super(key: key);

  @override
  State<DietPlanTestScreen> createState() => _DietPlanTestScreenState();
}

class _DietPlanTestScreenState extends State<DietPlanTestScreen> {
  DailyDietPlan? _dailyPlan;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _generateTestPlan();
  }

  Future<void> _generateTestPlan() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Ottieni il profilo utente
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getUserProfile();

      if (userProfile == null) {
        throw Exception('Profilo utente non trovato');
      }

      // Genera un piano giornaliero di test
      final dietService = await SpecificDietGeneratorService.getInstance();
      final dailyPlan = await dietService.generateDailyDietPlan(
        userProfile,
        DateTime.now(),
      );

      setState(() {
        _dailyPlan = dailyPlan;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore nella generazione del piano: $e';
        _isLoading = false;
      });
    }
  }

  void _onMealCompletedChanged(String mealId, bool isCompleted) {
    if (_dailyPlan == null) return;

    setState(() {
      final mealIndex = _dailyPlan!.meals.indexWhere((meal) => meal.id == mealId);
      if (mealIndex != -1) {
        _dailyPlan!.meals[mealIndex] = PlannedMeal(
          id: _dailyPlan!.meals[mealIndex].id,
          name: _dailyPlan!.meals[mealIndex].name,
          type: _dailyPlan!.meals[mealIndex].type,
          foods: _dailyPlan!.meals[mealIndex].foods,
          time: _dailyPlan!.meals[mealIndex].time,
          isCompleted: isCompleted,
        );
      }
    });

    // Mostra feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isCompleted 
            ? 'Pasto completato! 🎉' 
            : 'Pasto rimosso dai completati',
        ),
        backgroundColor: isCompleted ? AppTheme.primaryColor : Colors.grey,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onFoodReplaced(String mealId, Food oldFood, Food newFood) {
    if (_dailyPlan == null) return;

    setState(() {
      final mealIndex = _dailyPlan!.meals.indexWhere((meal) => meal.id == mealId);
      if (mealIndex != -1) {
        final meal = _dailyPlan!.meals[mealIndex];
        final updatedFoods = meal.foods.map((portion) {
          if (portion.food.id == oldFood.id) {
            // Mantieni la stessa quantità in grammi
            return FoodPortion(food: newFood, grams: portion.grams);
          }
          return portion;
        }).toList();

        _dailyPlan!.meals[mealIndex] = PlannedMeal(
          id: meal.id,
          name: meal.name,
          type: meal.type,
          foods: updatedFoods,
          time: meal.time,
          isCompleted: meal.isCompleted,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Piano Dietetico'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateTestPlan,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generazione piano dietetico...'),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Errore',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _generateTestPlan,
              child: const Text('Riprova'),
            ),
          ],
        ),
      );
    }

    if (_dailyPlan == null) {
      return const Center(
        child: Text('Nessun piano disponibile'),
      );
    }

    final totalCalories = _dailyPlan!.meals.fold(0, (sum, meal) => 
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    return Column(
      children: [
        // Header con statistiche
        Container(
          padding: const EdgeInsets.all(16),
          color: AppTheme.primaryColor.withOpacity(0.1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatChip('Target', '${_dailyPlan!.calorieTarget} kcal', AppTheme.primaryColor),
              _buildStatChip('Attuale', '${totalCalories} kcal', AppTheme.accentColor),
              _buildStatChip('Pasti', '${_dailyPlan!.meals.length}', AppTheme.secondaryColor),
            ],
          ),
        ),

        // Lista pasti
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: _dailyPlan!.meals.length,
            itemBuilder: (context, index) {
              final meal = _dailyPlan!.meals[index];
              return PlannedMealCard(
                meal: meal,
                onCompletedChanged: (isCompleted) => _onMealCompletedChanged(meal.id, isCompleted),
                onFoodReplaced: _onFoodReplaced,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
