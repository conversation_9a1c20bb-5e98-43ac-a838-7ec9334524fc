import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Responsive utilities for cross-platform design consistency
class ResponsiveUtils {
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;
  static const double _desktopBreakpoint = 1200;

  /// Device type enumeration
  enum DeviceType { mobile, tablet, desktop }

  /// Get device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < _mobileBreakpoint) return DeviceType.mobile;
    if (width < _tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  /// Check if device is mobile
  static bool isMobile(BuildContext context) => 
      getDeviceType(context) == DeviceType.mobile;

  /// Check if device is tablet
  static bool isTablet(BuildContext context) => 
      getDeviceType(context) == DeviceType.tablet;

  /// Check if device is desktop
  static bool isDesktop(BuildContext context) => 
      getDeviceType(context) == DeviceType.desktop;

  /// Get responsive font size based on device type
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final deviceType = getDeviceType(context);
    final textScaleFactor = MediaQuery.textScaleFactorOf(context);
    
    double scaleFactor;
    switch (deviceType) {
      case DeviceType.mobile:
        scaleFactor = 1.0;
        break;
      case DeviceType.tablet:
        scaleFactor = 1.1;
        break;
      case DeviceType.desktop:
        scaleFactor = 1.2;
        break;
    }
    
    return baseFontSize * scaleFactor * textScaleFactor;
  }

  /// Get responsive padding based on device type
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double mobile = 16.0,
    double tablet = 24.0,
    double desktop = 32.0,
  }) {
    final deviceType = getDeviceType(context);
    double padding;
    
    switch (deviceType) {
      case DeviceType.mobile:
        padding = mobile;
        break;
      case DeviceType.tablet:
        padding = tablet;
        break;
      case DeviceType.desktop:
        padding = desktop;
        break;
    }
    
    return EdgeInsets.all(padding);
  }

  /// Get responsive margin based on device type
  static EdgeInsets getResponsiveMargin(BuildContext context, {
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    final deviceType = getDeviceType(context);
    double margin;
    
    switch (deviceType) {
      case DeviceType.mobile:
        margin = mobile;
        break;
      case DeviceType.tablet:
        margin = tablet;
        break;
      case DeviceType.desktop:
        margin = desktop;
        break;
    }
    
    return EdgeInsets.all(margin);
  }

  /// Get responsive width based on screen size
  static double getResponsiveWidth(BuildContext context, {
    double mobileRatio = 1.0,
    double tabletRatio = 0.8,
    double desktopRatio = 0.6,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth * mobileRatio;
      case DeviceType.tablet:
        return screenWidth * tabletRatio;
      case DeviceType.desktop:
        return screenWidth * desktopRatio;
    }
  }

  /// Get minimum touch target size based on platform
  static double getMinTouchTargetSize() {
    if (kIsWeb) return 44.0; // Web minimum
    if (defaultTargetPlatform == TargetPlatform.iOS) return 44.0; // iOS minimum
    return 48.0; // Android minimum
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSize;
      case DeviceType.tablet:
        return baseSize * 1.2;
      case DeviceType.desktop:
        return baseSize * 1.4;
    }
  }

  /// Get responsive border radius
  static double getResponsiveBorderRadius(BuildContext context, double baseRadius) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseRadius;
      case DeviceType.tablet:
        return baseRadius * 1.1;
      case DeviceType.desktop:
        return baseRadius * 1.2;
    }
  }

  /// Get responsive elevation
  static double getResponsiveElevation(BuildContext context, double baseElevation) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseElevation;
      case DeviceType.tablet:
        return baseElevation * 1.2;
      case DeviceType.desktop:
        return baseElevation * 1.5;
    }
  }

  /// Get responsive grid columns
  static int getResponsiveGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
        return 4;
    }
  }

  /// Get responsive app bar height
  static double getResponsiveAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight * 1.2;
      case DeviceType.desktop:
        return kToolbarHeight * 1.4;
    }
  }

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get responsive card width for lists
  static double getResponsiveCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth - 32; // Full width with margins
      case DeviceType.tablet:
        return (screenWidth - 64) / 2; // Two columns
      case DeviceType.desktop:
        return (screenWidth - 96) / 3; // Three columns
    }
  }

  /// Get responsive maximum content width
  static double getMaxContentWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return double.infinity;
      case DeviceType.tablet:
        return 800;
      case DeviceType.desktop:
        return 1200;
    }
  }
}
