import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/advanced_user_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../data/food_database.dart';
import '../utils/advanced_nutrition_properties.dart';
import 'food_database_service.dart';
import 'extended_categories_service.dart';
import 'advanced_nutrition_service.dart';
import 'advanced_food_selector.dart';
import 'food_safety_service.dart';

/// Servizio per la generazione avanzata di piani dietetici personalizzati
class AdvancedDietGeneratorService {
  final FoodDatabaseService _databaseService;
  final FoodDatabase _foodDatabase;
  final Uuid _uuid = const Uuid();

  // Singleton
  static AdvancedDietGeneratorService? _instance;

  // Costruttore privato
  AdvancedDietGeneratorService._({
    FoodDatabaseService? databaseService,
    FoodDatabase? foodDatabase,
  }) :
    _databaseService = databaseService ?? FoodDatabaseService.getInstance() as FoodDatabaseService,
    _foodDatabase = foodDatabase ?? FoodDatabase();

  /// Ottieni l'istanza singleton del servizio
  static Future<AdvancedDietGeneratorService> getInstance() async {
    if (_instance == null) {
      final databaseService = await FoodDatabaseService.getInstance();
      _instance = AdvancedDietGeneratorService._(databaseService: databaseService);
    }
    return _instance!;
  }

  /// Genera un piano dietetico settimanale avanzato
  Future<WeeklyDietPlan> generateWeeklyDietPlan(
    AdvancedUserProfile advancedProfile,
    List<Food> availableFoods, {
    int weeks = 1,
  }) async {
    print('Generazione piano dietetico settimanale avanzato');
    print('Durata: $weeks settimane');

    // Ottieni il profilo base
    final userProfile = advancedProfile.baseProfile;

    // Calcola l'obiettivo calorico e la distribuzione dei macronutrienti
    final calorieTarget = advancedProfile.calculateAdjustedCalorieTarget();
    final macroGrams = advancedProfile.calculateAdjustedMacroGrams();

    print('Obiettivo calorico adattato: $calorieTarget kcal');
    print('Obiettivo macronutrienti adattato: $macroGrams');

    // Crea un piano settimanale vuoto
    final startDate = _getStartOfWeek(DateTime.now());
    final weeklyPlan = WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano Settimanale Personalizzato ${startDate.day}/${startDate.month}/${startDate.year}',
      startDate: _formatDate(startDate),
      dailyPlans: [],
      userProfile: userProfile,
    );

    // Mappa per tenere traccia degli alimenti utilizzati nei giorni precedenti
    final Map<String, int> usedFoodsCount = {};

    // Genera un piano per ogni giorno della settimana
    WeeklyDietPlan updatedWeeklyPlan = weeklyPlan;

    for (int i = 0; i < 7 * weeks; i++) {
      final date = startDate.add(Duration(days: i));
      final dailyPlan = await generateDailyDietPlan(
        advancedProfile,
        _formatDate(date),
        calorieTarget,
        macroGrams,
        usedFoodsCount,
        dayOfWeek: i % 7,
      );

      // Aggiorna il conteggio degli alimenti utilizzati
      for (var meal in dailyPlan.meals) {
        for (var foodPortion in meal.foods) {
          final foodId = foodPortion.food.id;
          usedFoodsCount[foodId] = (usedFoodsCount[foodId] ?? 0) + 1;
        }
      }

      updatedWeeklyPlan = updatedWeeklyPlan.updateDailyPlan(dailyPlan);
    }

    return updatedWeeklyPlan;
  }

  /// Genera un piano dietetico giornaliero avanzato
  Future<DailyDietPlan> generateDailyDietPlan(
    AdvancedUserProfile advancedProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
    Map<String, int> usedFoodsCount, {
    int dayOfWeek = 0,
  }) async {
    print('Generazione piano giornaliero avanzato per la data: $date');
    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivi macronutrienti: $macroTargets');

    // Ottieni il profilo base
    final userProfile = advancedProfile.baseProfile;

    // Determina il numero di pasti in base alle preferenze dell'utente
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Ottieni la distribuzione dei pasti
    final mealDistribution = advancedProfile.getMealDistribution();

    // Distribuisci le calorie tra i pasti in base alla distribuzione personalizzata
    final mealCalorieDistribution = _distributeMealCaloriesAdvanced(
      calorieTarget,
      mealsPerDay,
      mealDistribution,
      dayOfWeek
    );
    print('Distribuzione calorie per pasto: $mealCalorieDistribution');

    // Distribuisci i macronutrienti tra i pasti
    final mealMacroDistribution = _distributeMealMacrosAdvanced(
      macroTargets,
      mealsPerDay,
      mealCalorieDistribution,
      advancedProfile
    );
    print('Distribuzione macronutrienti per pasto: $mealMacroDistribution');

    // Filtra gli alimenti disponibili in base alle condizioni mediche e intolleranze
    final filteredFoods = await _filterFoodsForSpecialConditions(advancedProfile);
    print('Alimenti disponibili dopo filtro per condizioni speciali: ${filteredFoods.length}');

    // Genera i pasti
    final meals = <PlannedMeal>[];

    // Orari predefiniti per i pasti, adattati al timing preferito
    final mealTimes = _getAdjustedMealTimes(mealsPerDay, advancedProfile.mealTiming);

    // Genera ogni pasto
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final mealCalories = mealCalorieDistribution[i];
      final mealMacros = mealMacroDistribution[i];

      print('Generazione pasto $i: tipo=$mealType, calorie=$mealCalories, macros=$mealMacros');

      final meal = await _generateAdvancedMeal(
        advancedProfile,
        mealType,
        mealCalories,
        mealMacros,
        mealTimes[i],
        filteredFoods,
        usedFoodsCount,
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // Verifica e correggi il piano giornaliero per garantire la precisione
    return _validateAndCorrectDailyPlan(dailyPlan, userProfile);
  }

  /// Filtra gli alimenti disponibili in base alle condizioni mediche e intolleranze
  Future<List<Food>> _filterFoodsForSpecialConditions(AdvancedUserProfile profile) async {
    // Ottieni tutti gli alimenti dal database
    final allFoods = await _databaseService.getAllFoods();

    // Filtra in base alle intolleranze alimentari
    List<Food> filteredFoods = allFoods.where((food) {
      // Verifica che l'alimento non contenga intolleranze dell'utente
      for (String intolerance in profile.foodIntolerances) {
        if (food.allergens.contains(intolerance.toLowerCase()) ||
            food.tags.contains(intolerance.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Filtra in base alle condizioni mediche
    if (profile.medicalConditions.contains(MedicalCondition.diabetes)) {
      // Per diabete, filtra alimenti con alto indice glicemico
      filteredFoods = filteredFoods.where((food) => food.glycemicIndex < 55).toList();
    }

    if (profile.medicalConditions.contains(MedicalCondition.hypertension)) {
      // Per ipertensione, filtra alimenti con alto contenuto di sodio
      filteredFoods = filteredFoods.where((food) {
        final sodium = food.micronutrients['sodium'] ?? 0.0;
        return sodium < 400.0; // meno di 400mg di sodio per 100g
      }).toList();
    }

    if (profile.medicalConditions.contains(MedicalCondition.kidneyDisease)) {
      // Per malattie renali, filtra alimenti con alto contenuto di potassio e fosforo
      filteredFoods = filteredFoods.where((food) {
        final potassium = food.micronutrients['potassium'] ?? 0.0;
        final phosphorus = food.micronutrients['phosphorus'] ?? 0.0;
        return potassium < 300.0 && phosphorus < 150.0;
      }).toList();
    }

    if (profile.medicalConditions.contains(MedicalCondition.gout)) {
      // Per gotta, filtra alimenti con alto contenuto di purine
      filteredFoods = filteredFoods.where((food) {
        if (food.advancedProperties.containsKey(AdvancedNutritionProperties.PURINE_CONTENT)) {
          final purineContent = food.advancedProperties[AdvancedNutritionProperties.PURINE_CONTENT];
          return purineContent < 100.0; // meno di 100mg di purine per 100g
        }
        // Se non abbiamo dati sulle purine, escludiamo alimenti noti per essere ricchi di purine
        return !food.tags.contains('high_purine') &&
               !food.tags.contains('organ_meat') &&
               !food.tags.contains('shellfish');
      }).toList();
    }

    if (profile.medicalConditions.contains(MedicalCondition.ibs)) {
      // Per IBS, filtra alimenti con alto contenuto di FODMAP
      filteredFoods = filteredFoods.where((food) {
        if (food.advancedProperties.containsKey(AdvancedNutritionProperties.FODMAP_LEVEL)) {
          final fodmapLevel = food.advancedProperties[AdvancedNutritionProperties.FODMAP_LEVEL];
          return fodmapLevel != 'Alto';
        }
        return true;
      }).toList();
    }

    // Adatta in base all'obiettivo di fitness
    if (profile.fitnessGoal == FitnessGoal.muscleGain) {
      // Priorità ad alimenti ricchi di proteine
      filteredFoods.sort((a, b) => b.proteins.compareTo(a.proteins));
    } else if (profile.fitnessGoal == FitnessGoal.endurance) {
      // Priorità ad alimenti ricchi di carboidrati complessi
      filteredFoods.sort((a, b) {
        // Calcola il rapporto carboidrati/zuccheri (più alto = più carboidrati complessi)
        double ratioA = a.sugar > 0 ? a.carbs / a.sugar : a.carbs;
        double ratioB = b.sugar > 0 ? b.carbs / b.sugar : b.carbs;
        return ratioB.compareTo(ratioA);
      });
    }

    // FILTRO DI SICUREZZA ALIMENTARE - CRITICO!
    // Rimuovi tutti gli alimenti non sicuri (es. pollo crudo, spinaci crudi, ecc.)
    final safeFoods = FoodSafetyService.filterSafeFoods(filteredFoods);

    print('Alimenti sicuri dopo filtro di sicurezza: ${safeFoods.length}');

    // Se non ci sono alimenti sicuri, prova a convertire quelli non sicuri in versioni cotte
    if (safeFoods.isEmpty && filteredFoods.isNotEmpty) {
      print('ATTENZIONE: Nessun alimento sicuro trovato, tentativo di conversione in versioni cotte...');
      final convertedFoods = <Food>[];
      for (var food in filteredFoods) {
        if (FoodSafetyService.requiresCooking(food)) {
          final cookedVersion = FoodSafetyService.getCookedVersion(food);
          if (cookedVersion != null) {
            convertedFoods.add(cookedVersion);
          }
        } else {
          convertedFoods.add(food);
        }
      }

      print('Alimenti convertiti in versioni sicure: ${convertedFoods.length}');
      return convertedFoods;
    }

    return safeFoods;
  }

  /// Genera un pasto avanzato con alimenti adatti alle condizioni speciali
  Future<PlannedMeal> _generateAdvancedMeal(
    AdvancedUserProfile profile,
    String mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    String time,
    List<Food> availableFoods,
    Map<String, int>? usedFoodsCount,
  ) async {
    // Raggruppa gli alimenti per categoria
    final foodsByCategory = _groupFoodsByCategory(availableFoods);

    // Definisci le categorie di alimenti da includere in base al tipo di pasto
    final categoriesToInclude = _getCategoriesForMealType(mealType);

    // Seleziona gli alimenti con porzioni precise
    final selectedFoods = _selectFoodsWithPrecisePortions(
      foodsByCategory,
      categoriesToInclude,
      targetCalories,
      targetMacros,
      mealType,
      usedFoodsCount,
      profile,
    );

    // Crea il pasto
    return PlannedMeal(
      id: _uuid.v4(),
      name: _getMealName(mealType),
      type: mealType,
      foods: selectedFoods,
      time: time,
    );
  }

  /// Seleziona gli alimenti con porzioni precise per rispettare i target nutrizionali
  List<FoodPortion> _selectFoodsWithPrecisePortions(
    Map<FoodCategory, List<Food>> foodsByCategory,
    List<FoodCategory> categoriesToInclude,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
    Map<String, int>? usedFoodsCount,
    AdvancedUserProfile profile,
  ) {
    // Utilizza il selettore avanzato di alimenti
    return AdvancedFoodSelector.selectFoodsForMeal(
      profile: profile,
      mealType: mealType,
      targetCalories: targetCalories,
      targetMacros: targetMacros,
      availableFoods: _flattenFoodsByCategory(foodsByCategory),
      foodsByCategory: foodsByCategory,
      categoriesToInclude: categoriesToInclude,
      usedFoodsCount: usedFoodsCount,
    );
  }

  /// Appiattisce la mappa di alimenti per categoria in una lista
  List<Food> _flattenFoodsByCategory(Map<FoodCategory, List<Food>> foodsByCategory) {
    final result = <Food>[];
    final seenIds = <String>{};

    for (final foods in foodsByCategory.values) {
      for (final food in foods) {
        if (!seenIds.contains(food.id)) {
          result.add(food);
          seenIds.add(food.id);
        }
      }
    }

    return result;
  }

  /// Distribuisci le calorie tra i pasti in base alla distribuzione personalizzata
  List<int> _distributeMealCaloriesAdvanced(
    int totalCalories,
    int mealsPerDay,
    Map<String, double> mealDistribution,
    int dayOfWeek,
  ) {
    final result = <int>[];

    // Ottieni i tipi di pasto per questo numero di pasti
    final mealTypes = <String>[];
    for (int i = 0; i < mealsPerDay; i++) {
      mealTypes.add(_getMealType(i, mealsPerDay));
    }

    // Calcola le calorie per ogni pasto in base alla distribuzione
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = mealTypes[i];
      final percentage = mealDistribution[mealType] ?? (1.0 / mealsPerDay);

      // Aggiungi variazione in base al giorno della settimana (±5%)
      double variation = 0.0;
      if (dayOfWeek == 5 || dayOfWeek == 6) { // Weekend
        // Colazione più abbondante nel weekend
        if (mealType == 'breakfast') {
          variation = 0.05;
        }
        // Cena più abbondante nel weekend
        else if (mealType == 'dinner') {
          variation = 0.05;
        }
      } else { // Giorni feriali
        // Pranzo più abbondante nei giorni feriali
        if (mealType == 'lunch') {
          variation = 0.05;
        }
      }

      // Calcola le calorie per questo pasto
      final adjustedPercentage = percentage * (1 + variation);
      final calories = (totalCalories * adjustedPercentage).round();

      result.add(calories);
    }

    // Assicurati che la somma sia uguale al totale
    int sum = result.fold(0, (sum, calories) => sum + calories);
    if (sum != totalCalories) {
      // Aggiusta l'ultimo pasto per compensare
      result[result.length - 1] += (totalCalories - sum);
    }

    return result;
  }

  /// Distribuisci i macronutrienti tra i pasti in base alle esigenze specifiche
  List<Map<String, int>> _distributeMealMacrosAdvanced(
    Map<String, int> macroTargets,
    int mealsPerDay,
    List<int> mealCalorieDistribution,
    AdvancedUserProfile profile,
  ) {
    final result = <Map<String, int>>[];

    // Ottieni i tipi di pasto per questo numero di pasti
    final mealTypes = <String>[];
    for (int i = 0; i < mealsPerDay; i++) {
      mealTypes.add(_getMealType(i, mealsPerDay));
    }

    // Calcola il totale delle calorie
    final totalCalories = mealCalorieDistribution.fold(0, (sum, calories) => sum + calories);

    // Ottieni i target di macronutrienti
    final targetProteins = macroTargets['proteins'] ?? 0;
    final targetCarbs = macroTargets['carbs'] ?? 0;
    final targetFats = macroTargets['fats'] ?? 0;

    // Distribuisci i macronutrienti in base al tipo di pasto e alle esigenze specifiche
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = mealTypes[i];
      final mealCalories = mealCalorieDistribution[i];
      final caloriePercentage = mealCalories / totalCalories;

      // Distribuzione base proporzionale alle calorie
      int proteinGrams = (targetProteins * caloriePercentage).round();
      int carbGrams = (targetCarbs * caloriePercentage).round();
      int fatGrams = (targetFats * caloriePercentage).round();

      // Aggiusta in base al tipo di pasto
      if (mealType == 'breakfast') {
        // Colazione: più carboidrati, meno grassi
        carbGrams = (carbGrams * 1.2).round();
        fatGrams = (fatGrams * 0.8).round();
      } else if (mealType == 'lunch') {
        // Pranzo: distribuzione bilanciata
      } else if (mealType == 'dinner') {
        // Cena: più proteine, meno carboidrati
        proteinGrams = (proteinGrams * 1.2).round();
        carbGrams = (carbGrams * 0.8).round();
      } else if (mealType == 'snack') {
        // Spuntino: più proteine per sazietà
        proteinGrams = (proteinGrams * 1.1).round();
      }

      // Aggiusta in base all'obiettivo di fitness
      if (profile.fitnessGoal == FitnessGoal.muscleGain) {
        // Per aumento massa: più proteine dopo l'allenamento
        if (mealType == 'dinner' || mealType == 'lunch') {
          proteinGrams = (proteinGrams * 1.1).round();
        }
      } else if (profile.fitnessGoal == FitnessGoal.endurance) {
        // Per resistenza: più carboidrati prima dell'allenamento
        if (mealType == 'breakfast' || mealType == 'lunch') {
          carbGrams = (carbGrams * 1.1).round();
        }
      }

      // Aggiusta in base alle condizioni mediche
      if (profile.medicalConditions.contains(MedicalCondition.diabetes)) {
        // Per diabete: distribuzione più uniforme dei carboidrati
        carbGrams = (targetCarbs * (1.0 / mealsPerDay)).round();
      }

      // Aggiungi i macronutrienti per questo pasto
      result.add({
        'proteins': proteinGrams,
        'carbs': carbGrams,
        'fats': fatGrams,
      });
    }

    // Assicurati che la somma sia uguale ai target
    int sumProteins = result.fold(0, (sum, macros) => sum + (macros['proteins'] ?? 0));
    int sumCarbs = result.fold(0, (sum, macros) => sum + (macros['carbs'] ?? 0));
    int sumFats = result.fold(0, (sum, macros) => sum + (macros['fats'] ?? 0));

    // Aggiusta l'ultimo pasto per compensare
    if (sumProteins != targetProteins) {
      result[result.length - 1]['proteins'] = (result[result.length - 1]['proteins'] ?? 0) + (targetProteins - sumProteins);
    }
    if (sumCarbs != targetCarbs) {
      result[result.length - 1]['carbs'] = (result[result.length - 1]['carbs'] ?? 0) + (targetCarbs - sumCarbs);
    }
    if (sumFats != targetFats) {
      result[result.length - 1]['fats'] = (result[result.length - 1]['fats'] ?? 0) + (targetFats - sumFats);
    }

    return result;
  }

  /// Ottieni orari dei pasti adattati al timing preferito
  List<String> _getAdjustedMealTimes(int mealsPerDay, MealTiming mealTiming) {
    // Orari predefiniti per i diversi timing
    final Map<MealTiming, Map<int, List<String>>> timingMap = {
      MealTiming.standard: {
        3: ['08:00', '13:00', '20:00'],
        4: ['08:00', '12:00', '16:00', '20:00'],
        5: ['08:00', '11:00', '13:30', '17:00', '20:00'],
        6: ['08:00', '10:30', '13:00', '16:00', '18:30', '21:00'],
      },
      MealTiming.earlyBird: {
        3: ['07:00', '12:00', '19:00'],
        4: ['07:00', '10:30', '14:00', '19:00'],
        5: ['07:00', '10:00', '13:00', '16:00', '19:00'],
        6: ['07:00', '09:30', '12:00', '15:00', '17:30', '20:00'],
      },
      MealTiming.nightOwl: {
        3: ['09:00', '14:00', '21:00'],
        4: ['09:00', '13:00', '17:00', '21:00'],
        5: ['09:00', '12:00', '15:00', '18:00', '21:00'],
        6: ['09:00', '11:30', '14:00', '17:00', '19:30', '22:00'],
      },
      MealTiming.intermittentFasting: {
        2: ['13:00', '19:00'],
        3: ['13:00', '16:00', '19:00'],
        4: ['13:00', '15:00', '17:00', '19:00'],
      },
      MealTiming.frequentSmallMeals: {
        5: ['08:00', '11:00', '14:00', '17:00', '20:00'],
        6: ['08:00', '10:30', '13:00', '15:30', '18:00', '20:30'],
      },
    };

    // Ottieni gli orari per il timing e il numero di pasti specificati
    if (timingMap.containsKey(mealTiming) &&
        timingMap[mealTiming]!.containsKey(mealsPerDay)) {
      return timingMap[mealTiming]![mealsPerDay]!;
    }

    // Fallback: genera orari equidistanti
    final startHour = mealTiming == MealTiming.earlyBird ? 7 :
                      mealTiming == MealTiming.nightOwl ? 9 :
                      mealTiming == MealTiming.intermittentFasting ? 13 : 8;

    final endHour = mealTiming == MealTiming.earlyBird ? 19 :
                    mealTiming == MealTiming.nightOwl ? 21 :
                    mealTiming == MealTiming.intermittentFasting ? 19 : 20;

    final totalHours = endHour - startHour;
    final hoursBetweenMeals = totalHours / (mealsPerDay - 1);

    return List.generate(mealsPerDay, (index) {
      final hour = (startHour + index * hoursBetweenMeals).floor();
      final minute = ((startHour + index * hoursBetweenMeals) % 1 * 60).round();
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    });
  }

  /// Verifica e correggi il piano giornaliero per garantire la precisione
  DailyDietPlan _validateAndCorrectDailyPlan(DailyDietPlan plan, UserProfile userProfile) {
    // Calcola i totali attuali
    int totalCalories = 0;
    int totalProteins = 0;
    int totalCarbs = 0;
    int totalFats = 0;

    for (final meal in plan.meals) {
      totalCalories += meal.totalCalories;
      totalProteins += meal.getMacroValue('proteins').round();
      totalCarbs += meal.getMacroValue('carbs').round();
      totalFats += meal.getMacroValue('fats').round();
    }

    // Verifica se i totali sono entro le tolleranze
    final calorieTarget = plan.calorieTarget;
    final proteinTarget = plan.macroTargets['proteins'] ?? 0;
    final carbTarget = plan.macroTargets['carbs'] ?? 0;
    final fatTarget = plan.macroTargets['fats'] ?? 0;

    // Calcola le deviazioni
    final calorieDiff = totalCalories - calorieTarget;
    final proteinDiff = totalProteins - proteinTarget;
    final carbDiff = totalCarbs - carbTarget;
    final fatDiff = totalFats - fatTarget;

    // Verifica se le deviazioni sono accettabili
    final calorieDeviation = (calorieDiff.abs() / calorieTarget) * 100;
    final proteinDeviation = proteinTarget > 0 ? (proteinDiff.abs() / proteinTarget) * 100 : 0;
    final carbDeviation = carbTarget > 0 ? (carbDiff.abs() / carbTarget) * 100 : 0;
    final fatDeviation = fatTarget > 0 ? (fatDiff.abs() / fatTarget) * 100 : 0;

    print('Deviazioni: Calorie ${calorieDeviation.toStringAsFixed(1)}%, Proteine ${proteinDeviation.toStringAsFixed(1)}%, Carboidrati ${carbDeviation.toStringAsFixed(1)}%, Grassi ${fatDeviation.toStringAsFixed(1)}%');

    // Se le deviazioni sono accettabili, restituisci il piano invariato
    if (calorieDeviation <= 3.0 && proteinDeviation <= 5.0 && carbDeviation <= 5.0 && fatDeviation <= 5.0) {
      return plan;
    }

    // Altrimenti, correggi il piano
    print('Correzione del piano dietetico per migliorare la precisione...');

    // Implementazione della correzione
    // ...

    // Per ora, restituisci il piano originale
    return plan;
  }

  /// Raggruppa gli alimenti per categoria
  Map<FoodCategory, List<Food>> _groupFoodsByCategory(List<Food> foods) {
    final result = <FoodCategory, List<Food>>{};

    for (final category in FoodCategory.values) {
      result[category] = [];
    }

    for (final food in foods) {
      for (final category in food.categories) {
        result[category]!.add(food);
      }
    }

    return result;
  }

  /// Ottieni le categorie di alimenti da includere in un pasto
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    if (mealType == 'breakfast') {
      return [
        FoodCategory.grain,
        FoodCategory.dairy,
        FoodCategory.fruit,
      ];
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
        FoodCategory.fat,
      ];
    } else if (mealType == 'snack') {
      return [
        FoodCategory.fruit,
        FoodCategory.dairy,
        FoodCategory.protein,
      ];
    } else {
      return [
        FoodCategory.protein,
        FoodCategory.grain,
        FoodCategory.vegetable,
      ];
    }
  }

  /// Ottieni il tipo di pasto in base all'indice
  String _getMealType(int index, int mealsPerDay) {
    if (index == 0) return 'breakfast';
    if (index == mealsPerDay - 1) return 'dinner';
    if (index == 1 && mealsPerDay > 3) return 'snack';
    if (index == mealsPerDay - 2 && mealsPerDay > 3) return 'snack';
    return index < mealsPerDay / 2 ? 'lunch' : 'snack';
  }

  /// Ottieni il nome del pasto in base al tipo
  String _getMealName(String mealType) {
    if (mealType == 'breakfast') {
      return 'Colazione';
    } else if (mealType == 'lunch') {
      return 'Pranzo';
    } else if (mealType == 'dinner') {
      return 'Cena';
    } else if (mealType == 'snack') {
      return 'Spuntino';
    } else {
      return 'Pasto';
    }
  }

  /// Verifica e correggi il piano giornaliero per garantire la precisione


  /// Ottieni l'inizio della settimana (lunedì) per una data
  DateTime _getStartOfWeek(DateTime date) {
    final dayOfWeek = date.weekday;
    return date.subtract(Duration(days: dayOfWeek - 1));
  }

  /// Formatta una data come stringa
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
