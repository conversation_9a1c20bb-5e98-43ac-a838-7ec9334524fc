import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/dr_staffilano_theme.dart';
import '../utils/image_placeholder_helper.dart';
import '../controllers/welljourney_controller.dart';
import '../services/supabase_auth_service.dart';
import '../models/supabase_models.dart';

/// Widget per visualizzare un'intestazione con saluto personalizzato all'utente
class UserGreetingHeader extends StatefulWidget {
  final String? userName; // Ora opzionale, verrà caricato dal database
  final String? userAvatarUrl;
  final VoidCallback? onAvatarTap;

  const UserGreetingHeader({
    Key? key,
    this.userName,
    this.userAvatarUrl,
    this.onAvatarTap,
  }) : super(key: key);

  @override
  State<UserGreetingHeader> createState() => _UserGreetingHeaderState();
}

class _UserGreetingHeaderState extends State<UserGreetingHeader> {
  SupabaseProfile? _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  /// Carica il profilo utente dal database
  Future<void> _loadUserProfile() async {
    try {
      final authService = SupabaseAuthService();
      final profile = await authService.getCurrentUserProfile();

      if (mounted) {
        setState(() {
          _userProfile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ UserGreetingHeader: Errore caricamento profilo: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Ottieni il nome da mostrare
  String get _displayName {
    if (widget.userName != null && widget.userName!.isNotEmpty) {
      return widget.userName!;
    }

    if (_userProfile?.nome != null && _userProfile!.nome!.isNotEmpty) {
      return _userProfile!.nome!;
    }

    return 'Utente';
  }

  /// Ottieni l'URL dell'avatar
  String? get _displayAvatarUrl {
    return widget.userAvatarUrl ?? _userProfile?.fotoProfiloUrl;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Ciao,',
                  style: const TextStyle(
                    fontSize: 16,
                    color: DrStaffilanoTheme.textOnPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                _isLoading
                  ? Container(
                      width: 120,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    )
                  : Text(
                      _displayName,
                      style: const TextStyle(
                        fontSize: 24,
                        color: DrStaffilanoTheme.textOnPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Dr. Staffilano Nutrition',
                        style: TextStyle(
                          fontSize: 12,
                          color: DrStaffilanoTheme.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Consumer<WellJourneyController>(
                      builder: (context, controller, child) {
                        final points = controller.userProgress?.totalPoints ?? 0;
                        final level = controller.userProgress?.level ?? 1;

                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.stars_rounded,
                                color: DrStaffilanoTheme.textOnPrimary,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '$points',
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: DrStaffilanoTheme.textOnPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: widget.onAvatarTap,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: DrStaffilanoTheme.backgroundWhite,
                border: Border.all(
                  color: DrStaffilanoTheme.accentGold,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: DrStaffilanoTheme.shadowMedium,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: _isLoading
                  ? const CircularProgressIndicator(
                      color: DrStaffilanoTheme.primaryGreen,
                      strokeWidth: 2,
                    )
                  : _displayAvatarUrl != null
                    ? ClipOval(
                        child: Image.network(
                          _displayAvatarUrl!,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Text(
                              _displayName.isNotEmpty ? _displayName[0].toUpperCase() : 'U',
                              style: const TextStyle(
                                fontSize: 24,
                                color: DrStaffilanoTheme.primaryGreen,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      )
                    : Text(
                        _displayName.isNotEmpty ? _displayName[0].toUpperCase() : 'U',
                        style: const TextStyle(
                          fontSize: 24,
                          color: DrStaffilanoTheme.primaryGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
