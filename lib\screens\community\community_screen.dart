import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_post.dart';
import '../../models/community_stats.dart';
import '../../models/community_user.dart';
import '../../models/community_notification.dart';
import '../../services/community_service.dart';
import '../../services/notification_service.dart';
import '../../services/community_profile_service.dart';
import '../../services/friendship_service.dart';
import '../../widgets/common/collapsing_app_bar.dart';
import '../../widgets/community/facebook_style_post_modal.dart';
import '../../widgets/notification_icon_with_badge.dart';
import '../../widgets/responsive_layout.dart';
import '../../widgets/responsive_text.dart';
import '../../widgets/responsive_button.dart';
import '../../utils/notification_test_helper.dart';
import '../../utils/responsive_utils.dart';
import '../../theme/dr_staffilano_theme.dart';
import 'widgets/community_feed_widget.dart';
import 'user_profile_screen.dart';
import 'friends_screen.dart';

/// Schermata principale della Staffilano InnerCircle™
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  final CommunityService _communityService = CommunityService.instance;
  bool _isDisposed = false;

  // Dati della community
  List<CommunityPost> _feedPosts = [];
  CommunityStats _stats = CommunityStats(
    totalMembers: 12500,
    totalPosts: 3200,
    activeChallenges: 24,
    totalGroups: 156,
  );
  bool _isLoading = true;

  // Current user for post creation
  late CommunityUser _currentUser;

  @override
  void initState() {
    super.initState();
    _initializeCurrentUser();
    _loadCommunityData();
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  /// Inizializza l'utente corrente
  void _initializeCurrentUser() {
    // TODO: Get from authentication service
    _currentUser = CommunityUser(
      id: 'current_user_id',
      displayName: 'Dr. Giovanni Staffilano',
      username: 'dr.staffilano',
      avatarUrl: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
      bio: 'Cardiologo specializzato in prevenzione e benessere',
      joinDate: DateTime.now().subtract(const Duration(days: 365)),
      lastActive: DateTime.now(),
      isVerified: true,
      membershipLevel: MembershipLevel.ambassador,
      communityPoints: 12500,
      followersCount: 1250,
      followingCount: 89,
      totalPosts: 156,
      totalLikes: 3420,
      badges: ['Cardiologo Verificato', 'Mentore Premium', 'Pioniere Community'],
      interests: ['Cardiologia', 'Prevenzione', 'Nutrizione', 'Benessere'],
      isMentor: true,
    );
  }

  /// Carica i dati della community
  Future<void> _loadCommunityData() async {
    if (_isDisposed) return;

    try {
      if (mounted && !_isDisposed) {
        setState(() => _isLoading = true);
      }

      // Carica solo i post del feed per il layout Facebook-like
      // Per ora usiamo l'ID di Dr. Staffilano come utente corrente per i test
      final currentUser = await _communityService.getUsers(limit: 1);
      final currentUserId = currentUser.isNotEmpty ? currentUser.first.id : null;
      final feedPosts = await _communityService.getFeedPosts(limit: 30, currentUserId: currentUserId);

      if (mounted && !_isDisposed) {
        setState(() {
          _feedPosts = feedPosts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted && !_isDisposed) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nel caricamento: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return DefaultTabController(
      length: 4,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            _buildSliverAppBar(),
          ],
          body: _buildTabBarView(),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _createNewPost(),
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  /// Costruisce la SliverAppBar con le tab
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: DrStaffilanoTheme.primaryGreen,
      title: const Text(
        'Staffilano InnerCircle™',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      centerTitle: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
              ],
            ),
          ),
          child: _buildHeaderContent(),
        ),
      ),
      bottom: _buildTabBar(),
      actions: [
        const NotificationIconWithBadge(
          iconSize: 24,
          iconColor: Colors.white,
        ),
        Consumer<CommunityProfileService>(
          builder: (context, profileService, child) {
            return IconButton(
              onPressed: () => _showCurrentUserProfile(),
              icon: CircleAvatar(
                radius: 12,
                backgroundColor: Colors.white,
                backgroundImage: profileService.currentUser?.avatarUrl != null
                    ? NetworkImage(profileService.currentUser!.avatarUrl!)
                    : null,
                child: profileService.currentUser?.avatarUrl == null
                    ? Text(
                        profileService.currentUser?.displayName.isNotEmpty == true
                            ? profileService.currentUser!.displayName[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: DrStaffilanoTheme.primaryGreen,
                        ),
                      )
                    : null,
              ),
              tooltip: 'Il mio profilo',
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showSearch(),
        ),
      ],
    );
  }

  /// Costruisce il contenuto dell'header
  Widget _buildHeaderContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 70, 20, 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Statistiche compatte con migliore leggibilità
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildReadableStat('12.5K', 'Membri', Icons.people),
              _buildReadableStat('3.2K', 'Post', Icons.article),
              _buildReadableStat('24', 'Sfide', Icons.emoji_events),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce una statistica leggibile e compatta con design responsive
  Widget _buildReadableStat(String value, String label, IconData icon) {
    return ResponsiveContainer(
      padding: ResponsiveUtils.getResponsivePadding(context,
        mobile: 8, tablet: 12, desktop: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context, 8)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: ResponsiveUtils.getResponsiveIconSize(context, 14),
              ),
              SizedBox(width: ResponsiveUtils.isMobile(context) ? 6 : 8),
              ResponsiveText(
                value,
                baseFontSize: 13,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                style: const TextStyle(
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          ResponsiveText(
            label,
            baseFontSize: 9,
            fontWeight: FontWeight.w500,
            color: Colors.white,
            style: const TextStyle(
              shadows: [
                Shadow(
                  offset: Offset(0, 1),
                  blurRadius: 1,
                  color: Colors.black26,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la TabBar
  PreferredSizeWidget _buildTabBar() {
    return const TabBar(
      indicatorColor: Colors.white,
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white70,
      labelStyle: TextStyle(fontWeight: FontWeight.w600),
      tabs: [
        Tab(text: 'Feed', icon: Icon(Icons.home, size: 20)),
        Tab(text: 'Amici', icon: Icon(Icons.people, size: 20)),
        Tab(text: 'Profilo', icon: Icon(Icons.person, size: 20)),
        Tab(text: 'Gruppi', icon: Icon(Icons.group, size: 20)),
      ],
    );
  }

  /// Costruisce il contenuto delle tab
  Widget _buildTabBarView() {
    return TabBarView(
      children: [
        // Tab Feed
        _buildFeedTab(),

        // Tab Amici
        _buildFriendsTab(),

        // Tab Profilo
        _buildProfileTab(),

        // Tab Gruppi (placeholder)
        _buildGroupsTab(),
      ],
    );
  }

  /// Costruisce la tab del feed
  Widget _buildFeedTab() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    try {
      return CommunityFeedWidget(posts: _feedPosts);
    } catch (e) {
      return _buildErrorState('Errore nel caricamento del feed', e.toString());
    }
  }

  /// Costruisce la tab degli amici
  Widget _buildFriendsTab() {
    try {
      return const FriendsScreen();
    } catch (e) {
      return _buildErrorState('Errore nel caricamento amici', e.toString());
    }
  }

  /// Costruisce la tab del profilo
  Widget _buildProfileTab() {
    try {
      return const UserProfileScreen(isCurrentUser: true);
    } catch (e) {
      return _buildErrorState('Errore nel caricamento profilo', e.toString());
    }
  }

  /// Costruisce la tab dei gruppi
  Widget _buildGroupsTab() {
    return _buildComingSoonTab('Gruppi', 'La gestione dei gruppi sarà disponibile presto!');
  }

  /// Costruisce uno stato di errore
  Widget _buildErrorState(String title, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Si è verificato un errore. Riprova più tardi.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _loadCommunityData();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: const Text('Riprova'),
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce una tab "Coming Soon"
  Widget _buildComingSoonTab(String title, String description) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  /// Mostra il profilo dell'utente corrente
  void _showCurrentUserProfile() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const UserProfileScreen(isCurrentUser: true),
      ),
    );
  }

  /// Costruisce lo stato di caricamento
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Caricamento community...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }



  /// Crea notifiche di test per dimostrare il sistema
  void _createTestNotifications() async {
    try {
      final notificationService = context.read<NotificationService>();
      await NotificationTestHelper.createTestNotifications(notificationService);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('🧪 Notifiche di test create!'),
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Mostra la ricerca
  void _showSearch() {
    showSearch(
      context: context,
      delegate: CommunitySearchDelegate(),
    );
  }

  /// Crea un nuovo post con modal Facebook-style
  void _createNewPost() {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.transparent,
      builder: (context) => FacebookStylePostModal(
        currentUser: _currentUser,
        onPostCreated: (post) {
          // Aggiungi il nuovo post al feed
          setState(() {
            _feedPosts.insert(0, post.copyWith(author: _currentUser));
          });

          // Mostra messaggio di successo
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Post pubblicato con successo!'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Delegate per la ricerca nella community
class CommunitySearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return const Center(
      child: Text('Risultati della ricerca'),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return const Center(
      child: Text('Suggerimenti di ricerca'),
    );
  }
}
