import 'dart:math';
import 'dart:collection';
import 'package:uuid/uuid.dart';
import '../interfaces/ai_interface.dart';
import '../models/ai_models.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../models/diet_plan.dart';
import '../../services/food_database_service.dart';
import '../../services/optimized_diet_generator_service.dart';
import '../../services/storage_service.dart';
import '../../constants/app_constants.dart';

/// Implementazione avanzata del sistema di intelligenza artificiale "Dr. Staffilano AI Nutritionist™"
/// Classe di supporto per gli alimenti con punteggio
class _ScoredFood {
  final Food food;
  final double score;

  _ScoredFood({
    required this.food,
    required this.score,
  });
}

class StaffilanoAIImplementation implements AIInterface {
  final FoodDatabaseService _foodDatabaseService;
  final StorageService _storageService;
  final Uuid _uuid = Uuid();

  // Cache delle preferenze apprese
  final Map<String, List<LearnedPreference>> _userPreferencesCache = {};

  // Cache dei pattern alimentari
  final Map<String, Map<String, dynamic>> _userPatternsCache = {};

  // Stato di inizializzazione
  bool _isInitialized = false;

  StaffilanoAIImplementation({
    required FoodDatabaseService foodDatabaseService,
    required StorageService storageService,
  }) :
    _foodDatabaseService = foodDatabaseService,
    _storageService = storageService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Carica le preferenze degli utenti
    await _loadUserPreferences();

    // Carica i pattern alimentari
    await _loadUserPatterns();

    _isInitialized = true;
  }

  /// Carica le preferenze degli utenti dal database
  Future<void> _loadUserPreferences() async {
    // In una implementazione reale, qui caricheremmo le preferenze dal database
    // Per ora, utilizziamo dati di esempio
    _userPreferencesCache.clear();

    // Esempio di preferenze per un utente di test
    const String testUserId = 'test_user_1';
    _userPreferencesCache[testUserId] = [
      LearnedPreference(
        id: '1',
        userId: testUserId,
        foodId: 'apple',
        preferenceScore: 0.8,
        updatedAt: DateTime.now(),
      ),
      LearnedPreference(
        id: '2',
        userId: testUserId,
        foodId: 'spinach',
        preferenceScore: 0.6,
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// Carica i pattern alimentari degli utenti
  Future<void> _loadUserPatterns() async {
    // In una implementazione reale, qui caricheremmo i pattern dal database
    // Per ora, utilizziamo dati di esempio
    _userPatternsCache.clear();

    // Esempio di pattern per un utente di test
    const String testUserId = 'test_user_1';
    _userPatternsCache[testUserId] = {
      'preferredMealTimes': {
        'breakfast': '07:30',
        'lunch': '13:00',
        'dinner': '20:00',
      },
      'frequentFoods': [
        'apple',
        'chicken_breast',
        'brown_rice',
      ],
      'avoidedFoods': [
        'processed_meat',
        'sugary_drinks',
      ],
      'weekdayPatterns': {
        'monday': ['high_protein', 'low_carb'],
        'friday': ['treat_meal'],
        'weekend': ['relaxed_diet'],
      },
    };
  }

  @override
  Future<void> train(AILearningContext context) async {
    // Aggiorna le preferenze apprese in base ai feedback
    for (var feedback in context.feedbacks) {
      if (feedback.foodId != null) {
        await _updateFoodPreference(
          feedback.userId,
          feedback.foodId!,
          _convertRatingToPreferenceScore(feedback.rating)
        );
      }
    }

    // Analizza i pattern alimentari
    await _analyzeUserPatterns(context);
  }

  /// Analizza i pattern alimentari dell'utente
  Future<void> _analyzeUserPatterns(AILearningContext context) async {
    final userId = context.userProfile.id;

    // Inizializza il pattern se non esiste
    if (!_userPatternsCache.containsKey(userId)) {
      _userPatternsCache[userId] = {
        'preferredMealTimes': {},
        'frequentFoods': [],
        'avoidedFoods': [],
        'weekdayPatterns': {},
      };
    }

    // Analizza gli orari dei pasti
    final mealTimes = <String, List<String>>{};
    for (var meal in context.recentMeals) {
      // Assumiamo che meal.time sia una stringa nel formato "HH:MM"
      final timeStr = meal.time;

      if (!mealTimes.containsKey(meal.type)) {
        mealTimes[meal.type] = [];
      }
      mealTimes[meal.type]!.add(timeStr);
    }

    // Calcola gli orari medi dei pasti
    for (var entry in mealTimes.entries) {
      final times = entry.value;
      if (times.isNotEmpty) {
        // Calcola l'orario medio (semplificato)
        final avgTime = times[times.length ~/ 2]; // Mediana
        _userPatternsCache[userId]!['preferredMealTimes'][entry.key] = avgTime;
      }
    }

    // Identifica gli alimenti frequenti
    final foodCounts = <String, int>{};
    for (var meal in context.recentMeals) {
      for (var foodPortion in meal.foods) {
        final foodId = foodPortion.food.id;
        foodCounts[foodId] = (foodCounts[foodId] ?? 0) + 1;
      }
    }

    // Ordina per frequenza
    final sortedFoods = foodCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Aggiorna gli alimenti frequenti
    _userPatternsCache[userId]!['frequentFoods'] =
      sortedFoods.take(10).map((e) => e.key).toList();

    // Identifica gli alimenti evitati (quelli con feedback negativi)
    final avoidedFoods = <String>[];
    for (var pref in context.preferences) {
      if (pref.preferenceScore < -0.5) {
        avoidedFoods.add(pref.foodId);
      }
    }

    _userPatternsCache[userId]!['avoidedFoods'] = avoidedFoods;
  }

  double _convertRatingToPreferenceScore(int rating) {
    // Converte un rating 1-5 in un punteggio di preferenza -1.0 a 1.0
    return (rating - 3) / 2;
  }

  /// Aggiorna la preferenza per un alimento
  Future<void> _updateFoodPreference(String userId, String foodId, double score) async {
    // Inizializza la lista di preferenze per l'utente se non esiste
    if (!_userPreferencesCache.containsKey(userId)) {
      _userPreferencesCache[userId] = [];
    }

    // Cerca una preferenza esistente
    final existingIndex = _userPreferencesCache[userId]!.indexWhere(
      (pref) => pref.foodId == foodId
    );

    if (existingIndex >= 0) {
      // Aggiorna la preferenza esistente
      final existing = _userPreferencesCache[userId]![existingIndex];
      final updatedScore = (existing.preferenceScore * 0.7) + (score * 0.3); // Media ponderata

      _userPreferencesCache[userId]![existingIndex] = LearnedPreference(
        id: existing.id,
        userId: userId,
        foodId: foodId,
        preferenceScore: updatedScore,
        updatedAt: DateTime.now(),
      );
    } else {
      // Crea una nuova preferenza
      _userPreferencesCache[userId]!.add(LearnedPreference(
        id: _uuid.v4(),
        userId: userId,
        foodId: foodId,
        preferenceScore: score,
        updatedAt: DateTime.now(),
      ));
    }

    // In una implementazione reale, qui salveremmo le preferenze nel database
  }

  @override
  Future<AIRecommendationResponse> getRecommendations(AIRecommendationRequest request) async {
    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabaseService.getAllFoods();

    // Filtra gli alimenti in base alle preferenze e restrizioni dell'utente
    final filteredFoods = _filterFoodsByUserPreferences(
      allFoods,
      request.userProfile.id,
      request.excludeFoodIds ?? [],
      request.preferredCategories ?? [],
      request.userProfile,
    );

    // Calcola i punteggi di raccomandazione
    final scoredFoods = _scoreFoodsForRecommendation(
      filteredFoods,
      request.userProfile,
      request.mealType,
      request.targetCalories,
      request.targetMacros,
    );

    // Crea le raccomandazioni
    final recommendations = scoredFoods.map((scoredFood) {
      return FoodRecommendation(
        food: scoredFood.food,
        score: scoredFood.score,
        reason: _generateAdvancedRecommendationReason(scoredFood.food, request.userProfile),
      );
    }).toList();

    // Genera un messaggio personalizzato
    final message = _generatePersonalizedMessage(request.userProfile, request.mealType);

    return AIRecommendationResponse(
      recommendedFoods: recommendations,
      message: message,
    );
  }

  /// Filtra gli alimenti in base alle preferenze e restrizioni dell'utente
  List<Food> _filterFoodsByUserPreferences(
    List<Food> foods,
    String userId,
    List<String> excludeFoodIds,
    List<FoodCategory> preferredCategories,
    UserProfile userProfile,
  ) {
    // Rimuovi gli alimenti esclusi esplicitamente
    var filteredFoods = foods.where((food) => !excludeFoodIds.contains(food.id)).toList();

    // Rimuovi gli alimenti che contengono allergeni dell'utente
    if (userProfile.allergies.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        return !food.allergens.any((allergen) =>
          userProfile.allergies.contains(allergen.toLowerCase()));
      }).toList();
    }

    // Filtra in base al tipo di dieta
    switch (userProfile.dietType) {
      case DietType.vegetarian:
        filteredFoods = filteredFoods.where((food) => food.isVegetarian).toList();
        break;
      case DietType.vegan:
        filteredFoods = filteredFoods.where((food) => food.isVegan).toList();
        break;
      case DietType.keto:
        filteredFoods = filteredFoods.where((food) => food.carbs <= 5).toList();
        break;
      case DietType.paleo:
        filteredFoods = filteredFoods.where((food) =>
          !food.categories.contains(FoodCategory.grain) &&
          !food.categories.contains(FoodCategory.dairy) &&
          !food.tags.contains('processed')
        ).toList();
        break;
      case DietType.pescatarian:
        filteredFoods = filteredFoods.where((food) =>
          food.isVegetarian ||
          food.tags.contains('pesce') ||
          food.tags.contains('fish')
        ).toList();
        break;
      default:
        // Nessun filtro speciale per la dieta onnivora
        break;
    }

    // Rimuovi gli alimenti che l'utente non gradisce
    if (userProfile.dislikedFoods.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) =>
        !userProfile.dislikedFoods.contains(food.id)).toList();
    }

    // Rimuovi gli alimenti che l'utente ha valutato negativamente
    final userPreferences = _userPreferencesCache[userId] ?? [];
    final dislikedFoodIds = userPreferences
      .where((pref) => pref.preferenceScore < -0.3)
      .map((pref) => pref.foodId)
      .toList();

    filteredFoods = filteredFoods.where((food) =>
      !dislikedFoodIds.contains(food.id)).toList();

    // Se ci sono categorie preferite, dai priorità a quelle
    if (preferredCategories.isNotEmpty) {
      final preferredFoods = filteredFoods.where((food) =>
        food.categories.any((cat) => preferredCategories.contains(cat))).toList();

      // Se abbiamo abbastanza alimenti nelle categorie preferite, usa solo quelli
      if (preferredFoods.length >= 10) {
        filteredFoods = preferredFoods;
      }
    }

    // Controlla se ci sono pattern alimentari per l'utente
    if (_userPatternsCache.containsKey(userId)) {
      final patterns = _userPatternsCache[userId]!;

      // Rimuovi gli alimenti che l'utente evita abitualmente
      if (patterns.containsKey('avoidedFoods')) {
        final avoidedFoods = patterns['avoidedFoods'] as List<dynamic>;
        filteredFoods = filteredFoods.where((food) =>
          !avoidedFoods.contains(food.id)).toList();
      }
    }

    return filteredFoods;
  }

  /// Calcola i punteggi di raccomandazione per gli alimenti
  List<_ScoredFood> _scoreFoodsForRecommendation(
    List<Food> foods,
    UserProfile userProfile,
    String? mealType,
    int? targetCalories,
    Map<String, int>? targetMacros,
  ) {
    final scoredFoods = <_ScoredFood>[];
    final userPreferences = _userPreferencesCache[userProfile.id] ?? [];

    for (final food in foods) {
      double score = 0.5; // Punteggio base

      // Fattore 1: Adatto al tipo di pasto
      if (mealType != null) {
        final mealTypeEnum = MealType.values.firstWhere(
          (e) => e.toString().split('.').last == mealType,
          orElse: () => MealType.values.first,
        );

        if (food.suitableForMeals.contains(mealTypeEnum)) {
          score += 0.2;
        } else {
          score -= 0.1;
        }
      }

      // Fattore 2: Preferenze dell'utente
      final preference = userPreferences.firstWhere(
        (pref) => pref.foodId == food.id,
        orElse: () => LearnedPreference(
          id: '',
          userId: userProfile.id,
          foodId: food.id,
          preferenceScore: 0.0,
          updatedAt: DateTime.now(),
        ),
      );

      // Aggiungi il punteggio di preferenza (normalizzato da -1.0 a 1.0)
      score += preference.preferenceScore * 0.3;

      // Fattore 3: Allineamento con gli obiettivi nutrizionali
      if (targetCalories != null && targetCalories > 0) {
        // Premia gli alimenti che contribuiscono in modo appropriato alle calorie
        final calorieContribution = food.calories / targetCalories;
        if (calorieContribution <= 0.3) {
          score += 0.1;
        } else if (calorieContribution > 0.5) {
          score -= 0.2; // Penalizza alimenti troppo calorici
        }
      }

      // Fattore 4: Allineamento con i macronutrienti target
      if (targetMacros != null && targetMacros.isNotEmpty) {
        // Calcola quanto l'alimento si allinea con la distribuzione di macronutrienti desiderata
        double macroAlignmentScore = 0.0;

        if (targetMacros.containsKey('proteins') && targetMacros['proteins']! > 0) {
          final proteinRatio = food.proteins / targetMacros['proteins']!;
          macroAlignmentScore += _scoreNutrientRatio(proteinRatio);
        }

        if (targetMacros.containsKey('carbs') && targetMacros['carbs']! > 0) {
          final carbRatio = food.carbs / targetMacros['carbs']!;
          macroAlignmentScore += _scoreNutrientRatio(carbRatio);
        }

        if (targetMacros.containsKey('fats') && targetMacros['fats']! > 0) {
          final fatRatio = food.fats / targetMacros['fats']!;
          macroAlignmentScore += _scoreNutrientRatio(fatRatio);
        }

        // Normalizza e aggiungi al punteggio
        if (targetMacros.length > 0) {
          macroAlignmentScore /= targetMacros.length;
          score += macroAlignmentScore * 0.2;
        }
      }

      // Fattore 5: Qualità nutrizionale generale
      double nutritionalQualityScore = 0.0;

      // Premia alimenti ricchi di fibre
      if (food.fiber > 3.0) {
        nutritionalQualityScore += 0.1;
      }

      // Penalizza alimenti con alto contenuto di zuccheri
      if (food.sugar > 15.0) {
        nutritionalQualityScore -= 0.1;
      }

      // Premia alimenti con micronutrienti
      if (food.micronutrients.isNotEmpty) {
        nutritionalQualityScore += 0.1;
      }

      score += nutritionalQualityScore;

      // Assicurati che il punteggio sia nell'intervallo 0.0-1.0
      score = score.clamp(0.0, 1.0);

      scoredFoods.add(_ScoredFood(food: food, score: score));
    }

    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    return scoredFoods;
  }

  /// Calcola un punteggio per il rapporto di un nutriente
  double _scoreNutrientRatio(double ratio) {
    // Punteggio più alto per rapporti vicini a 0.1-0.2 (contributo ideale per un singolo alimento)
    if (ratio >= 0.05 && ratio <= 0.25) {
      return 0.1;
    } else if (ratio > 0.25 && ratio <= 0.4) {
      return 0.05;
    } else if (ratio > 0.4) {
      return -0.05; // Penalizza alimenti che contribuiscono troppo a un singolo macronutriente
    }
    return 0.0;
  }

  /// Genera una spiegazione avanzata per la raccomandazione di un alimento
  String _generateAdvancedRecommendationReason(Food food, UserProfile userProfile) {
    final reasons = <String>[];

    // Verifica se l'alimento è stato apprezzato in passato
    final userPreferences = _userPreferencesCache[userProfile.id] ?? [];
    final preference = userPreferences.firstWhere(
      (pref) => pref.foodId == food.id,
      orElse: () => LearnedPreference(
        id: '',
        userId: userProfile.id,
        foodId: food.id,
        preferenceScore: 0.0,
        updatedAt: DateTime.now(),
      ),
    );

    if (preference.preferenceScore > 0.5) {
      reasons.add('Hai apprezzato questo alimento in passato');
    }

    // Verifica l'allineamento con gli obiettivi dell'utente
    switch (userProfile.goal) {
      case Goal.weightLoss:
        if (food.calories < 150 && food.proteins > 5) {
          reasons.add('Basso contenuto calorico e buona fonte di proteine, ideale per la perdita di peso');
        }
        break;
      case Goal.weightGain:
        if (food.calories > 200) {
          reasons.add('Buon apporto calorico per supportare l\'aumento di peso');
        }
        break;
      case Goal.maintenance:
        if (food.fiber > 3) {
          reasons.add('Buona fonte di fibre per una dieta equilibrata');
        }
        break;
    }

    // Verifica proprietà nutrizionali specifiche
    if (food.fiber > 5) {
      reasons.add('Eccellente fonte di fibre (${food.fiber}g per 100g)');
    }

    if (food.proteins > 15) {
      reasons.add('Elevato contenuto proteico (${food.proteins}g per 100g)');
    }

    if (food.sugar < 5 && food.carbs > 10) {
      reasons.add('Carboidrati complessi con basso contenuto di zuccheri');
    }

    // Verifica micronutrienti
    if (food.calcium != null && food.calcium! > 200) {
      reasons.add('Buona fonte di calcio per la salute delle ossa');
    }

    if (food.potassium != null && food.potassium! > 300) {
      reasons.add('Ricco di potassio per la salute cardiovascolare');
    }

    if (food.magnesium != null && food.magnesium! > 50) {
      reasons.add('Contiene magnesio, importante per la funzione muscolare e nervosa');
    }

    // Verifica proprietà avanzate
    if (food.glycemicIndex > 0 && food.glycemicIndex < 55) {
      reasons.add('Basso indice glicemico (${food.glycemicIndex}), ideale per il controllo della glicemia');
    }

    // Se non abbiamo trovato ragioni specifiche
    if (reasons.isEmpty) {
      reasons.add('Alimento nutriente che si adatta bene al tuo piano alimentare');
    }

    // Limita a massimo 3 ragioni
    if (reasons.length > 3) {
      reasons.length = 3;
    }

    return reasons.join('. ') + '.';
  }

  /// Genera un messaggio personalizzato per l'utente
  String _generatePersonalizedMessage(UserProfile userProfile, String? mealType) {
    final random = Random();
    final greetings = [
      'Buongiorno',
      'Ciao',
      'Salve',
    ];

    final mealMessages = {
      'breakfast': [
        'Ecco alcune opzioni nutrienti per iniziare bene la giornata',
        'Una colazione equilibrata è fondamentale per la tua energia',
      ],
      'lunch': [
        'Ecco alcune opzioni per un pranzo sano e gustoso',
        'Questi alimenti ti aiuteranno a mantenere l\'energia per il pomeriggio',
      ],
      'dinner': [
        'Ecco alcune opzioni per una cena leggera ma nutriente',
        'Questi alimenti sono perfetti per concludere la giornata',
      ],
      'snack': [
        'Ecco alcuni spuntini sani per ricaricarti',
        'Questi spuntini ti aiuteranno a mantenere l\'energia tra i pasti',
      ],
    };

    final greeting = greetings[random.nextInt(greetings.length)];
    String message = '$greeting, ${userProfile.name}! ';

    if (mealType != null && mealMessages.containsKey(mealType)) {
      final mealMessage = mealMessages[mealType]![random.nextInt(mealMessages[mealType]!.length)];
      message += mealMessage;
    } else {
      message += 'Ecco alcune raccomandazioni alimentari personalizzate per te';
    }

    return message;
  }

  @override
  Future<void> recordFeedback(UserFeedback feedback) async {
    // Verifica se il feedback contiene un ID alimento
    if (feedback.foodId != null) {
      // Converti il rating in un punteggio di preferenza
      final preferenceScore = _convertRatingToPreferenceScore(feedback.rating);

      // Aggiorna la preferenza dell'utente per questo alimento
      await _updateFoodPreference(feedback.userId, feedback.foodId!, preferenceScore);

      // Aggiorna i pattern alimentari se il feedback è positivo
      if (feedback.rating >= 4) {
        // Aggiungi l'alimento ai preferiti dell'utente
        if (!_userPatternsCache.containsKey(feedback.userId)) {
          _userPatternsCache[feedback.userId] = {
            'preferredMealTimes': {},
            'frequentFoods': [],
            'avoidedFoods': [],
            'weekdayPatterns': {},
            'favoriteFoods': [],
          };
        }

        final patterns = _userPatternsCache[feedback.userId]!;
        if (!patterns.containsKey('favoriteFoods')) {
          patterns['favoriteFoods'] = [];
        }

        final favoriteFoods = patterns['favoriteFoods'] as List<dynamic>;
        if (!favoriteFoods.contains(feedback.foodId)) {
          favoriteFoods.add(feedback.foodId);

          // Limita a 20 alimenti preferiti
          if (favoriteFoods.length > 20) {
            favoriteFoods.removeAt(0);
          }
        }
      } else if (feedback.rating <= 2) {
        // Aggiungi l'alimento agli evitati dell'utente
        if (!_userPatternsCache.containsKey(feedback.userId)) {
          _userPatternsCache[feedback.userId] = {
            'preferredMealTimes': {},
            'frequentFoods': [],
            'avoidedFoods': [],
            'weekdayPatterns': {},
          };
        }

        final patterns = _userPatternsCache[feedback.userId]!;
        if (!patterns.containsKey('avoidedFoods')) {
          patterns['avoidedFoods'] = [];
        }

        final avoidedFoods = patterns['avoidedFoods'] as List<dynamic>;
        if (!avoidedFoods.contains(feedback.foodId)) {
          avoidedFoods.add(feedback.foodId);
        }
      }

      // Salva i pattern aggiornati
      await _saveUserPatterns();
    }

    // Se il feedback contiene un ID pasto
    if (feedback.mealId != null) {
      // Aggiorna i pattern per i pasti
      if (!_userPatternsCache.containsKey(feedback.userId)) {
        _userPatternsCache[feedback.userId] = {
          'preferredMealTimes': {},
          'frequentFoods': [],
          'avoidedFoods': [],
          'weekdayPatterns': {},
          'mealPreferences': {},
        };
      }

      final patterns = _userPatternsCache[feedback.userId]!;
      if (!patterns.containsKey('mealPreferences')) {
        patterns['mealPreferences'] = {};
      }

      final mealPreferences = patterns['mealPreferences'] as Map<String, dynamic>;
      if (!mealPreferences.containsKey(feedback.mealId)) {
        mealPreferences[feedback.mealId!] = {
          'rating': feedback.rating,
          'timestamp': DateTime.now().toIso8601String(),
        };
      } else {
        // Aggiorna il rating esistente
        final mealPref = mealPreferences[feedback.mealId!] as Map<String, dynamic>;
        mealPref['rating'] = feedback.rating;
        mealPref['timestamp'] = DateTime.now().toIso8601String();
      }

      // Salva i pattern aggiornati
      await _saveUserPatterns();
    }
  }

  /// Salva i pattern degli utenti nel database
  Future<void> _saveUserPatterns() async {
    // In una implementazione reale, qui salveremmo i pattern nel database
  }

  @override
  Future<List<String>> analyzeDietPlan(WeeklyDietPlan dietPlan, UserProfile userProfile) async {

    final suggestions = <String>[];

    // 1. Verifica la distribuzione calorica
    final calorieDistribution = _analyzeDailyCalorieDistribution(dietPlan);
    if (calorieDistribution['deviation']! > 0.1) {
      suggestions.add('La distribuzione calorica tra i giorni è irregolare. Considera di bilanciare meglio le calorie tra i giorni della settimana.');
    }

    // 2. Verifica la distribuzione dei macronutrienti
    final macroDistribution = _analyzeMacroDistribution(dietPlan);
    if (macroDistribution['proteinDeviation']! > 0.15) {
      suggestions.add('La distribuzione proteica è irregolare. Cerca di mantenere un apporto proteico più costante.');
    }
    if (macroDistribution['carbDeviation']! > 0.15) {
      suggestions.add('La distribuzione dei carboidrati è irregolare. Considera di bilanciare meglio i carboidrati durante la settimana.');
    }
    if (macroDistribution['fatDeviation']! > 0.15) {
      suggestions.add('La distribuzione dei grassi è irregolare. Cerca di mantenere un apporto di grassi più costante.');
    }

    // 3. Verifica la varietà alimentare
    final varietyAnalysis = _analyzeFoodVariety(dietPlan);
    if (varietyAnalysis['uniqueFoodsCount']! < 20) {
      suggestions.add('La varietà alimentare è limitata. Prova ad includere più alimenti diversi per garantire un apporto nutrizionale completo.');
    }
    if (varietyAnalysis['repeatedFoodsCount']! > 10) {
      suggestions.add('Alcuni alimenti vengono ripetuti troppo frequentemente. Considera di variare maggiormente la tua dieta.');
    }

    // 4. Verifica l'adeguatezza nutrizionale
    final nutritionalAdequacy = _analyzeNutritionalAdequacy(dietPlan, userProfile);
    if (nutritionalAdequacy.containsKey('deficiencies')) {
      final deficiencies = nutritionalAdequacy['deficiencies'] as List<String>;
      if (deficiencies.isNotEmpty) {
        suggestions.add('Possibili carenze nutrizionali rilevate: ${deficiencies.join(", ")}. Considera di includere alimenti ricchi di questi nutrienti.');
      }
    }

    // 5. Verifica l'allineamento con gli obiettivi dell'utente
    final goalAlignment = _analyzeGoalAlignment(dietPlan, userProfile);
    if (!goalAlignment['isAligned']) {
      suggestions.add('Il piano dietetico potrebbe non essere ottimale per il tuo obiettivo di ${_goalToString(userProfile.goal)}. ${goalAlignment["suggestion"]}');
    }

    // 6. Verifica la distribuzione dei pasti
    final mealDistribution = _analyzeMealDistribution(dietPlan);
    if (!mealDistribution['isBalanced']) {
      suggestions.add('La distribuzione dei pasti durante la giornata potrebbe essere migliorata. ${mealDistribution["suggestion"]}');
    }

    // 7. Verifica l'idratazione
    if (!_checkHydrationSources(dietPlan)) {
      suggestions.add('Il piano potrebbe non includere sufficienti fonti di idratazione. Considera di aggiungere più acqua, tisane o alimenti ad alto contenuto di acqua.');
    }

    // 8. Verifica la presenza di alimenti preferiti dall'utente
    if (_userPreferencesCache.containsKey(userProfile.id)) {
      final userPreferences = _userPreferencesCache[userProfile.id]!;
      final favoriteFoods = userPreferences
          .where((pref) => pref.preferenceScore > 0.5)
          .map((pref) => pref.foodId)
          .toList();

      if (favoriteFoods.isNotEmpty) {
        final includedFavorites = _countIncludedFavoriteFoods(dietPlan, favoriteFoods);
        if (includedFavorites < favoriteFoods.length * 0.3) {
          suggestions.add('Il piano include pochi dei tuoi alimenti preferiti. Considera di personalizzarlo ulteriormente in base ai tuoi gusti.');
        }
      }
    }

    // 9. Verifica la presenza di alimenti stagionali
    final seasonalAnalysis = _analyzeSeasonalFoods(dietPlan);
    if (seasonalAnalysis['seasonalPercentage']! < 0.3) {
      suggestions.add('Il piano include pochi alimenti stagionali. Considera di includere più alimenti di stagione per una maggiore freschezza e sostenibilità.');
    }

    // 10. Suggerimenti specifici per il Dr. Staffilano
    suggestions.add(_generateStaffilanoSpecificSuggestion(dietPlan, userProfile));

    // Se non ci sono suggerimenti, aggiungi un messaggio positivo
    if (suggestions.isEmpty) {
      suggestions.add('Il piano dietetico è ben bilanciato e adeguato alle tue esigenze. Continua così!');
    }

    return suggestions;
  }

  /// Analizza la distribuzione calorica giornaliera
  Map<String, double> _analyzeDailyCalorieDistribution(WeeklyDietPlan dietPlan) {
    final dailyCalories = dietPlan.dailyPlans.map((day) => day.totalCaloriesPlanned).toList();

    // Calcola media e deviazione standard
    final avgCalories = dailyCalories.reduce((a, b) => a + b) / dailyCalories.length;

    double sumSquaredDiff = 0;
    for (final calories in dailyCalories) {
      sumSquaredDiff += pow(calories - avgCalories, 2);
    }

    final stdDev = sqrt(sumSquaredDiff / dailyCalories.length);
    final deviation = stdDev / avgCalories; // Coefficiente di variazione

    return {
      'average': avgCalories,
      'stdDev': stdDev,
      'deviation': deviation,
    };
  }

  /// Analizza la distribuzione dei macronutrienti
  Map<String, double> _analyzeMacroDistribution(WeeklyDietPlan dietPlan) {
    final dailyProteins = <double>[];
    final dailyCarbs = <double>[];
    final dailyFats = <double>[];

    for (final day in dietPlan.dailyPlans) {
      double totalProteins = 0;
      double totalCarbs = 0;
      double totalFats = 0;

      for (final meal in day.meals) {
        final macros = meal.totalMacros;
        totalProteins += macros['proteins'] ?? 0;
        totalCarbs += macros['carbs'] ?? 0;
        totalFats += macros['fats'] ?? 0;
      }

      dailyProteins.add(totalProteins);
      dailyCarbs.add(totalCarbs);
      dailyFats.add(totalFats);
    }

    // Calcola i coefficienti di variazione per ogni macronutriente
    final proteinDeviation = _calculateCoeffOfVariation(dailyProteins);
    final carbDeviation = _calculateCoeffOfVariation(dailyCarbs);
    final fatDeviation = _calculateCoeffOfVariation(dailyFats);

    return {
      'proteinDeviation': proteinDeviation,
      'carbDeviation': carbDeviation,
      'fatDeviation': fatDeviation,
    };
  }

  /// Calcola il coefficiente di variazione di una lista di valori
  double _calculateCoeffOfVariation(List<double> values) {
    if (values.isEmpty) return 0;

    final avg = values.reduce((a, b) => a + b) / values.length;
    if (avg == 0) return 0;

    double sumSquaredDiff = 0;
    for (final value in values) {
      sumSquaredDiff += pow(value - avg, 2);
    }

    final stdDev = sqrt(sumSquaredDiff / values.length);
    return stdDev / avg;
  }

  /// Analizza la varietà alimentare nel piano
  Map<String, int> _analyzeFoodVariety(WeeklyDietPlan dietPlan) {
    final allFoodIds = <String>[];
    final foodCounts = <String, int>{};

    for (final day in dietPlan.dailyPlans) {
      for (final meal in day.meals) {
        for (final food in meal.foods) {
          allFoodIds.add(food.food.id);
          foodCounts[food.food.id] = (foodCounts[food.food.id] ?? 0) + 1;
        }
      }
    }

    final uniqueFoodsCount = foodCounts.length;
    final repeatedFoodsCount = foodCounts.entries.where((e) => e.value > 3).length;

    return {
      'uniqueFoodsCount': uniqueFoodsCount,
      'repeatedFoodsCount': repeatedFoodsCount,
      'totalFoodsCount': allFoodIds.length,
    };
  }

  /// Analizza l'adeguatezza nutrizionale del piano
  Map<String, dynamic> _analyzeNutritionalAdequacy(WeeklyDietPlan dietPlan, UserProfile userProfile) {
    // Deficienze nutrizionali da verificare
    final deficiencies = <String>[];

    // Calcola l'apporto medio giornaliero di fibre
    double totalFiber = 0;
    int daysCount = dietPlan.dailyPlans.length;

    for (final day in dietPlan.dailyPlans) {
      double dayFiber = 0;

      for (final meal in day.meals) {
        for (final food in meal.foods) {
          dayFiber += food.food.fiber * food.grams / 100;
        }
      }

      totalFiber += dayFiber;
    }

    final avgFiber = totalFiber / daysCount;
    if (avgFiber < 25) {
      deficiencies.add('fibre');
    }

    // Verifica l'apporto di calcio
    double totalCalcium = 0;
    int calciumDays = 0;

    for (final day in dietPlan.dailyPlans) {
      double dayCalcium = 0;
      bool hasCalciumData = false;

      for (final meal in day.meals) {
        for (final food in meal.foods) {
          if (food.food.calcium != null) {
            dayCalcium += food.food.calcium! * food.grams / 100;
            hasCalciumData = true;
          }
        }
      }

      if (hasCalciumData) {
        totalCalcium += dayCalcium;
        calciumDays++;
      }
    }

    if (calciumDays > 0) {
      final avgCalcium = totalCalcium / calciumDays;
      if (avgCalcium < 800) {
        deficiencies.add('calcio');
      }
    }

    // Verifica l'apporto di potassio
    double totalPotassium = 0;
    int potassiumDays = 0;

    for (final day in dietPlan.dailyPlans) {
      double dayPotassium = 0;
      bool hasPotassiumData = false;

      for (final meal in day.meals) {
        for (final food in meal.foods) {
          if (food.food.potassium != null) {
            dayPotassium += food.food.potassium! * food.grams / 100;
            hasPotassiumData = true;
          }
        }
      }

      if (hasPotassiumData) {
        totalPotassium += dayPotassium;
        potassiumDays++;
      }
    }

    if (potassiumDays > 0) {
      final avgPotassium = totalPotassium / potassiumDays;
      if (avgPotassium < 3500) {
        deficiencies.add('potassio');
      }
    }

    return {
      'deficiencies': deficiencies,
    };
  }

  /// Analizza l'allineamento del piano con gli obiettivi dell'utente
  Map<String, dynamic> _analyzeGoalAlignment(WeeklyDietPlan dietPlan, UserProfile userProfile) {
    bool isAligned = true;
    String suggestion = '';

    switch (userProfile.goal) {
      case Goal.weightLoss:
        // Verifica che il deficit calorico sia appropriato
        final tdee = userProfile.calculateTDEE();
        final avgCalories = dietPlan.dailyPlans
            .map((day) => day.totalCaloriesPlanned)
            .reduce((a, b) => a + b) / dietPlan.dailyPlans.length;

        if (avgCalories > tdee - 300) {
          isAligned = false;
          suggestion = 'Per la perdita di peso, considera di ridurre leggermente l\'apporto calorico mantenendo un deficit di 300-500 calorie.';
        } else if (avgCalories < tdee - 800) {
          isAligned = false;
          suggestion = 'Il deficit calorico potrebbe essere troppo elevato. Considera di aumentare leggermente l\'apporto calorico per una perdita di peso più sostenibile.';
        }
        break;

      case Goal.weightGain:
        // Verifica che il surplus calorico sia appropriato
        final tdee = userProfile.calculateTDEE();
        final avgCalories = dietPlan.dailyPlans
            .map((day) => day.totalCaloriesPlanned)
            .reduce((a, b) => a + b) / dietPlan.dailyPlans.length;

        if (avgCalories < tdee + 300) {
          isAligned = false;
          suggestion = 'Per l\'aumento di peso, considera di aumentare leggermente l\'apporto calorico mantenendo un surplus di 300-500 calorie.';
        } else if (avgCalories > tdee + 800) {
          isAligned = false;
          suggestion = 'Il surplus calorico potrebbe essere troppo elevato. Considera di ridurre leggermente l\'apporto calorico per un aumento di peso più sano.';
        }
        break;

      case Goal.maintenance:
        // Verifica che le calorie siano vicine al TDEE
        final tdee = userProfile.calculateTDEE();
        final avgCalories = dietPlan.dailyPlans
            .map((day) => day.totalCaloriesPlanned)
            .reduce((a, b) => a + b) / dietPlan.dailyPlans.length;

        if (avgCalories < tdee - 200 || avgCalories > tdee + 200) {
          isAligned = false;
          suggestion = 'Per il mantenimento del peso, considera di adeguare l\'apporto calorico a circa ${tdee.round()} calorie al giorno.';
        }
        break;
    }

    return {
      'isAligned': isAligned,
      'suggestion': suggestion,
    };
  }

  /// Analizza la distribuzione dei pasti durante la giornata
  Map<String, dynamic> _analyzeMealDistribution(WeeklyDietPlan dietPlan) {
    bool isBalanced = true;
    String suggestion = '';

    // Calcola la distribuzione calorica media tra i pasti
    final mealCalories = <String, List<int>>{};

    for (final day in dietPlan.dailyPlans) {
      for (final meal in day.meals) {
        if (!mealCalories.containsKey(meal.type)) {
          mealCalories[meal.type] = [];
        }
        mealCalories[meal.type]!.add(meal.totalCalories);
      }
    }

    // Calcola le percentuali medie per tipo di pasto
    final mealPercentages = <String, double>{};
    final totalDailyCalories = dietPlan.dailyPlans
        .map((day) => day.totalCaloriesPlanned)
        .reduce((a, b) => a + b) / dietPlan.dailyPlans.length;

    for (final entry in mealCalories.entries) {
      final avgCalories = entry.value.reduce((a, b) => a + b) / entry.value.length;
      mealPercentages[entry.key] = avgCalories / totalDailyCalories;
    }

    // Verifica se la distribuzione è sbilanciata
    if (mealPercentages.containsKey('breakfast') && mealPercentages['breakfast']! < 0.15) {
      isBalanced = false;
      suggestion = 'La colazione sembra troppo leggera. Considera di aumentare leggermente l\'apporto calorico a colazione.';
    } else if (mealPercentages.containsKey('dinner') && mealPercentages['dinner']! > 0.45) {
      isBalanced = false;
      suggestion = 'La cena sembra troppo abbondante. Considera di distribuire meglio le calorie durante la giornata.';
    }

    // Verifica la presenza di spuntini
    if (!mealPercentages.containsKey('snack') && dietPlan.userProfile.mealsPerDay > 3) {
      isBalanced = false;
      suggestion = 'Considera di includere spuntini sani tra i pasti principali per mantenere stabile la glicemia.';
    }

    return {
      'isBalanced': isBalanced,
      'suggestion': suggestion,
      'mealPercentages': mealPercentages,
    };
  }

  /// Verifica la presenza di fonti di idratazione
  bool _checkHydrationSources(WeeklyDietPlan dietPlan) {
    int hydrationSourcesCount = 0;

    for (final day in dietPlan.dailyPlans) {
      for (final meal in day.meals) {
        for (final food in meal.foods) {
          // Verifica se l'alimento è una bevanda o ha alto contenuto d'acqua
          if (food.food.categories.contains(FoodCategory.beverage) ||
              food.food.extendedCategories.any((cat) =>
                  cat.contains('water') ||
                  cat.contains('acqua') ||
                  cat.contains('hydration'))) {
            hydrationSourcesCount++;
          }
        }
      }
    }

    // Dovremmo avere almeno una fonte di idratazione al giorno
    return hydrationSourcesCount >= dietPlan.dailyPlans.length;
  }

  /// Conta quanti alimenti preferiti sono inclusi nel piano
  int _countIncludedFavoriteFoods(WeeklyDietPlan dietPlan, List<String> favoriteFoodIds) {
    final includedFavorites = <String>{};

    for (final day in dietPlan.dailyPlans) {
      for (final meal in day.meals) {
        for (final food in meal.foods) {
          if (favoriteFoodIds.contains(food.food.id)) {
            includedFavorites.add(food.food.id);
          }
        }
      }
    }

    return includedFavorites.length;
  }

  /// Analizza la presenza di alimenti stagionali
  Map<String, double> _analyzeSeasonalFoods(WeeklyDietPlan dietPlan) {
    int totalFoods = 0;
    int seasonalFoods = 0;

    // Determina la stagione corrente
    final now = DateTime.now();
    final currentMonth = now.month;
    Season currentSeason;

    if (currentMonth >= 3 && currentMonth <= 5) {
      currentSeason = Season.spring;
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      currentSeason = Season.summer;
    } else if (currentMonth >= 9 && currentMonth <= 11) {
      currentSeason = Season.autumn;
    } else {
      currentSeason = Season.winter;
    }

    for (final day in dietPlan.dailyPlans) {
      for (final meal in day.meals) {
        for (final food in meal.foods) {
          totalFoods++;

          // Verifica se l'alimento è stagionale
          if (food.food.isSeasonal) {
            // Verifica se è di stagione nel mese corrente
            if (food.food.seasonalMonths.contains(currentMonth)) {
              seasonalFoods++;
            }
          }
        }
      }
    }

    final seasonalPercentage = totalFoods > 0 ? (seasonalFoods / totalFoods).toDouble() : 0.0;

    return {
      'seasonalPercentage': seasonalPercentage,
      'seasonalFoodsCount': seasonalFoods.toDouble(),
      'totalFoodsCount': totalFoods.toDouble(),
    };
  }

  /// Genera un suggerimento specifico nello stile del Dr. Staffilano
  String _generateStaffilanoSpecificSuggestion(WeeklyDietPlan dietPlan, UserProfile userProfile) {
    final random = Random();
    final suggestions = [
      'Ricorda che la vera prevenzione è la ricerca della felicità. Scegli alimenti che ti danno gioia oltre che nutrimento.',
      'Per la salute del cuore, considera di includere più alimenti ricchi di acidi grassi omega-3 come pesce azzurro, noci e semi di lino.',
      'La dieta mediterranea è un tesoro per la salute cardiovascolare. Privilegia olio d\'oliva, verdure fresche e cereali integrali.',
      'Ricorda che il cibo è medicina: scegli alimenti freschi e non processati per prenderti cura del tuo cuore.',
      'Un\'alimentazione varia e colorata è la chiave per una buona salute cardiovascolare. Cerca di includere alimenti di tutti i colori.',
      'La regolarità nei pasti è importante quanto la qualità degli alimenti. Mantieni orari regolari per ottimizzare il metabolismo.',
    ];

    return suggestions[random.nextInt(suggestions.length)];
  }

  /// Converte un obiettivo in una stringa leggibile
  String _goalToString(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return 'perdita di peso';
      case Goal.weightGain:
        return 'aumento di peso';
      case Goal.maintenance:
        return 'mantenimento del peso';
      default:
        return 'obiettivo personalizzato';
    }
  }

  @override
  Future<WeeklyDietPlan> generatePersonalizedDietPlan(UserProfile userProfile, {int weeks = 1}) async {

    // Utilizziamo il generatore di diete ottimizzato esistente come base
    final dietGenerator = await OptimizedDietGeneratorService.getInstance();

    // Genera il piano dietetico base
    final baseDietPlan = await dietGenerator.generateWeeklyDietPlan(userProfile, weeks: weeks);

    // Ora personalizziamo il piano in base alle preferenze apprese
    final personalizedDietPlan = await _personalizeDietPlan(baseDietPlan, userProfile);

    return personalizedDietPlan;
  }

  /// Personalizza un piano dietetico in base alle preferenze dell'utente
  Future<WeeklyDietPlan> _personalizeDietPlan(WeeklyDietPlan basePlan, UserProfile userProfile) async {
    // Ottieni le preferenze dell'utente
    final userPreferences = await getLearnedPreferences(userProfile.id);

    // Se non ci sono preferenze, restituisci il piano base
    if (userPreferences.isEmpty) {
      return basePlan;
    }

    // Crea una copia del piano base per modificarlo
    final dailyPlans = List<DailyDietPlan>.from(basePlan.dailyPlans);

    // Per ogni giorno del piano
    for (int i = 0; i < dailyPlans.length; i++) {
      final day = dailyPlans[i];

      // Crea una lista di pasti modificati
      final modifiedMeals = <PlannedMeal>[];

      // Per ogni pasto del giorno
      for (final meal in day.meals) {
        // Verifica se possiamo migliorare questo pasto
        final improvedMeal = await _improveMealWithPreferences(meal, userPreferences, userProfile);
        modifiedMeals.add(improvedMeal);
      }

      // Sostituisci i pasti del giorno con quelli migliorati
      dailyPlans[i] = day.copyWith(meals: modifiedMeals);
    }

    // Crea un nuovo piano settimanale con i giorni personalizzati
    return basePlan.copyWith(dailyPlans: dailyPlans);
  }

  /// Migliora un pasto in base alle preferenze dell'utente
  Future<PlannedMeal> _improveMealWithPreferences(
    PlannedMeal meal,
    List<LearnedPreference> preferences,
    UserProfile userProfile
  ) async {
    // Se il pasto ha meno di 2 alimenti, non modificarlo
    if (meal.foods.length < 2) {
      return meal;
    }

    // Ordina le preferenze per punteggio decrescente
    preferences.sort((a, b) => b.preferenceScore.compareTo(a.preferenceScore));

    // Ottieni gli alimenti preferiti con punteggio alto
    final favoriteFoodIds = preferences
        .where((pref) => pref.preferenceScore > 0.5)
        .map((pref) => pref.foodId)
        .toList();

    // Ottieni gli alimenti non graditi
    final dislikedFoodIds = preferences
        .where((pref) => pref.preferenceScore < -0.3)
        .map((pref) => pref.foodId)
        .toList();

    // Crea una copia delle porzioni di cibo
    final foodPortions = List<FoodPortion>.from(meal.foods);

    // Verifica se ci sono alimenti non graditi nel pasto
    bool hasDislikedFoods = false;
    for (int i = 0; i < foodPortions.length; i++) {
      if (dislikedFoodIds.contains(foodPortions[i].food.id)) {
        hasDislikedFoods = true;
        break;
      }
    }

    // Se ci sono alimenti non graditi, sostituiscili
    if (hasDislikedFoods) {
      for (int i = 0; i < foodPortions.length; i++) {
        if (dislikedFoodIds.contains(foodPortions[i].food.id)) {
          // Trova un alimento alternativo
          final alternatives = await getAlternativeFoods(foodPortions[i].food, 5);

          if (alternatives.isNotEmpty) {
            // Scegli l'alternativa con il punteggio più alto
            final bestAlternative = alternatives.first.food;

            // Sostituisci l'alimento mantenendo la quantità
            foodPortions[i] = FoodPortion(
              food: bestAlternative,
              grams: foodPortions[i].grams,
            );
          }
        }
      }
    }

    // Verifica se possiamo aggiungere un alimento preferito
    if (favoriteFoodIds.isNotEmpty && meal.type != 'snack' && foodPortions.length < 5) {
      // Cerca di aggiungere un alimento preferito che sia adatto al tipo di pasto
      for (final foodId in favoriteFoodIds) {
        // Ottieni l'alimento dal database
        final food = await _foodDatabaseService.getFoodById(foodId);

        if (food != null) {
          // Verifica se l'alimento è adatto al tipo di pasto
          final mealTypeEnum = MealType.values.firstWhere(
            (e) => e.toString().split('.').last == meal.type,
            orElse: () => MealType.values.first,
          );

          if (food.suitableForMeals.contains(mealTypeEnum)) {
            // Verifica se l'alimento è già presente nel pasto
            bool alreadyInMeal = foodPortions.any((portion) => portion.food.id == food.id);

            if (!alreadyInMeal) {
              // Aggiungi l'alimento in una porzione appropriata
              final portion = _calculateAppropriatePortionSize(food, meal, userProfile);

              if (portion > 0) {
                foodPortions.add(FoodPortion(
                  food: food,
                  grams: portion,
                ));

                // Aggiungiamo solo un alimento preferito per pasto
                break;
              }
            }
          }
        }
      }
    }

    // Crea un nuovo pasto con le porzioni modificate
    return PlannedMeal(
      id: meal.id,
      name: meal.name,
      type: meal.type,
      foods: foodPortions,
      time: meal.time,
      isCompleted: meal.isCompleted,
    );
  }

  /// Calcola una porzione appropriata per un alimento in un pasto
  int _calculateAppropriatePortionSize(Food food, PlannedMeal meal, UserProfile userProfile) {
    // Calcola le calorie attuali del pasto
    final currentCalories = meal.totalCalories;

    // Stima le calorie target per questo pasto in base al TDEE
    final tdee = userProfile.calculateTDEE();
    double targetCalories;

    switch (meal.type) {
      case 'breakfast':
        targetCalories = tdee * 0.25; // 25% delle calorie giornaliere
        break;
      case 'lunch':
        targetCalories = tdee * 0.35; // 35% delle calorie giornaliere
        break;
      case 'dinner':
        targetCalories = tdee * 0.30; // 30% delle calorie giornaliere
        break;
      case 'snack':
        targetCalories = tdee * 0.10; // 10% delle calorie giornaliere
        break;
      default:
        targetCalories = tdee * 0.25;
    }

    // Calcola le calorie disponibili
    final availableCalories = targetCalories - currentCalories;

    // Se non ci sono calorie disponibili, non aggiungere l'alimento
    if (availableCalories < 50) {
      return 0;
    }

    // Calcola la porzione in base alle calorie disponibili
    // Limita al 50% delle calorie disponibili per non sbilanciare troppo il pasto
    final maxCaloriesToAdd = availableCalories * 0.5;

    // Calcola i grammi in base alle calorie per 100g dell'alimento
    int grams = (maxCaloriesToAdd / food.calories * 100).round();

    // Limita la porzione a valori ragionevoli
    if (food.categories.contains(FoodCategory.vegetable)) {
      // Per le verdure, porzioni più grandi sono accettabili
      grams = grams.clamp(50, 200);
    } else if (food.categories.contains(FoodCategory.fruit)) {
      // Per la frutta, porzioni medie
      grams = grams.clamp(50, 150);
    } else if (food.categories.contains(FoodCategory.protein)) {
      // Per le proteine, porzioni più piccole
      grams = grams.clamp(30, 120);
    } else if (food.categories.contains(FoodCategory.grain)) {
      // Per i cereali, porzioni medie
      grams = grams.clamp(40, 100);
    } else if (food.categories.contains(FoodCategory.dairy)) {
      // Per i latticini, porzioni più piccole
      grams = grams.clamp(30, 100);
    } else if (food.categories.contains(FoodCategory.fat)) {
      // Per i grassi, porzioni molto piccole
      grams = grams.clamp(5, 30);
    } else {
      // Per altri alimenti, porzioni medie
      grams = grams.clamp(30, 100);
    }

    return grams;
  }

  @override
  Future<List<FoodRecommendation>> getSimilarFoods(Food food, int limit) async {

    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabaseService.getAllFoods();

    // Rimuovi l'alimento di riferimento dalla lista
    final otherFoods = allFoods.where((f) => f.id != food.id).toList();

    // Calcola la similarità per ogni alimento
    final scoredFoods = <_ScoredFood>[];

    for (final otherFood in otherFoods) {
      final similarityScore = _calculateFoodSimilarity(food, otherFood);
      scoredFoods.add(_ScoredFood(food: otherFood, score: similarityScore));
    }

    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    // Prendi i primi 'limit' alimenti
    final topSimilarFoods = scoredFoods.take(limit).toList();

    // Converti in raccomandazioni
    return topSimilarFoods.map((scoredFood) {
      return FoodRecommendation(
        food: scoredFood.food,
        score: scoredFood.score,
        reason: _generateSimilarityReason(food, scoredFood.food),
      );
    }).toList();
  }

  /// Calcola la similarità tra due alimenti
  double _calculateFoodSimilarity(Food food1, Food food2) {
    double score = 0.0;

    // Fattore 1: Stessa categoria principale (peso: 0.3)
    if (food1.categories.any((cat) => food2.categories.contains(cat))) {
      score += 0.3;
    }

    // Fattore 2: Profilo nutrizionale simile (peso: 0.4)
    final nutritionalSimilarity = _calculateNutritionalSimilarity(food1, food2);
    score += nutritionalSimilarity * 0.4;

    // Fattore 3: Adatto agli stessi tipi di pasto (peso: 0.15)
    if (food1.suitableForMeals.any((meal) => food2.suitableForMeals.contains(meal))) {
      score += 0.15;
    }

    // Fattore 4: Proprietà dietetiche simili (peso: 0.15)
    if (food1.isVegetarian == food2.isVegetarian &&
        food1.isVegan == food2.isVegan &&
        food1.isGlutenFree == food2.isGlutenFree &&
        food1.isDairyFree == food2.isDairyFree) {
      score += 0.15;
    }

    return score;
  }

  /// Calcola la similarità nutrizionale tra due alimenti
  double _calculateNutritionalSimilarity(Food food1, Food food2) {
    // Calcola la differenza percentuale per ogni nutriente principale
    final calorieDiff = (food1.calories - food2.calories).abs() / max(food1.calories, 1);
    final proteinDiff = (food1.proteins - food2.proteins).abs() / max(food1.proteins, 1);
    final carbDiff = (food1.carbs - food2.carbs).abs() / max(food1.carbs, 1);
    final fatDiff = (food1.fats - food2.fats).abs() / max(food1.fats, 1);

    // Normalizza le differenze (più basse = più simili)
    final calorieScore = 1.0 - min(calorieDiff, 1.0);
    final proteinScore = 1.0 - min(proteinDiff, 1.0);
    final carbScore = 1.0 - min(carbDiff, 1.0);
    final fatScore = 1.0 - min(fatDiff, 1.0);

    // Media dei punteggi
    return (calorieScore + proteinScore + carbScore + fatScore) / 4.0;
  }

  /// Genera una spiegazione per la similarità tra due alimenti
  String _generateSimilarityReason(Food originalFood, Food similarFood) {
    final reasons = <String>[];

    // Verifica la categoria
    if (originalFood.categories.any((cat) => similarFood.categories.contains(cat))) {
      reasons.add('Appartiene alla stessa categoria alimentare');
    }

    // Verifica il profilo nutrizionale
    final calorieDiff = ((similarFood.calories - originalFood.calories) / originalFood.calories * 100).round();
    if (calorieDiff.abs() < 20) {
      reasons.add('Profilo calorico simile (${calorieDiff > 0 ? '+' : ''}$calorieDiff% calorie)');
    }

    // Verifica i macronutrienti
    if ((similarFood.proteins - originalFood.proteins).abs() < 3) {
      reasons.add('Contenuto proteico simile');
    }

    if ((similarFood.carbs - originalFood.carbs).abs() < 5) {
      reasons.add('Contenuto di carboidrati simile');
    }

    if ((similarFood.fats - originalFood.fats).abs() < 3) {
      reasons.add('Contenuto di grassi simile');
    }

    // Verifica proprietà dietetiche
    if (originalFood.isVegetarian == similarFood.isVegetarian &&
        originalFood.isVegan == similarFood.isVegan) {
      if (originalFood.isVegan) {
        reasons.add('Adatto a una dieta vegana come l\'alimento originale');
      } else if (originalFood.isVegetarian) {
        reasons.add('Adatto a una dieta vegetariana come l\'alimento originale');
      }
    }

    // Se non abbiamo trovato ragioni specifiche
    if (reasons.isEmpty) {
      reasons.add('Alternativa nutrizionalmente simile a ${originalFood.name}');
    }

    // Limita a massimo 2 ragioni
    if (reasons.length > 2) {
      reasons.length = 2;
    }

    return reasons.join('. ') + '.';
  }

  @override
  Future<List<FoodRecommendation>> getComplementaryFoods(Food food, int limit) async {

    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabaseService.getAllFoods();

    // Rimuovi l'alimento di riferimento dalla lista
    final otherFoods = allFoods.where((f) => f.id != food.id).toList();

    // Calcola la complementarietà per ogni alimento
    final scoredFoods = <_ScoredFood>[];

    for (final otherFood in otherFoods) {
      final complementaryScore = _calculateFoodComplementarity(food, otherFood);
      scoredFoods.add(_ScoredFood(food: otherFood, score: complementaryScore));
    }

    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    // Prendi i primi 'limit' alimenti
    final topComplementaryFoods = scoredFoods.take(limit).toList();

    // Converti in raccomandazioni
    return topComplementaryFoods.map((scoredFood) {
      return FoodRecommendation(
        food: scoredFood.food,
        score: scoredFood.score,
        reason: _generateComplementaryReason(food, scoredFood.food),
      );
    }).toList();
  }

  /// Calcola la complementarietà tra due alimenti
  double _calculateFoodComplementarity(Food food1, Food food2) {
    double score = 0.0;

    // Fattore 1: Categorie complementari (peso: 0.3)
    if (_areCategoriesComplementary(food1.categories, food2.categories)) {
      score += 0.3;
    }

    // Fattore 2: Profilo nutrizionale complementare (peso: 0.4)
    final nutritionalComplementarity = _calculateNutritionalComplementarity(food1, food2);
    score += nutritionalComplementarity * 0.4;

    // Fattore 3: Adatto allo stesso tipo di pasto (peso: 0.2)
    if (food1.suitableForMeals.any((meal) => food2.suitableForMeals.contains(meal))) {
      score += 0.2;
    }

    // Fattore 4: Combinazione tradizionale (peso: 0.1)
    if (_isTraditionalCombination(food1, food2)) {
      score += 0.1;
    }

    return score;
  }

  /// Verifica se due categorie di alimenti sono complementari
  bool _areCategoriesComplementary(List<FoodCategory> categories1, List<FoodCategory> categories2) {
    // Definisci le coppie di categorie complementari
    final complementaryPairs = {
      FoodCategory.protein: [FoodCategory.vegetable, FoodCategory.grain],
      FoodCategory.grain: [FoodCategory.protein, FoodCategory.vegetable],
      FoodCategory.vegetable: [FoodCategory.protein, FoodCategory.grain],
      FoodCategory.fruit: [FoodCategory.dairy],
      FoodCategory.dairy: [FoodCategory.fruit, FoodCategory.grain],
    };

    // Verifica se c'è almeno una coppia complementare
    for (final category1 in categories1) {
      if (complementaryPairs.containsKey(category1)) {
        final complementaryCategories = complementaryPairs[category1]!;
        for (final category2 in categories2) {
          if (complementaryCategories.contains(category2)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /// Calcola la complementarietà nutrizionale tra due alimenti
  double _calculateNutritionalComplementarity(Food food1, Food food2) {
    double score = 0.0;

    // Complementarietà proteica
    if (food1.proteins < 5 && food2.proteins > 10) {
      score += 0.25;
    }

    // Complementarietà di fibre
    if (food1.fiber < 2 && food2.fiber > 5) {
      score += 0.25;
    }

    // Complementarietà di micronutrienti
    if ((food1.calcium == null || food1.calcium! < 100) &&
        (food2.calcium != null && food2.calcium! > 200)) {
      score += 0.25;
    }

    if ((food1.potassium == null || food1.potassium! < 200) &&
        (food2.potassium != null && food2.potassium! > 400)) {
      score += 0.25;
    }

    return score;
  }

  /// Verifica se due alimenti formano una combinazione tradizionale
  bool _isTraditionalCombination(Food food1, Food food2) {
    // Definisci alcune combinazioni tradizionali
    final traditionalCombinations = [
      {'pasta', 'pomodoro'}, // Pasta al pomodoro
      {'pane', 'formaggio'}, // Pane e formaggio
      {'riso', 'fagioli'}, // Riso e fagioli
      {'patate', 'carne'}, // Carne e patate
      {'yogurt', 'frutta'}, // Yogurt e frutta
      {'insalata', 'olio'}, // Insalata e olio
    ];

    // Verifica se i nomi degli alimenti formano una combinazione tradizionale
    final name1 = food1.name.toLowerCase();
    final name2 = food2.name.toLowerCase();

    for (final combination in traditionalCombinations) {
      final combinationList = combination.toList();
      if ((name1.contains(combinationList[0]) && name2.contains(combinationList[1])) ||
          (name1.contains(combinationList[1]) && name2.contains(combinationList[0]))) {
        return true;
      }
    }

    return false;
  }

  /// Genera una spiegazione per la complementarietà tra due alimenti
  String _generateComplementaryReason(Food originalFood, Food complementaryFood) {
    final reasons = <String>[];

    // Verifica la complementarietà di categorie
    if (_areCategoriesComplementary(originalFood.categories, complementaryFood.categories)) {
      reasons.add('Si abbina perfettamente a ${originalFood.name} per una combinazione nutrizionalmente completa');
    }

    // Verifica la complementarietà proteica
    if (originalFood.proteins < 5 && complementaryFood.proteins > 10) {
      reasons.add('Aggiunge proteine che completano il profilo nutrizionale di ${originalFood.name}');
    }

    // Verifica la complementarietà di fibre
    if (originalFood.fiber < 2 && complementaryFood.fiber > 5) {
      reasons.add('Fornisce fibre che bilanciano ${originalFood.name}');
    }

    // Verifica la complementarietà di micronutrienti
    if ((originalFood.calcium == null || originalFood.calcium! < 100) &&
        (complementaryFood.calcium != null && complementaryFood.calcium! > 200)) {
      reasons.add('Aggiunge calcio che manca in ${originalFood.name}');
    }

    // Verifica se è una combinazione tradizionale
    if (_isTraditionalCombination(originalFood, complementaryFood)) {
      reasons.add('Combinazione tradizionale e gustosa con ${originalFood.name}');
    }

    // Se non abbiamo trovato ragioni specifiche
    if (reasons.isEmpty) {
      reasons.add('Si abbina bene a ${originalFood.name} per un pasto equilibrato');
    }

    // Limita a massimo 2 ragioni
    if (reasons.length > 2) {
      reasons.length = 2;
    }

    return reasons.join('. ') + '.';
  }

  @override
  Future<List<FoodRecommendation>> getAlternativeFoods(Food food, int limit) async {

    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabaseService.getAllFoods();

    // Rimuovi l'alimento di riferimento dalla lista
    final otherFoods = allFoods.where((f) => f.id != food.id).toList();

    // Calcola il punteggio di alternativa per ogni alimento
    final scoredFoods = <_ScoredFood>[];

    for (final otherFood in otherFoods) {
      final alternativeScore = _calculateAlternativeScore(food, otherFood);
      scoredFoods.add(_ScoredFood(food: otherFood, score: alternativeScore));
    }

    // Ordina per punteggio decrescente
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    // Prendi i primi 'limit' alimenti
    final topAlternatives = scoredFoods.take(limit).toList();

    // Converti in raccomandazioni
    return topAlternatives.map((scoredFood) {
      return FoodRecommendation(
        food: scoredFood.food,
        score: scoredFood.score,
        reason: _generateAlternativeReason(food, scoredFood.food),
      );
    }).toList();
  }

  /// Calcola il punteggio di alternativa tra due alimenti
  double _calculateAlternativeScore(Food originalFood, Food alternativeFood) {
    double score = 0.0;

    // Fattore 1: Stessa categoria principale (peso: 0.3)
    if (originalFood.categories.any((cat) => alternativeFood.categories.contains(cat))) {
      score += 0.3;
    }

    // Fattore 2: Profilo nutrizionale simile (peso: 0.3)
    final nutritionalSimilarity = _calculateNutritionalSimilarity(originalFood, alternativeFood);
    score += nutritionalSimilarity * 0.3;

    // Fattore 3: Adatto agli stessi tipi di pasto (peso: 0.2)
    if (originalFood.suitableForMeals.any((meal) => alternativeFood.suitableForMeals.contains(meal))) {
      score += 0.2;
    }

    // Fattore 4: Proprietà dietetiche simili o migliori (peso: 0.2)
    double dietaryScore = 0.0;

    // Verifica se l'alternativa è più adatta a diete speciali
    if (!originalFood.isVegetarian && alternativeFood.isVegetarian) {
      dietaryScore += 0.05;
    } else if (originalFood.isVegetarian == alternativeFood.isVegetarian) {
      dietaryScore += 0.05;
    }

    if (!originalFood.isVegan && alternativeFood.isVegan) {
      dietaryScore += 0.05;
    } else if (originalFood.isVegan == alternativeFood.isVegan) {
      dietaryScore += 0.05;
    }

    if (!originalFood.isGlutenFree && alternativeFood.isGlutenFree) {
      dietaryScore += 0.05;
    } else if (originalFood.isGlutenFree == alternativeFood.isGlutenFree) {
      dietaryScore += 0.05;
    }

    if (!originalFood.isDairyFree && alternativeFood.isDairyFree) {
      dietaryScore += 0.05;
    } else if (originalFood.isDairyFree == alternativeFood.isDairyFree) {
      dietaryScore += 0.05;
    }

    score += dietaryScore;

    return score;
  }

  /// Genera una spiegazione per l'alternativa a un alimento
  String _generateAlternativeReason(Food originalFood, Food alternativeFood) {
    final reasons = <String>[];

    // Verifica la categoria
    if (originalFood.categories.any((cat) => alternativeFood.categories.contains(cat))) {
      reasons.add('Alternativa nella stessa categoria alimentare di ${originalFood.name}');
    }

    // Verifica il profilo nutrizionale
    final calorieDiff = ((alternativeFood.calories - originalFood.calories) / originalFood.calories * 100).round();
    if (calorieDiff < -10) {
      reasons.add('Contiene meno calorie (${calorieDiff.abs()}% in meno)');
    } else if (calorieDiff.abs() < 10) {
      reasons.add('Profilo calorico simile a ${originalFood.name}');
    }

    // Verifica i macronutrienti
    if (alternativeFood.proteins > originalFood.proteins * 1.2) {
      reasons.add('Più ricco di proteine');
    }

    if (alternativeFood.fiber > originalFood.fiber * 1.5) {
      reasons.add('Più ricco di fibre');
    }

    if (alternativeFood.sugar < originalFood.sugar * 0.7) {
      reasons.add('Contiene meno zuccheri');
    }

    // Verifica proprietà dietetiche
    if (!originalFood.isVegetarian && alternativeFood.isVegetarian) {
      reasons.add('Alternativa vegetariana');
    }

    if (!originalFood.isVegan && alternativeFood.isVegan) {
      reasons.add('Alternativa vegana');
    }

    if (!originalFood.isGlutenFree && alternativeFood.isGlutenFree) {
      reasons.add('Alternativa senza glutine');
    }

    if (!originalFood.isDairyFree && alternativeFood.isDairyFree) {
      reasons.add('Alternativa senza latticini');
    }

    // Se non abbiamo trovato ragioni specifiche
    if (reasons.isEmpty) {
      reasons.add('Buona alternativa a ${originalFood.name} con profilo nutrizionale simile');
    }

    // Limita a massimo 2 ragioni
    if (reasons.length > 2) {
      reasons.length = 2;
    }

    return reasons.join('. ') + '.';
  }

  @override
  Future<List<LearnedPreference>> getLearnedPreferences(String userId) async {
    // Verifica se abbiamo preferenze in cache per questo utente
    if (_userPreferencesCache.containsKey(userId)) {
      return _userPreferencesCache[userId]!;
    }

    // Se non ci sono preferenze in cache, restituisci una lista vuota
    return [];
  }

  @override
  Future<Map<String, dynamic>> getAIStats() async {
    int totalPreferences = 0;
    int totalUsers = _userPreferencesCache.length;
    int totalPatterns = _userPatternsCache.length;

    for (var userPrefs in _userPreferencesCache.values) {
      totalPreferences += userPrefs.length;
    }

    // Calcola la distribuzione delle preferenze
    final preferenceDistribution = <String, int>{
      'positive': 0, // Preferenze positive (score > 0.3)
      'neutral': 0,  // Preferenze neutrali (-0.3 <= score <= 0.3)
      'negative': 0, // Preferenze negative (score < -0.3)
    };

    for (var userPrefs in _userPreferencesCache.values) {
      for (var pref in userPrefs) {
        if (pref.preferenceScore > 0.3) {
          preferenceDistribution['positive'] = (preferenceDistribution['positive'] ?? 0) + 1;
        } else if (pref.preferenceScore < -0.3) {
          preferenceDistribution['negative'] = (preferenceDistribution['negative'] ?? 0) + 1;
        } else {
          preferenceDistribution['neutral'] = (preferenceDistribution['neutral'] ?? 0) + 1;
        }
      }
    }

    return {
      'totalUsers': totalUsers,
      'totalPreferences': totalPreferences,
      'totalPatterns': totalPatterns,
      'averagePreferencesPerUser': totalUsers > 0 ? (totalPreferences / totalUsers).toDouble() : 0.0,
      'preferenceDistribution': preferenceDistribution,
      'isInitialized': _isInitialized,
      'aiVersion': '1.0.0',
      'aiName': AppConstants.aiNutritionistName,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
}
