import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../utils/responsive_utils.dart';
import '../theme/dr_staffilano_theme.dart';
import 'responsive_text.dart';

/// Responsive button widget that adapts to different screen sizes and platforms
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? width;
  final double? height;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.backgroundColor,
    this.foregroundColor,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final minTouchTarget = ResponsiveUtils.getMinTouchTargetSize();
    final deviceType = ResponsiveUtils.getDeviceType(context);

    // Calculate responsive dimensions
    double buttonHeight;
    double fontSize;
    double iconSize;
    double horizontalPadding;

    switch (size) {
      case ButtonSize.small:
        buttonHeight = minTouchTarget;
        fontSize = 12;
        iconSize = 16;
        horizontalPadding = 16;
        break;
      case ButtonSize.medium:
        buttonHeight = minTouchTarget + 8;
        fontSize = 14;
        iconSize = 18;
        horizontalPadding = 24;
        break;
      case ButtonSize.large:
        buttonHeight = minTouchTarget + 16;
        fontSize = 16;
        iconSize = 20;
        horizontalPadding = 32;
        break;
    }

    // Adjust for device type
    switch (deviceType) {
      case DeviceType.tablet:
        buttonHeight *= 1.1;
        fontSize *= 1.1;
        iconSize *= 1.1;
        horizontalPadding *= 1.2;
        break;
      case DeviceType.desktop:
        buttonHeight *= 1.2;
        fontSize *= 1.2;
        iconSize *= 1.2;
        horizontalPadding *= 1.4;
        break;
      case DeviceType.mobile:
        break;
    }

    final borderRadius = ResponsiveUtils.getResponsiveBorderRadius(context, 12);
    final elevation = ResponsiveUtils.getResponsiveElevation(context, 2);

    // Button style based on type
    ButtonStyle buttonStyle;
    Color textColor;

    switch (type) {
      case ButtonType.primary:
        buttonStyle = ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
          foregroundColor: foregroundColor ?? Colors.white,
          elevation: elevation,
          shadowColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: Size(width ?? 0, height ?? buttonHeight),
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        );
        textColor = foregroundColor ?? Colors.white;
        break;

      case ButtonType.secondary:
        buttonStyle = OutlinedButton.styleFrom(
          foregroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
          side: BorderSide(
            color: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
            width: 2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: Size(width ?? 0, height ?? buttonHeight),
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        );
        textColor = backgroundColor ?? DrStaffilanoTheme.primaryGreen;
        break;

      case ButtonType.tertiary:
        buttonStyle = TextButton.styleFrom(
          foregroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: Size(width ?? 0, height ?? buttonHeight),
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        );
        textColor = backgroundColor ?? DrStaffilanoTheme.primaryGreen;
        break;

      case ButtonType.danger:
        buttonStyle = ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          elevation: elevation,
          shadowColor: Colors.red.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: Size(width ?? 0, height ?? buttonHeight),
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        );
        textColor = Colors.white;
        break;
    }

    Widget buttonChild;

    if (isLoading) {
      buttonChild = SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: iconSize),
          SizedBox(width: ResponsiveUtils.isMobile(context) ? 8 : 12),
          ResponsiveButtonText(text, color: textColor),
        ],
      );
    } else {
      buttonChild = ResponsiveButtonText(text, color: textColor);
    }

    // Choose button type based on style
    switch (type) {
      case ButtonType.primary:
      case ButtonType.danger:
        return SizedBox(
          width: width,
          height: height ?? buttonHeight,
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: buttonStyle,
            child: buttonChild,
          ),
        );

      case ButtonType.secondary:
        return SizedBox(
          width: width,
          height: height ?? buttonHeight,
          child: OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: buttonStyle,
            child: buttonChild,
          ),
        );

      case ButtonType.tertiary:
        return SizedBox(
          width: width,
          height: height ?? buttonHeight,
          child: TextButton(
            onPressed: isLoading ? null : onPressed,
            style: buttonStyle,
            child: buttonChild,
          ),
        );
    }
  }
}

/// Responsive floating action button
class ResponsiveFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final bool isExtended;
  final String? label;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ResponsiveFloatingActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.isExtended = false,
    this.label,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final iconSize = ResponsiveUtils.getResponsiveIconSize(context, 24);
    final elevation = ResponsiveUtils.getResponsiveElevation(context, 6);

    if (isExtended && label != null) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        icon: Icon(icon, size: iconSize),
        label: ResponsiveButtonText(
          label!,
          color: foregroundColor ?? Colors.white,
        ),
        tooltip: tooltip,
        backgroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
        foregroundColor: foregroundColor ?? Colors.white,
        elevation: elevation,
      );
    }

    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      backgroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      child: Icon(icon, size: iconSize),
    );
  }
}

/// Responsive icon button
class ResponsiveIconButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? color;
  final double? size;

  const ResponsiveIconButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final minTouchTarget = ResponsiveUtils.getMinTouchTargetSize();
    final iconSize = size ?? ResponsiveUtils.getResponsiveIconSize(context, 24);

    return SizedBox(
      width: minTouchTarget,
      height: minTouchTarget,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: iconSize),
        tooltip: tooltip,
        color: color,
        splashRadius: minTouchTarget / 2,
      ),
    );
  }
}

/// Button type enumeration
enum ButtonType { primary, secondary, tertiary, danger }

/// Button size enumeration
enum ButtonSize { small, medium, large }
