import 'dart:math' as math;
import '../models/food.dart';
import '../models/diet_plan.dart'; // Contains FoodPortion
import '../models/user_profile.dart';
import '../data/food_database.dart';
import 'food_variety_manager.dart';

/// Selettore di alimenti migliorato che massimizza la varietà
/// mantenendo la precisione nutrizionale
class EnhancedFoodSelector {
  final FoodDatabase _foodDatabase;
  final FoodVarietyManager _varietyManager;

  // Tolleranze per la precisione nutrizionale
  static const double _calorieTolerancePercent = 0.05; // 5% di tolleranza per le calorie
  static const int _calorieToleranceAbsolute = 20; // ±20 kcal di tolleranza assoluta
  static const int _macroToleranceGrams = 3; // ±3g di tolleranza per i macronutrienti

  EnhancedFoodSelector(this._foodDatabase, this._varietyManager);

  /// Factory method per creare un'istanza con dipendenze
  static Future<EnhancedFoodSelector> create() async {
    final foodDatabase = FoodDatabase();
    final varietyManager = await FoodVarietyManager.getInstance();
    return EnhancedFoodSelector(foodDatabase, varietyManager);
  }

  /// Seleziona alimenti per un pasto con massima varietà e precisione nutrizionale
  Future<List<FoodPortion>> selectFoodsForMeal({
    required UserProfile userProfile,
    required String mealType,
    required int targetCalories,
    required Map<String, int> targetMacros,
    DateTime? forDate,
  }) async {
    print('Selezione alimenti migliorata per pasto di tipo $mealType');
    print('Obiettivo calorico: $targetCalories kcal');
    print('Obiettivi macronutrienti: $targetMacros');

    // Ottieni tutti gli alimenti disponibili
    final allFoods = await _foodDatabase.getAllFoods();

    // Filtra gli alimenti adatti per questo utente e tipo di pasto
    final suitableFoods = await _filterSuitableFoods(allFoods, userProfile, mealType);

    if (suitableFoods.isEmpty) {
      print('Nessun alimento adatto trovato per il pasto di tipo $mealType');
      return [];
    }

    print('Alimenti adatti trovati: ${suitableFoods.length}');

    // Raggruppa gli alimenti per categoria
    final foodsByCategory = _groupFoodsByCategory(suitableFoods);

    // Determina le categorie necessarie per questo tipo di pasto
    final requiredCategories = _getCategoriesForMealType(mealType);

    // Seleziona alimenti con varietà e precisione nutrizionale
    return _selectFoodsWithVarietyAndPrecision(
      foodsByCategory,
      requiredCategories,
      targetCalories,
      targetMacros,
      mealType,
      forDate,
    );
  }

  /// Filtra gli alimenti adatti in base alle preferenze dell'utente e al tipo di pasto
  Future<List<Food>> _filterSuitableFoods(
    List<Food> allFoods,
    UserProfile userProfile,
    String mealType,
  ) async {
    // Converti il tipo di pasto in enum
    final mealTypeEnum = MealType.values.firstWhere(
      (e) => e.toString().split('.').last == mealType,
      orElse: () => MealType.lunch,
    );

    // Filtra per tipo di pasto
    var filteredFoods = allFoods.where((food) =>
      food.suitableForMeals.contains(mealTypeEnum)
    ).toList();

    // Filtra per tipo di dieta
    filteredFoods = filteredFoods.where((food) {
      switch (userProfile.dietType) {
        case DietType.vegetarian:
          return food.isVegetarian;
        case DietType.vegan:
          return food.isVegan;
        case DietType.pescatarian:
          return food.isVegetarian || food.categories.contains(FoodCategory.protein);
        case DietType.keto:
          return food.carbs <= 5;
        case DietType.paleo:
          return !food.categories.contains(FoodCategory.grain) &&
                 !food.categories.contains(FoodCategory.dairy);
        case DietType.omnivore:
        default:
          return true;
      }
    }).toList();

    // Filtra per allergie
    filteredFoods = filteredFoods.where((food) {
      for (var allergen in userProfile.allergies) {
        if (food.allergens.contains(allergen.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    // Filtra per cibi non graditi
    filteredFoods = filteredFoods.where((food) {
      for (var dislikedFood in userProfile.dislikedFoods) {
        if (food.name.toLowerCase().contains(dislikedFood.toLowerCase())) {
          return false;
        }
      }
      return true;
    }).toList();

    return filteredFoods;
  }

  /// Raggruppa gli alimenti per categoria
  Map<FoodCategory, List<Food>> _groupFoodsByCategory(List<Food> foods) {
    final Map<FoodCategory, List<Food>> grouped = {};

    for (final food in foods) {
      for (final category in food.categories) {
        if (!grouped.containsKey(category)) {
          grouped[category] = [];
        }
        grouped[category]!.add(food);
      }
    }

    return grouped;
  }

  /// Ottiene le categorie necessarie per un tipo di pasto
  List<FoodCategory> _getCategoriesForMealType(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return [
          FoodCategory.grain,
          FoodCategory.protein,
          FoodCategory.dairy,
          FoodCategory.fruit,
        ];
      case 'lunch':
      case 'dinner':
        return [
          FoodCategory.protein,
          FoodCategory.grain,
          FoodCategory.vegetable,
          FoodCategory.fat,
        ];
      case 'snack':
        return [
          FoodCategory.fruit,
          FoodCategory.protein,
          FoodCategory.dairy,
        ];
      default:
        return [
          FoodCategory.protein,
          FoodCategory.grain,
          FoodCategory.vegetable,
        ];
    }
  }

  /// Seleziona alimenti con varietà e precisione nutrizionale
  List<FoodPortion> _selectFoodsWithVarietyAndPrecision(
    Map<FoodCategory, List<Food>> foodsByCategory,
    List<FoodCategory> requiredCategories,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
    DateTime? forDate,
  ) {
    final selectedFoods = <FoodPortion>[];
    final usedFoodIds = <String>{};

    // Calorie e macronutrienti correnti
    int currentCalories = 0;
    int currentProteins = 0;
    int currentCarbs = 0;
    int currentFats = 0;

    // Target macronutrienti
    final targetProtein = targetMacros['proteins'] ?? 0;
    final targetCarbs = targetMacros['carbs'] ?? 0;
    final targetFats = targetMacros['fats'] ?? 0;

    // Seleziona alimenti da ciascuna categoria richiesta
    for (final category in requiredCategories) {
      if (!foodsByCategory.containsKey(category) || foodsByCategory[category]!.isEmpty) {
        continue;
      }

      // Filtra alimenti già utilizzati
      final availableFoods = foodsByCategory[category]!
          .where((food) => !usedFoodIds.contains(food.id))
          .toList();

      if (availableFoods.isEmpty) continue;

      // Usa il variety manager per selezionare alimenti vari
      final variedFoods = _varietyManager.selectVariedFoods(
        availableFoods,
        maxSelections: 3, // Considera i 3 migliori per varietà
        forDate: forDate,
        preferredCategories: [category],
      );

      // Seleziona il miglior alimento per i target nutrizionali rimanenti
      Food? bestFood;
      int bestPortion = 0;
      double bestScore = double.negativeInfinity;

      for (final food in variedFoods) {
        final portion = _calculateOptimalPortion(
          food,
          category,
          mealType,
          targetCalories - currentCalories,
          {
            'proteins': targetProtein - currentProteins,
            'carbs': targetCarbs - currentCarbs,
            'fats': targetFats - currentFats,
          },
        );

        if (portion >= 20) { // Porzione minima significativa
          final score = _calculateNutritionalScore(
            food,
            portion,
            targetCalories - currentCalories,
            targetProtein - currentProteins,
            targetCarbs - currentCarbs,
            targetFats - currentFats,
          );

          if (score > bestScore) {
            bestScore = score;
            bestFood = food;
            bestPortion = portion;
          }
        }
      }

      // Aggiungi il miglior alimento selezionato
      if (bestFood != null && bestPortion > 0) {
        final foodPortion = FoodPortion(food: bestFood, grams: bestPortion);
        selectedFoods.add(foodPortion);
        usedFoodIds.add(bestFood.id);

        // Aggiorna i valori nutrizionali correnti
        currentCalories += foodPortion.calories;
        currentProteins += foodPortion.proteins.round();
        currentCarbs += foodPortion.carbs.round();
        currentFats += foodPortion.fats.round();

        print('Aggiunto ${bestFood.name} ($bestPortion g): ${foodPortion.calories} kcal');
      }
    }

    // Registra l'utilizzo degli alimenti selezionati
    _varietyManager.recordMealUsage(
      selectedFoods.map((fp) => fp.food.id).toList(),
      date: forDate,
    );

    return selectedFoods;
  }

  /// Calcola la porzione ottimale per un alimento
  int _calculateOptimalPortion(
    Food food,
    FoodCategory category,
    String mealType,
    int remainingCalories,
    Map<String, int> remainingMacros,
  ) {
    // Porzioni standard per categoria
    final Map<FoodCategory, int> standardPortions = {
      FoodCategory.protein: 100,
      FoodCategory.grain: 80,
      FoodCategory.vegetable: 150,
      FoodCategory.fruit: 150,
      FoodCategory.dairy: 150,
      FoodCategory.fat: 15,
    };

    int basePortion = standardPortions[category] ?? food.servingSizeGrams;

    // Adatta per tipo di pasto
    if (mealType == 'lunch') {
      basePortion = (basePortion * 1.2).round();
    } else if (mealType == 'snack') {
      basePortion = (basePortion * 0.7).round();
    }

    // Calcola porzione basata sui macronutrienti rimanenti
    final portionCandidates = <int>[];

    if (remainingMacros['proteins']! > 0 && food.proteins > 0.1) {
      portionCandidates.add((remainingMacros['proteins']! * 100 / food.proteins).round());
    }
    if (remainingMacros['carbs']! > 0 && food.carbs > 0.1) {
      portionCandidates.add((remainingMacros['carbs']! * 100 / food.carbs).round());
    }
    if (remainingMacros['fats']! > 0 && food.fats > 0.1) {
      portionCandidates.add((remainingMacros['fats']! * 100 / food.fats).round());
    }
    if (remainingCalories > 0 && food.calories > 0) {
      portionCandidates.add((remainingCalories * 100 / food.calories).round());
    }

    portionCandidates.add(basePortion);

    // Seleziona la porzione più vicina a quella standard
    final finalPortion = portionCandidates.isNotEmpty
        ? portionCandidates.reduce((a, b) =>
            (a - basePortion).abs() < (b - basePortion).abs() ? a : b)
        : basePortion;

    return math.max(20, math.min(300, finalPortion));
  }

  /// Calcola un punteggio nutrizionale per valutare quanto bene un alimento si adatta ai target
  double _calculateNutritionalScore(
    Food food,
    int portion,
    int targetCalories,
    int targetProteins,
    int targetCarbs,
    int targetFats,
  ) {
    final foodPortion = FoodPortion(food: food, grams: portion);

    double score = 0;

    // Punteggio per calorie (più vicino al target = meglio)
    if (targetCalories > 0) {
      final calorieRatio = foodPortion.calories / targetCalories;
      score += math.max(0, 100 - (calorieRatio - 1).abs() * 100);
    }

    // Punteggio per proteine
    if (targetProteins > 0) {
      final proteinRatio = foodPortion.proteins / targetProteins;
      score += math.max(0, 100 - (proteinRatio - 1).abs() * 100);
    }

    // Punteggio per carboidrati
    if (targetCarbs > 0) {
      final carbRatio = foodPortion.carbs / targetCarbs;
      score += math.max(0, 100 - (carbRatio - 1).abs() * 100);
    }

    // Punteggio per grassi
    if (targetFats > 0) {
      final fatRatio = foodPortion.fats / targetFats;
      score += math.max(0, 100 - (fatRatio - 1).abs() * 100);
    }

    return score;
  }
}
