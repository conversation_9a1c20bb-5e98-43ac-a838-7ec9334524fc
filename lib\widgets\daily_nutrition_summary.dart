import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/diet_plan.dart';
import '../models/ultra_detailed_profile.dart';
import '../theme/app_theme.dart';

/// Widget per il riepilogo nutrizionale giornaliero con grafici dettagliati
class DailyNutritionSummary extends StatefulWidget {
  final DailyDietPlan dailyPlan;
  final UltraDetailedProfile profile;

  const DailyNutritionSummary({
    Key? key,
    required this.dailyPlan,
    required this.profile,
  }) : super(key: key);

  @override
  State<DailyNutritionSummary> createState() => _DailyNutritionSummaryState();
}

class _DailyNutritionSummaryState extends State<DailyNutritionSummary> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header con informazioni profilo
            _buildProfileHeader(),
            const SizedBox(height: 20),
            
            // Tabs per diverse visualizzazioni
            _buildTabBar(),
            const SizedBox(height: 20),
            
            // Contenuto tab selezionato
            _buildTabContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    final bmr = widget.profile.calculateBMR();
    final tdee = widget.profile.calculateTDEE();
    final validation = widget.profile.validateNutritionalSafety();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppTheme.primaryColor,
                child: Text(
                  widget.profile.baseProfile.name[0].toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.profile.baseProfile.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${widget.profile.baseProfile.age} anni • ${widget.profile.baseProfile.weight}kg • ${widget.profile.primaryGoal.toString().split('.').last}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    if (widget.profile.dietaryRegimen != null)
                      Text(
                        'Regime: ${widget.profile.dietaryRegimen.toString().split('.').last}',
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildMetricChip('BMR', '${bmr.round()} kcal', AppTheme.secondaryColor),
                  const SizedBox(height: 4),
                  _buildMetricChip('TDEE', '${tdee.round()} kcal', AppTheme.primaryColor),
                  const SizedBox(height: 4),
                  _buildValidationChip(validation),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationChip(Map<String, dynamic> validation) {
    final isValid = validation['isValid'] as bool;
    final color = isValid ? Colors.green : Colors.orange;
    final icon = isValid ? Icons.check_circle : Icons.warning;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            isValid ? 'Sicuro' : 'Attenzione',
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final tabs = [
      {'label': 'Riepilogo', 'icon': Icons.dashboard},
      {'label': 'Macronutrienti', 'icon': Icons.pie_chart},
      {'label': 'Micronutrienti', 'icon': Icons.bar_chart},
      {'label': 'Progressi', 'icon': Icons.trending_up},
    ];

    return Row(
      children: tabs.asMap().entries.map((entry) {
        final index = entry.key;
        final tab = entry.value;
        final isSelected = _selectedTabIndex == index;

        return Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedTabIndex = index;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected 
                  ? AppTheme.primaryColor 
                  : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected 
                    ? AppTheme.primaryColor 
                    : Colors.grey[300]!,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    tab['icon'] as IconData,
                    color: isSelected ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tab['label'] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[600],
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildSummaryTab();
      case 1:
        return _buildMacronutrientsTab();
      case 2:
        return _buildMicronutrientsTab();
      case 3:
        return _buildProgressTab();
      default:
        return _buildSummaryTab();
    }
  }

  Widget _buildSummaryTab() {
    final totalCalories = _calculateTotalCalories();
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();
    final totalFiber = _calculateTotalFiber();

    final targetCalories = widget.dailyPlan.calorieTarget;
    final calorieDeviation = ((totalCalories - targetCalories) / targetCalories * 100);

    return Column(
      children: [
        // Riepilogo calorie principale
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.accentColor.withOpacity(0.1),
                AppTheme.accentColor.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppTheme.accentColor.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              Text(
                'Calorie Giornaliere',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.accentColor,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '$totalCalories',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.accentColor,
                    ),
                  ),
                  Text(
                    ' / $targetCalories kcal',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    calorieDeviation.abs() <= 5 
                      ? Icons.check_circle 
                      : calorieDeviation.abs() <= 10 
                        ? Icons.warning 
                        : Icons.error,
                    color: calorieDeviation.abs() <= 5 
                      ? Colors.green 
                      : calorieDeviation.abs() <= 10 
                        ? Colors.orange 
                        : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${calorieDeviation >= 0 ? '+' : ''}${calorieDeviation.toStringAsFixed(1)}% dal target',
                    style: TextStyle(
                      fontSize: 12,
                      color: calorieDeviation.abs() <= 5 
                        ? Colors.green 
                        : calorieDeviation.abs() <= 10 
                          ? Colors.orange 
                          : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Griglia macronutrienti
        Row(
          children: [
            Expanded(
              child: _buildMacroCard('Proteine', totalProteins, widget.dailyPlan.proteinTarget.toDouble(), 'g', AppTheme.proteinColor),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMacroCard('Carboidrati', totalCarbs, widget.dailyPlan.carbTarget.toDouble(), 'g', AppTheme.carbColor),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMacroCard('Grassi', totalFats, widget.dailyPlan.fatTarget.toDouble(), 'g', AppTheme.fatColor),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Informazioni aggiuntive
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Fibre totali:',
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                  Text(
                    '${totalFiber.toStringAsFixed(1)}g',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Numero pasti:',
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                  Text(
                    '${widget.dailyPlan.meals.length}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Densità nutrizionale:',
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                  Text(
                    '${_calculateNutritionalDensity().toStringAsFixed(1)}/10',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMacroCard(String label, double actual, double target, String unit, Color color) {
    final percentage = target > 0 ? (actual / target * 100) : 0;
    final isGood = percentage >= 90 && percentage <= 110;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${actual.toStringAsFixed(1)}$unit',
            style: TextStyle(
              fontSize: 16,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '/ ${target.toStringAsFixed(0)}$unit',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isGood ? Icons.check_circle : Icons.warning,
                color: isGood ? Colors.green : Colors.orange,
                size: 12,
              ),
              const SizedBox(width: 2),
              Text(
                '${percentage.round()}%',
                style: TextStyle(
                  fontSize: 10,
                  color: isGood ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMacronutrientsTab() {
    return SizedBox(
      height: 300,
      child: Row(
        children: [
          // Grafico a torta
          Expanded(
            child: _buildMacronutrientPieChart(),
          ),
          const SizedBox(width: 20),
          // Dettagli e legenda
          Expanded(
            child: _buildMacronutrientDetails(),
          ),
        ],
      ),
    );
  }

  Widget _buildMacronutrientPieChart() {
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();
    
    final proteinCalories = totalProteins * 4;
    final carbCalories = totalCarbs * 4;
    final fatCalories = totalFats * 9;
    final totalCalories = proteinCalories + carbCalories + fatCalories;

    if (totalCalories == 0) {
      return const Center(
        child: Text('Nessun dato disponibile'),
      );
    }

    return Column(
      children: [
        Text(
          'Distribuzione Macronutrienti',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: PieChart(
            PieChartData(
              sections: [
                PieChartSectionData(
                  value: proteinCalories,
                  title: '${(proteinCalories / totalCalories * 100).round()}%',
                  color: AppTheme.proteinColor,
                  radius: 80,
                  titleStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                PieChartSectionData(
                  value: carbCalories,
                  title: '${(carbCalories / totalCalories * 100).round()}%',
                  color: AppTheme.carbColor,
                  radius: 80,
                  titleStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                PieChartSectionData(
                  value: fatCalories,
                  title: '${(fatCalories / totalCalories * 100).round()}%',
                  color: AppTheme.fatColor,
                  radius: 80,
                  titleStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
              sectionsSpace: 3,
              centerSpaceRadius: 30,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMacronutrientDetails() {
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dettagli Macronutrienti',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildMacroDetail('Proteine', totalProteins, widget.dailyPlan.proteinTarget.toDouble(), AppTheme.proteinColor),
        const SizedBox(height: 12),
        _buildMacroDetail('Carboidrati', totalCarbs, widget.dailyPlan.carbTarget.toDouble(), AppTheme.carbColor),
        const SizedBox(height: 12),
        _buildMacroDetail('Grassi', totalFats, widget.dailyPlan.fatTarget.toDouble(), AppTheme.fatColor),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Raccomandazioni:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '• Proteine: 1.6-2.2g/kg peso corporeo\n• Grassi: 20-35% delle calorie totali\n• Carboidrati: per differenza',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMacroDetail(String label, double actual, double target, Color color) {
    final percentage = target > 0 ? (actual / target * 100) : 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${actual.toStringAsFixed(1)}g / ${target.toStringAsFixed(0)}g',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${percentage.round()}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: percentage >= 90 && percentage <= 110 ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: (percentage / 100).clamp(0.0, 1.0),
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildMicronutrientsTab() {
    return const Center(
      child: Text('Micronutrienti - In sviluppo'),
    );
  }

  Widget _buildProgressTab() {
    return const Center(
      child: Text('Progressi - In sviluppo'),
    );
  }

  // Metodi di calcolo
  int _calculateTotalCalories() {
    return widget.dailyPlan.meals.fold(0, (sum, meal) => 
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));
  }

  double _calculateTotalProteins() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) => 
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.proteins));
  }

  double _calculateTotalCarbs() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) => 
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.carbs));
  }

  double _calculateTotalFats() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) => 
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.fats));
  }

  double _calculateTotalFiber() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) => 
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.fiber));
  }

  double _calculateNutritionalDensity() {
    final totalCalories = _calculateTotalCalories();
    final totalProteins = _calculateTotalProteins();
    final totalFiber = _calculateTotalFiber();
    
    if (totalCalories == 0) return 0;
    
    final proteinDensity = (totalProteins / totalCalories) * 100;
    final fiberDensity = (totalFiber / totalCalories) * 100;
    
    return ((proteinDensity + fiberDensity) * 2).clamp(0, 10);
  }
}
